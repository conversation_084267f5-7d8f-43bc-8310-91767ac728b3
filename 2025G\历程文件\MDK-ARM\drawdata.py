# -*- coding: utf-8 -*-
import matplotlib.pyplot as plt
import numpy as np

def read_data_log(filename):
    """Read data from Data.log file"""
    data = []
    try:
        with open(filename, 'r') as file:
            for line in file:
                line = line.strip()
                if line:  # Skip empty lines
                    try:
                        value = float(line)
                        data.append(value)
                    except ValueError:
                        print("Warning: Cannot parse data line: " + line)
        return data
    except IOError:
        print("Error: File not found " + filename)
        return []
    except Exception as e:
        print("Error: Exception occurred while reading file: " + str(e))
        return []

def plot_data(data):
    """Plot data as line chart"""
    if not data:
        print("No data to plot")
        return
    
    # Set Chinese font for matplotlib
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # Create figure and axis
    plt.figure(figsize=(12, 8))
    
    # Generate x-axis data (sample point indices)
    x = np.arange(len(data))
    
    # Plot line chart
    plt.plot(x, data, linewidth=1.5, color='blue', marker='o', markersize=2)
    
    # Set figure properties
    plt.title('Data.log Line Chart', fontsize=16, fontweight='bold')
    plt.xlabel('Sample Points', fontsize=12)
    plt.ylabel('Values', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # Add statistics
    mean_value = np.mean(data)
    max_value = np.max(data)
    min_value = np.min(data)
    
    plt.axhline(y=mean_value, color='red', linestyle='--', alpha=0.7, 
                label='Mean: {:.4f}'.format(mean_value))
    
    # Set legend
    plt.legend()
    
    # Add text box with statistics
    stats_text = 'Max: {:.4f}\nMin: {:.4f}\nMean: {:.4f}\nSamples: {}'.format(
        max_value, min_value, mean_value, len(data))
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Adjust layout
    plt.tight_layout()
    
    # Show figure
    plt.show()

def main():
    """Main function"""
    # Read data
    print("Reading Data.log file...")
    data = read_data_log('data.log')
    
    if data:
        print("Successfully read {} data points".format(len(data)))
        print("Data range: {:.4f} ~ {:.4f}".format(min(data), max(data)))
        print("Generating line chart...")
        
        # Plot figure
        plot_data(data)
    else:
        print("Cannot read data, please check if Data.log file exists and format is correct")

if __name__ == "__main__":
    main()