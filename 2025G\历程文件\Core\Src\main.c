/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dac.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"
#include "fsmc.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "lcd.h"
#include "ad9959.h"
#include "stdio.h"
#include <stdarg.h> // for va_list
#include <stdio.h>  // for vsnprintf
#include "arm_math.h"
#include <stdlib.h> // ????????? qsort ??
#include <string.h> // ????????? memcpy ??
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
/* --- NEW --- */
// Enum to hold the application's current state
typedef enum {
    STATE_IDLE,
    STATE_SWEEPING,
    STATE_ANALYZING,
    STATE_DISPLAY_RESULTS,
	STATE_SIMULATING        // ??:??????
} AppState;

// Enum for the determined filter type 
typedef enum {
    FILTER_TYPE_UNKNOWN,
    FILTER_TYPE_LOW_PASS,
    FILTER_TYPE_HIGH_PASS,
    FILTER_TYPE_BAND_PASS,
    FILTER_TYPE_BAND_STOP
} FilterType;
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// 建议在文件顶部定义此常量
#define TIMER5_TICK_FREQ_HZ 84000000.0f
// 每次处理的采样点数 (来自您的宏定义)
#define NUM_SAMPLES ADC_SAMPLES_PER_HALF_BUFFER

// ADC参考电压 (Volts)。例如：3.3V
// !!! 重要：请修改为您的ADC的实际参考电压 !!!
#define ADC_VREF_V 3.3f
// PI 的定义，如果您的 math.h 没有定义 M_PI
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif
// ADC分辨率 (位数)。例如：12位ADC为 2^12 = 4096
// !!! 重要：请修改为您的ADC的实际分辨率 !!!
#define ADC_RESOLUTION 4096


#define ADC_SAMPLES_PER_HALF_BUFFER 3000
#define ADC_TOTAL_BUFFER_SIZE       (ADC_SAMPLES_PER_HALF_BUFFER * 2)
#define FFT_SIZE 1024
// --- ???? ---
#define SWEEP_START_FREQ_HZ 200
#define SWEEP_END_FREQ_HZ   500000
#define SWEEP_STEP_FREQ_HZ  200
#define NUM_SWEEP_STEPS     ((SWEEP_END_FREQ_HZ - SWEEP_START_FREQ_HZ) / SWEEP_STEP_FREQ_HZ + 1)
#define SETTLE_TIME_MS      20
// ??????????
#define FLAT_TOP_COHERENT_GAIN (0.21557895f)
#define HANNING_COHERENT_GAIN  (0.5f)

#define RELAY_STATE_LEARN    GPIO_PIN_RESET // ?????????ADC2???
#define RELAY_STATE_SIMULATE GPIO_PIN_SET   // ??????ADC2???
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

// --- ??: ????????DMA??? ---
volatile uint16_t g_adc1_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
volatile uint16_t g_adc2_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
volatile uint16_t g_dac_output_buffer[ADC_TOTAL_BUFFER_SIZE]; 
// --- ??: ?????????????? ---
uint16_t g_adc1_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
uint16_t g_adc2_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];

// ??????
volatile uint8_t g_adc1_data_ready = 0;
volatile uint8_t g_adc2_data_ready = 0;

#define NUM_PERIODS_TO_AVERAGE 50 // ����Ҫƽ���������������Ը�����Ҫ����
volatile float g_current_sampling_simulation = 1000000.0f; 
volatile uint32_t g_tim5_capture_val1 = 0;
volatile uint32_t g_tim5_capture_val2 = 0;
volatile uint8_t  g_tim5_capture_state = 0;   // ״̬��, 0:�ȴ���һ������, 1:���ڼ���
volatile uint32_t g_capture_count = 0;      // �Ѳ���ı��ؼ���
volatile float    g_measured_freq = 0.0f;
volatile float g_current_signal_rate=50000.0;
volatile uint8_t stop_flag = 0;
volatile AppState g_app_state = STATE_IDLE;
FilterType g_filter_type = FILTER_TYPE_UNKNOWN;

// ???????
float g_H_magnitude[NUM_SWEEP_STEPS]; // �洢H(w)�ķ���
float g_H_phase[NUM_SWEEP_STEPS];     // �洢H(w)����λ
uint32_t g_sweep_current_step = 0;
uint32_t g_current_freq_sweep = SWEEP_START_FREQ_HZ;
float sampling_frequency_sweep;
arm_rfft_fast_instance_f32 g_fft_instance;
float g_fft_input_buffer[FFT_SIZE];
float g_fft_output_buffer[FFT_SIZE];
// ????????????
float g_flat_top_window[FFT_SIZE];
float g_hanning_window[FFT_SIZE];
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void Start_Sweep_And_Analysis(void);
// --- ??: ?????????????????? ---
float Get_Signal_Amplitude(const uint16_t* processing_buffer);
void Analyze_Filter_Response(void);
void Display_Results(void);
HAL_StatusTypeDef Safely_Update_Sampling_Frequency(uint32_t new_sampling_freq_hz);
void LCD_ShowString_Simplified(uint16_t x, uint16_t y, const char *p, uint16_t color);
void Process_Buffer_FFT_Convolution(const uint16_t* adc_src, volatile uint16_t* dac_dest, uint32_t count);
void Get_Signal_Accurate_Amplitude_And_Phase(const uint16_t* processing_buffer,
                                             float target_frequency_hz,
                                             float current_sampling_freq_hz,
                                             float* p_amplitude_v,
                                             float* p_phase_rad);
void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                           volatile uint16_t* dac_dest,
                           float signal_base_freq_hz,
                           float sampling_freq_hz);																					 
void Start_Simulation_Mode(void);
void Stop_Simulation_Mode(void);
void Process_And_Fill_DAC_Buffer(const uint16_t* adc_src,volatile uint16_t* dac_dest, uint32_t count); 
static int partition_uint16(uint16_t arr[], int low, int high);
void Generate_Flat_Top_Window(void);
void Generate_Hanning_Window(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_FSMC_Init();
  MX_ADC1_Init();
  MX_ADC2_Init();
  MX_DAC_Init();
  MX_TIM5_Init();
  /* USER CODE BEGIN 2 */
  lcd_init();
	Init_AD9959(); 
	if (arm_rfft_fast_init_f32(&g_fft_instance, FFT_SIZE) != ARM_MATH_SUCCESS) {
      Error_Handler();
  }
  lcd_clear(WHITE); // ???????????
  LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
  LCD_ShowString_Simplified(10, 30, "Press Learn Key.", BLACK);
	Write_frequence(0,1000);
	Write_Amplitude(3, 0);
	Write_frequence(3,100000);
	//Write_Amplitude(3, 1000);
  Generate_Flat_Top_Window();
	Generate_Hanning_Window();
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    switch (g_app_state) {
        case STATE_IDLE:
            if (HAL_GPIO_ReadPin(LEARN_KEY_GPIO_Port, LEARN_KEY_Pin) == GPIO_PIN_SET) {
                HAL_Delay(20);
                if (HAL_GPIO_ReadPin(LEARN_KEY_GPIO_Port, LEARN_KEY_Pin) == GPIO_PIN_SET) {
                    Start_Sweep_And_Analysis();
                }
            }
						if (HAL_GPIO_ReadPin(SIM_KEY_GPIO_Port, SIM_KEY_Pin) == GPIO_PIN_SET) {
                HAL_Delay(20);
                if (HAL_GPIO_ReadPin(SIM_KEY_GPIO_Port, SIM_KEY_Pin) == GPIO_PIN_SET) {
                    Start_Simulation_Mode();
                }
            }
            break;

        case STATE_SWEEPING:
            // ????ADC??????????????
            if (g_adc1_data_ready && g_adc2_data_ready) {
                g_adc1_data_ready = 0;
                g_adc2_data_ready = 0;

                float input_amp = 0.0f, input_phase = 0.0f;
                float output_amp = 0.0f, output_phase = 0.0f;

                Get_Signal_Accurate_Amplitude_And_Phase(g_adc1_processing_buffer,g_current_freq_sweep, sampling_frequency_sweep,&input_amp, &input_phase);
                Get_Signal_Accurate_Amplitude_And_Phase(g_adc2_processing_buffer,g_current_freq_sweep, sampling_frequency_sweep,&output_amp, &output_phase);

                // ���㲢�洢 H(w)
                if (output_amp / input_amp <10.0f&& output_amp / input_amp>0.0f) { // ���������
                    g_H_magnitude[g_sweep_current_step] = output_amp / input_amp;
                } else {
                    g_H_magnitude[g_sweep_current_step] = 0.0f;
                }
								if(input_amp==0.0f){
									stop_flag=1;
								}
                // 计算原始的相位差
               float phase_diff = output_phase - input_phase;

               // 将相位差归一化到 [-PI, PI] 区间
               while (phase_diff > M_PI) {
                  phase_diff -= 2.0f * M_PI;
               }
               while (phase_diff <= -M_PI) {
                  phase_diff += 2.0f * M_PI;
               }

                // 存储归一化后的结果
                g_H_phase[g_sweep_current_step] = phase_diff;
                g_sweep_current_step++;
                if (g_sweep_current_step < NUM_SWEEP_STEPS) {
					          g_current_freq_sweep += SWEEP_STEP_FREQ_HZ;
									  Write_Amplitude(3, 0);
                    HAL_ADC_Stop_DMA(&hadc1);
                    HAL_ADC_Stop_DMA(&hadc2);
					          Write_frequence(0, g_current_freq_sweep);
									 if(g_current_freq_sweep<50000){
                     sampling_frequency_sweep=g_current_freq_sweep*20;
					           }
									 else if(g_current_freq_sweep<200000){
                     sampling_frequency_sweep=g_current_freq_sweep*5;
					           }
									 else if(g_current_freq_sweep<466666){
                     sampling_frequency_sweep=g_current_freq_sweep*3;
					           }
					         else{
						         sampling_frequency_sweep=g_current_freq_sweep*2;
					           }
									  if (HAL_ADC_Start_DMA(&hadc1, (uint32_t*)g_adc1_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
                      return HAL_ERROR;
                     }
                   if (HAL_ADC_Start_DMA(&hadc2, (uint32_t*)g_adc2_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
                      return HAL_ERROR;
                     }
									  Write_frequence(3, sampling_frequency_sweep);
										Write_Amplitude(3, 1000);
                    HAL_Delay(SETTLE_TIME_MS); // ??????????
                } else {
									 Write_Amplitude(3, 0);
                   HAL_ADC_Stop_DMA(&hadc1);
                   HAL_ADC_Stop_DMA(&hadc2);
                   g_app_state = STATE_ANALYZING;
                }
            }
            break;

        case STATE_ANALYZING:
            Analyze_Filter_Response();
            g_app_state = STATE_DISPLAY_RESULTS;
				    
            break;

        case STATE_DISPLAY_RESULTS:
            Display_Results(); // ��ʾһ�ν��
            // �ȴ��������ؿ���״̬
            while(g_app_state == STATE_DISPLAY_RESULTS) {
                if (HAL_GPIO_ReadPin(IDLE_KEY_GPIO_Port, IDLE_KEY_Pin) == GPIO_PIN_SET) {
                    HAL_Delay(20);
                    if (HAL_GPIO_ReadPin(IDLE_KEY_GPIO_Port, IDLE_KEY_Pin) == GPIO_PIN_SET) {
                        lcd_clear(WHITE);
                        LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
                        LCD_ShowString_Simplified(10, 30, "Press Learn Key.", BLACK);
                        g_app_state = STATE_IDLE;
                    }
                }
            }
            break;		

		/* --- ??:?????????? --- */
        case STATE_SIMULATING:
			//�ǵô�TIM5���벶��
            if (HAL_GPIO_ReadPin(IDLE_KEY_GPIO_Port, IDLE_KEY_Pin) == GPIO_PIN_SET) {
                HAL_Delay(20); // ??
                if (HAL_GPIO_ReadPin(IDLE_KEY_GPIO_Port, IDLE_KEY_Pin) == GPIO_PIN_SET) {
                    Stop_Simulation_Mode();
                }
            }
            break;
    }
	}
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/**
 * @brief  ????????????????????
 */
void Analyze_Filter_Response(void) {
    float max_gain = 0.0f;
    int max_gain_index = 0;

    for (int i = 0; i < NUM_SWEEP_STEPS; i++) {
        if (g_H_magnitude[i] > max_gain) {
            max_gain = g_H_magnitude[i];
            max_gain_index = i;
        }
    }

    if (max_gain < 0.01) {
        g_filter_type = FILTER_TYPE_UNKNOWN;
        return;
    }

    float cutoff_gain = max_gain * 0.707f;
    float start_gain = g_H_magnitude[0];
    float end_gain = g_H_magnitude[NUM_SWEEP_STEPS - 1];

    if (max_gain_index < NUM_SWEEP_STEPS * 0.1 && end_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_LOW_PASS;
    } else if (max_gain_index > NUM_SWEEP_STEPS * 0.9 && start_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_HIGH_PASS;
    } else if (start_gain < cutoff_gain && end_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_BAND_PASS;
    } else if (start_gain > cutoff_gain && end_gain > cutoff_gain) {
        g_filter_type = FILTER_TYPE_BAND_STOP;
    } else {
        g_filter_type = FILTER_TYPE_UNKNOWN;
    }
}
/**
 * @brief ???????
 * @note  ?????5????,??????0.21558
 */
void Generate_Flat_Top_Window(void) {
    // Standard 5-term flat-top window coefficients
    const float a0 = 0.21557895f;
    const float a1 = 0.41663158f;
    const float a2 = 0.277263158f;
    const float a3 = 0.083578947f;
    const float a4 = 0.006947368f;
    for (int i = 0; i < FFT_SIZE; i++) {
        g_flat_top_window[i] = a0 - a1 * cosf(2 * PI * i / (FFT_SIZE - 1)) +
                               a2 * cosf(4 * PI * i / (FFT_SIZE - 1)) -
                               a3 * cosf(6 * PI * i / (FFT_SIZE - 1)) +
                               a4 * cosf(8 * PI * i / (FFT_SIZE - 1));
    }
}

/**
 * @brief ???????
 * @note  ?????0.5
 */
void Generate_Hanning_Window(void) {
    for (int i = 0; i < FFT_SIZE; i++) {
        g_hanning_window[i] = 0.5f * (1.0f - cosf(2.0f * PI * i / (FFT_SIZE - 1)));
    }
}



/**
 * @brief Stops the real-time filter simulation.
 */
void Stop_Simulation_Mode(void) {

    
    // 2. Stop the DAC and ADC DMAs
    HAL_DAC_Stop_DMA(&hdac, DAC_CHANNEL_1);
    HAL_ADC_Stop_DMA(&hadc2);

    // 3. Update state and display
    g_app_state = STATE_IDLE;
    lcd_clear(WHITE);
    LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
    LCD_ShowString_Simplified(10, 30, "Press Learn/Sim Key", BLACK);
}
/**
 * @brief Starts the real-time filter simulation.
 */
void Start_Simulation_Mode(void) {
    // Only start if a filter has been learned successfully
    if (g_filter_type == FILTER_TYPE_UNKNOWN) {
        lcd_clear(RED);
        LCD_ShowString_Simplified(10, 10, "Error!", WHITE);
        LCD_ShowString_Simplified(10, 30, "Learn a filter first.", WHITE);
        HAL_Delay(2000);
        g_app_state = STATE_IDLE; // Go back to idle
        lcd_clear(WHITE);
        LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
        LCD_ShowString_Simplified(10, 30, "Press Learn/Sim Key", BLACK);
        return;
    }
    HAL_GPIO_WritePin(RELAY_GPIO_Port, RELAY_Pin, RELAY_STATE_SIMULATE);
    HAL_Delay(20);
		
    lcd_clear(BLUE);
    LCD_ShowString_Simplified(10, 10, "Simulation Active", WHITE);
    LCD_ShowString_Simplified(10, 30, "Press Sim Key to Stop", WHITE);
    HAL_TIM_IC_Start_IT(&htim5, TIM_CHANNEL_1);
    // 3. ??DAC DMA
    if (HAL_DAC_Start_DMA(&hdac, DAC_CHANNEL_1, (uint32_t*)g_dac_output_buffer, ADC_TOTAL_BUFFER_SIZE, DAC_ALIGN_12B_R) != HAL_OK) {
        Error_Handler();
    }
		if (HAL_ADC_Start_DMA(&hadc2, (uint32_t*)g_adc2_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
        Error_Handler();
     }
    Write_frequence(3, g_current_sampling_simulation); 
    g_app_state = STATE_SIMULATING;
}

/**
 * @brief  ?????????
 */
void Start_Sweep_And_Analysis(void) {
	  g_sweep_current_step = 0;
    g_current_freq_sweep = SWEEP_START_FREQ_HZ;
    g_filter_type = FILTER_TYPE_UNKNOWN;
		HAL_GPIO_WritePin(RELAY_GPIO_Port, RELAY_Pin, RELAY_STATE_LEARN);
	  if (HAL_ADC_Start_DMA(&hadc1, (uint32_t*)g_adc1_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
       Error_Handler();
     }
    if (HAL_ADC_Start_DMA(&hadc2, (uint32_t*)g_adc2_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
       Error_Handler();
     }
	  sampling_frequency_sweep=g_current_freq_sweep*20;
	  Write_frequence(0, g_current_freq_sweep);
	  Write_frequence(3, sampling_frequency_sweep);
		Write_Amplitude(3, 1000);
	  HAL_Delay(SETTLE_TIME_MS); 
    lcd_clear(WHITE);
    LCD_ShowString_Simplified(10, 10, "Learning...", BLACK); 
    g_adc1_data_ready = 0;
    g_adc2_data_ready = 0;
    
    g_app_state = STATE_SWEEPING;
}

/*
 * @brief  [??] ???????????
 * @note   ???????????lcd_show_string,??????????????
 * @param  x, y:  ????
 * @param  p:     ???????
 * @param  color: ??
 */
void LCD_ShowString_Simplified(uint16_t x, uint16_t y, const char *p, uint16_t color)
{
    // ???????????,??16
    uint8_t font_size = 16;
    // ?????????,???????????240,?????????,?????????
    lcd_show_string(x, y, 240, font_size, font_size, (char*)p, color);
}

/**
 * @brief  Calculates the precise amplitude and phase of a single sine wave using a single-frequency DFT.
 *
 * @param  processing_buffer      Pointer to the buffer containing ADC sample values (uint16_t).
 * @param  target_frequency_hz    The frequency of the target signal (Hz).
 * @param  current_sampling_freq_hz The actual sampling frequency used to acquire this buffer (Hz).
 * @param  p_amplitude_v          Pointer to a float to store the calculated amplitude (in Volts).
 * @param  p_phase_rad            Pointer to a float to store the calculated phase (in radians).
 */
void Get_Signal_Accurate_Amplitude_And_Phase(const uint16_t* processing_buffer,
                                             float target_frequency_hz,
                                             float current_sampling_freq_hz,
                                             float* p_amplitude_v,
                                             float* p_phase_rad)
{
    // --------------------------------------------------------------------------
    // 1. Preparation: Calculate parameters needed for DFT
    // --------------------------------------------------------------------------

    // DFT bin formula: f_k = k * fs / N  =>  k = f_k * N / fs
    // Use the passed-in current sampling frequency for an accurate calculation.
    const float k = target_frequency_hz * (float)NUM_SAMPLES / current_sampling_freq_hz;

    // Calculate angular frequency w = 2 * PI * k / N
    const float w = 2.0f * M_PI * k / (float)NUM_SAMPLES;

    float real_dc_offset = 0.0f;
    for (int i = 0; i < NUM_SAMPLES; i++) {
        real_dc_offset += (float)processing_buffer[i];
    }
    real_dc_offset /= (float)NUM_SAMPLES;

    // Initialize the sum for the real and imaginary parts of the DFT
    float sum_real = 0.0f;
    float sum_imag = 0.0f;

    // --------------------------------------------------------------------------
    // 2. Core Calculation: Execute the single-frequency DFT
    // --------------------------------------------------------------------------

    for (int n = 0; n < NUM_SAMPLES; n++)
    {
        float sample_no_dc = (float)processing_buffer[n] - real_dc_offset;
        float angle = w * (float)n;
        sum_real += sample_no_dc * cosf(angle);
        sum_imag += sample_no_dc * sinf(angle);
    }

    // --------------------------------------------------------------------------
    // 3. Post-processing: Calculate amplitude and phase from DFT results
    // --------------------------------------------------------------------------
    float dft_real = sum_real;
    float dft_imag = -sum_imag;

    // Calculate amplitude
    float amplitude_adc_units = (2.0f / (float)NUM_SAMPLES) * sqrtf(dft_real * dft_real + dft_imag * dft_imag);
    *p_amplitude_v = amplitude_adc_units * (ADC_VREF_V / (float)ADC_RESOLUTION);

    // Calculate phase
    *p_phase_rad = atan2f(dft_imag, dft_real);
}
/**
 * @brief  ?LCD??????????
 */
void Display_Results(void) {
    lcd_clear(WHITE);
    LCD_ShowString_Simplified(10, 10, "Analysis Complete!", BLACK);
    
    char* type_str = "Type: Unknown";
    switch (g_filter_type) {
        case FILTER_TYPE_LOW_PASS:  type_str = "Type: Low-Pass";  break;
        case FILTER_TYPE_HIGH_PASS: type_str = "Type: High-Pass"; break;
        case FILTER_TYPE_BAND_PASS: type_str = "Type: Band-Pass"; break;
        case FILTER_TYPE_BAND_STOP: type_str = "Type: Band-Stop"; break;
        default: break;
    }
    LCD_ShowString_Simplified(10, 30, type_str, BLACK);

}

/**
  * @brief  ADC DMA ����һ����ɻص�������
  */
void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        if(g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[0], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    }
    else if (hadc->Instance == ADC2) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[0], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        }
        else if (g_app_state == STATE_SIMULATING) {
            // --- ���޸�: �����µĴ����� ---
            Process_Buffer_DFT_SIM(
                (const uint16_t*)&g_adc2_dma_buffer[0],
                &g_dac_output_buffer[0],
								g_current_signal_rate,         
                g_current_sampling_simulation
            );
        }
    }
}

/**
  * @brief  ADC DMA ������ɻص�������
  */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        if(g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    }
    else if (hadc->Instance == ADC2) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        }
        else if (g_app_state == STATE_SIMULATING) {
           Process_Buffer_DFT_SIM(
                (const uint16_t*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                &g_dac_output_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                g_current_signal_rate,
                g_current_sampling_simulation
            );
        }
    }
}
/**
  * @brief  在仿真模式下，使用DFT处理ADC数据并生成DAC输出。
  * @note   此函数计算输入信号的基波及最多10次谐波的幅度和相位。
  * 然后，它会查找在学习模式下测得的H(w)值，对每个谐波分量进行滤波。
  * 最后，通过反向DFT（合成）重建时域信号并写入DAC缓冲区。
  * @param  adc_src             指向源ADC数据缓冲区的指针 (大小为 ADC_SAMPLES_PER_HALF_BUFFER)。
  * @param  dac_dest            指向目标DAC数据缓冲区的指针 (大小为 ADC_SAMPLES_PER_HALF_BUFFER)。
  * @param  signal_base_freq_hz 输入信号的基频 (Hz)。
  * @param  sampling_freq_hz    当前的采样频率 (Hz)。
  */
void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                           volatile uint16_t* dac_dest,
                           float signal_base_freq_hz,
                           float sampling_freq_hz)
{
    // --- 常量与缓冲区定义 ---
    const int NUM_HARMONICS = 10; // 处理到10次谐波
    const int NUM_COMPONENTS = NUM_HARMONICS + 1; // 基波 + 10次谐波
    const int BUFFER_SIZE = ADC_SAMPLES_PER_HALF_BUFFER;

    // 存储每个谐波分量的幅度和相位
    float mag_X_V[NUM_COMPONENTS];      // 输入信号 X(w) 的幅度 (伏特)
    float phase_X_rad[NUM_COMPONENTS];  // 输入信号 X(w) 的相位 (弧度)
    float mag_Y_V[NUM_COMPONENTS];      // 输出信号 Y(w) 的幅度 (伏特)
    float phase_Y_rad[NUM_COMPONENTS];  // 输出信号 Y(w) 的相位 (弧度)

    // --- 步骤 1: 计算输入信号的直流偏置 ---
    float dc_offset_adc = 0.0f;
    for (int i = 0; i < BUFFER_SIZE; i++) {
        dc_offset_adc += (float)adc_src[i];
    }
    dc_offset_adc /= (float)BUFFER_SIZE;

    // --- 步骤 2: 对每个谐波分量进行前向DFT, 并应用滤波器H(w) ---
    for (int h = 0; h < NUM_COMPONENTS; h++) {
        // 当前处理的频率 (h=0是基波, h=1是2次谐波, ...)
        float current_freq_hz = signal_base_freq_hz * (float)(h + 1);

        // --- 2a. 对当前频率执行单频DFT ---
        const float k = current_freq_hz * (float)BUFFER_SIZE / sampling_freq_hz;
        const float w = 2.0f * M_PI * k / (float)BUFFER_SIZE;
        float sum_real = 0.0f;
        float sum_imag = 0.0f;

        for (int n = 0; n < BUFFER_SIZE; n++) {
            float sample_no_dc = (float)adc_src[n] - dc_offset_adc;
            float angle = w * (float)n;
            sum_real += sample_no_dc * cosf(angle);
            sum_imag += sample_no_dc * sinf(angle);
        }

        float dft_real = sum_real;
        float dft_imag = -sum_imag; // 根据DFT定义调整符号

        // 计算输入信号X(w)的幅度和相位
        float amplitude_adc_units = (2.0f / (float)BUFFER_SIZE) * sqrtf(dft_real * dft_real + dft_imag * dft_imag);
        mag_X_V[h] = amplitude_adc_units * (ADC_VREF_V / (float)ADC_RESOLUTION);
        phase_X_rad[h] = atan2f(dft_imag, dft_real);

        // --- 2b. 查找对应的滤波器响应H(w) ---
        float mag_H = 0.0f;   // 默认增益为0
        float phase_H = 0.0f; // 默认相移为0

        // 检查频率是否在扫描范围内
        if (current_freq_hz >= SWEEP_START_FREQ_HZ && current_freq_hz <= SWEEP_END_FREQ_HZ) {
            // 计算索引，找到最接近的频率点
            int h_idx = roundf((current_freq_hz - SWEEP_START_FREQ_HZ) / SWEEP_STEP_FREQ_HZ);
            if (h_idx >= 0 && h_idx < NUM_SWEEP_STEPS) {
                mag_H = g_H_magnitude[h_idx];
                phase_H = g_H_phase[h_idx];
            }
        }
        // 如果频率超出范围, mag_H将保持为0, 从而滤除该谐波

        // --- 2c. 计算输出 Y(w) = H(w) * X(w) ---
        mag_Y_V[h] = mag_X_V[h] * mag_H;
        phase_Y_rad[h] = phase_X_rad[h] + phase_H;
    }

    // --- 步骤 3: 反向DFT (信号重建) ---
    const float dc_out_dac = (float)ADC_RESOLUTION / 2.0f; // DAC输出的直流偏置设为中间值

    for (int n = 0; n < BUFFER_SIZE; n++) {
        float sample_value_dac = dc_out_dac; // 从直流偏置开始

        // 叠加所有谐波分量来重建信号
        for (int h = 0; h < NUM_COMPONENTS; h++) {
            float current_freq_hz = signal_base_freq_hz * (float)(h + 1);
            // 将输出幅度从伏特转换回DAC单位
            float amp_Y_dac = mag_Y_V[h] * ((float)ADC_RESOLUTION / ADC_VREF_V);

            sample_value_dac += amp_Y_dac * cosf(2.0f * M_PI * current_freq_hz * (float)n / sampling_freq_hz + phase_Y_rad[h]);
        }

        // --- 步骤 4: 限幅并存入DAC缓冲区 ---
        if (sample_value_dac < 0.0f) {
            sample_value_dac = 0.0f;
        } else if (sample_value_dac > (ADC_RESOLUTION - 1.0f)) {
            sample_value_dac = (ADC_RESOLUTION - 1.0f);
        }
        dac_dest[n] = (uint16_t)sample_value_dac;
    }
}




/**
 * @brief  输入捕获回调函数，用于多周期频率测量
 */
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
{
    // 确保是TIM5的中断
    if (htim->Instance == TIM5 && htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1)
    {
        if (g_tim5_capture_state == 0) // 等待新一轮测量的第一个边沿
        {
            // 读取并存储开始时间
            g_tim5_capture_val1 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
            g_capture_count = 0;
            g_tim5_capture_state = 1; // 切换到计数状态
        }
        else if (g_tim5_capture_state == 1) // 处于计数状态
        {
            g_capture_count++; // 边沿计数加一

            // 检查是否已捕获足够的周期
            if (g_capture_count >= NUM_PERIODS_TO_AVERAGE)
            {
                // 读取结束时间
                g_tim5_capture_val2 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
                
                uint32_t total_ticks;
                // 计算N个周期的总计数值
                if (g_tim5_capture_val2 > g_tim5_capture_val1)
                {
                    total_ticks = g_tim5_capture_val2 - g_tim5_capture_val1;
                }
                else // 定时器已溢出
                {
                    // 已修正: 使用32位的值 (0xFFFFFFFF) 进行溢出计算
                    total_ticks = (0xFFFFFFFF - g_tim5_capture_val1) + g_tim5_capture_val2 + 1;
                }
                
                // 计算频率
                if (total_ticks > 0)
                {
                    // 已修正: 使用实际的定时器频率 (84 MHz)
                    g_measured_freq = ((float)NUM_PERIODS_TO_AVERAGE * TIMER5_TICK_FREQ_HZ) / total_ticks;

                    /*******************************************************************/
                    /* 接下来是您的频率取整逻辑...                                     */
                    /*******************************************************************/
                    float multiplier = g_measured_freq / 200.0f;
                    float rounded_multiplier = roundf(multiplier);
                    if(g_current_signal_rate != rounded_multiplier * 200)
                    {
                        g_current_signal_rate = rounded_multiplier * 200;
                        g_current_sampling_simulation = g_current_signal_rate * 20;
                        Write_frequence(3, g_current_sampling_simulation);
                    }
                }

                // 复位状态，为下一轮N周期测量做准备
                g_tim5_capture_state = 0;
            }
        }
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
