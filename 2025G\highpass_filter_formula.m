%% 二阶高通滤波器从s域到z域转换 - 通用公式推导
% 作者：STM32项目组
% 日期：2025-07-30
% 功能：推导高通滤波器 H(s) = cs^2/(s^2 + as + b) 的通用z域差分方程公式

clear all;
close all;
clc;

fprintf('========================================\n');
fprintf('二阶高通滤波器 - 通用公式推导\n');
fprintf('========================================\n');

%% 原始传递函数
fprintf('原始传递函数: H(s) = cs^2/(s^2 + as + b)\n');
fprintf('其中 a, b, c, T 为未知参数\n\n');

%% 步骤1：高通滤波器分解
fprintf('=== 步骤1：高通滤波器分解 ===\n');
fprintf('将高通滤波器重写为:\n');
fprintf('H(s) = cs^2/(s^2 + as + b) = c - c×b/(s^2 + as + b)\n');
fprintf('= c + H_lp(s)\n');
fprintf('其中 H_lp(s) = -cb/(s^2 + as + b) 是低通部分\n\n');

fprintf('这样高通滤波器 = 直流增益 c + 低通滤波器(-cb)\n\n');

%% 步骤2：对低通部分进行分解
fprintf('=== 步骤2：低通部分的部分分式分解 ===\n');
fprintf('对 H_lp(s) = -cb/(s^2 + as + b) 进行分解\n');
fprintf('特征方程: s^2 + as + b = 0\n');
fprintf('判别式: Δ = a^2 - 4b\n\n');

fprintf('情况1: Δ ≥ 0 (实根情况)\n');
fprintf('根: s1 = (-a + √Δ)/2,  s2 = (-a - √Δ)/2\n');
fprintf('极点: p1 = -s1 = (a - √Δ)/2,  p2 = -s2 = (a + √Δ)/2\n');
fprintf('部分分式: H_lp(s) = A1/(s + p1) + A2/(s + p2)\n');
fprintf('系数: A1 = -cb/(s1 - s2) = -cb/√Δ,  A2 = -cb/(s2 - s1) = cb/√Δ\n\n');

fprintf('情况2: Δ < 0 (复根情况)\n');
fprintf('σ = -a/2,  ω = √(-Δ)/2 = √(4b - a^2)/2\n');
fprintf('复根: s1,2 = σ ± jω\n');
fprintf('此时直接转换为二阶数字滤波器\n\n');

%% 步骤3：双线性变换
fprintf('=== 步骤3：双线性变换 ===\n');
fprintf('变换公式: s = (2/T) × (z-1)/(z+1)\n\n');

fprintf('实根情况的变换:\n');
fprintf('对低通部分的两个一阶滤波器分别变换:\n');
fprintf('b0_1 = b1_1 = A1×T/(2 + p1×T) = -cb×T/((2 + p1×T)×√Δ)\n');
fprintf('a1_1 = (p1×T - 2)/(2 + p1×T)\n\n');
fprintf('b0_2 = b1_2 = A2×T/(2 + p2×T) = cb×T/((2 + p2×T)×√Δ)\n');
fprintf('a1_2 = (p2×T - 2)/(2 + p2×T)\n\n');

fprintf('复根情况的变换:\n');
fprintf('直接转换为二阶IIR滤波器，但需要考虑高通特性\n');
fprintf('分子系数需要体现高通特性 (1, 0, -1) 的形式\n\n');

%% 步骤4：最终差分方程
fprintf('=== 步骤4：最终差分方程公式 ===\n\n');

fprintf('实根情况 (Δ ≥ 0):\n');
fprintf('高通输出 = 直流增益 + 低通部分\n');
fprintf('y_lp1[n] = b0_1×x[n] + b1_1×x[n-1] - a1_1×y_lp1[n-1]\n');
fprintf('y_lp2[n] = b0_2×x[n] + b1_2×x[n-1] - a1_2×y_lp2[n-1]\n');
fprintf('y[n] = c×x[n] + y_lp1[n] + y_lp2[n]\n\n');

fprintf('注意：实际实现中，直流增益c通过差分实现\n');
fprintf('y[n] = y_lp1[n] + y_lp2[n] + c×(x[n] - x[n-∞])\n');
fprintf('简化为: y[n] = y_lp1[n] + y_lp2[n] + c×x[n] (假设初始条件为0)\n\n');

fprintf('其中系数为:\n');
fprintf('Δ = a^2 - 4b\n');
fprintf('p1 = (a - √Δ)/2,  p2 = (a + √Δ)/2\n');
fprintf('b0_1 = b1_1 = -cb×T/((2 + p1×T)×√Δ)\n');
fprintf('b0_2 = b1_2 = cb×T/((2 + p2×T)×√Δ)\n');
fprintf('a1_1 = (p1×T - 2)/(2 + p1×T)\n');
fprintf('a1_2 = (p2×T - 2)/(2 + p2×T)\n\n');

fprintf('复根情况 (Δ < 0):\n');
fprintf('使用单个二阶滤波器:\n');
fprintf('y[n] = b0×x[n] + b1×x[n-1] + b2×x[n-2] - a1×y[n-1] - a2×y[n-2]\n\n');

fprintf('其中系数计算:\n');
fprintf('σ = -a/2,  ω = √(4b - a^2)/2\n');
fprintf('α = 2/T + σ,  β = ω\n');
fprintf('z_real = (α×(α-2σ) + β^2)/((α-2σ)^2 + β^2)\n');
fprintf('z_imag = (β×(-2σ))/((α-2σ)^2 + β^2)\n');
fprintf('r = √(z_real^2 + z_imag^2)\n');
fprintf('θ = atan2(z_imag, z_real)\n');
fprintf('a1 = -2r×cos(θ)\n');
fprintf('a2 = r^2\n\n');

fprintf('高通滤波器的分子系数 (体现高通特性):\n');
fprintf('K = c×(4 - b×T^2)/(4 + 2σ×T + (σ^2 + ω^2)×T^2)\n');
fprintf('b0 = K,  b1 = -2K×(4 - b×T^2)/(4 + b×T^2),  b2 = K\n\n');

%% 步骤5：单片机实现模板
fprintf('=== 步骤5：单片机实现模板 ===\n\n');

fprintf('实根情况的C代码模板:\n');
fprintf('float highpass_filter_real(float input) {\n');
fprintf('    static float x1_1=0, y1_1=0, x1_2=0, y1_2=0;\n');
fprintf('    \n');
fprintf('    // 计算系数\n');
fprintf('    float delta = a*a - 4*b;\n');
fprintf('    float sqrt_delta = sqrt(delta);\n');
fprintf('    float p1 = (a - sqrt_delta)/2;\n');
fprintf('    float p2 = (a + sqrt_delta)/2;\n');
fprintf('    \n');
fprintf('    float b0_1 = -c*b*T/((2 + p1*T)*sqrt_delta);\n');
fprintf('    float b1_1 = b0_1;\n');
fprintf('    float a1_1 = (p1*T - 2)/(2 + p1*T);\n');
fprintf('    \n');
fprintf('    float b0_2 = c*b*T/((2 + p2*T)*sqrt_delta);\n');
fprintf('    float b1_2 = b0_2;\n');
fprintf('    float a1_2 = (p2*T - 2)/(2 + p2*T);\n');
fprintf('    \n');
fprintf('    // 低通部分1\n');
fprintf('    float y_lp1 = b0_1*input + b1_1*x1_1 - a1_1*y1_1;\n');
fprintf('    x1_1 = input; y1_1 = y_lp1;\n');
fprintf('    \n');
fprintf('    // 低通部分2\n');
fprintf('    float y_lp2 = b0_2*input + b1_2*x1_2 - a1_2*y1_2;\n');
fprintf('    x1_2 = input; y1_2 = y_lp2;\n');
fprintf('    \n');
fprintf('    // 高通输出 = 直流增益 + 低通部分\n');
fprintf('    return c*input + y_lp1 + y_lp2;\n');
fprintf('}\n\n');

fprintf('复根情况的C代码模板:\n');
fprintf('float highpass_filter_complex(float input) {\n');
fprintf('    static float x1=0, x2=0, y1=0, y2=0;\n');
fprintf('    \n');
fprintf('    // 计算系数\n');
fprintf('    float sigma = -a/2;\n');
fprintf('    float omega = sqrt(4*b - a*a)/2;\n');
fprintf('    float alpha = 2/T + sigma;\n');
fprintf('    float beta = omega;\n');
fprintf('    \n');
fprintf('    float denom = (alpha-2*sigma)*(alpha-2*sigma) + beta*beta;\n');
fprintf('    float z_real = (alpha*(alpha-2*sigma) + beta*beta)/denom;\n');
fprintf('    float z_imag = (beta*(-2*sigma))/denom;\n');
fprintf('    \n');
fprintf('    float r = sqrt(z_real*z_real + z_imag*z_imag);\n');
fprintf('    float theta = atan2(z_imag, z_real);\n');
fprintf('    \n');
fprintf('    float a1_coeff = -2*r*cos(theta);\n');
fprintf('    float a2_coeff = r*r;\n');
fprintf('    \n');
fprintf('    // 高通滤波器分子系数\n');
fprintf('    float K = c*(4 - b*T*T)/(4 + 2*sigma*T + (sigma*sigma + omega*omega)*T*T);\n');
fprintf('    float b0 = K;\n');
fprintf('    float b1 = -2*K*(4 - b*T*T)/(4 + b*T*T);\n');
fprintf('    float b2 = K;\n');
fprintf('    \n');
fprintf('    float output = b0*input + b1*x1 + b2*x2 - a1_coeff*y1 - a2_coeff*y2;\n');
fprintf('    \n');
fprintf('    x2 = x1; x1 = input;\n');
fprintf('    y2 = y1; y1 = output;\n');
fprintf('    \n');
fprintf('    return output;\n');
fprintf('}\n\n');

%% 总结
fprintf('=== 总结 ===\n');
fprintf('高通滤波器转换公式已推导完成\n');
fprintf('1. 高通滤波器 = 直流增益 + 低通部分\n');
fprintf('2. 根据判别式 Δ = a^2 - 4b 选择实现方式\n');
fprintf('3. Δ ≥ 0: 直流增益 + 两个一阶低通滤波器\n');
fprintf('4. Δ < 0: 单个二阶滤波器，分子体现高通特性\n');
fprintf('5. 所有系数都用 a, b, c, T 的通用公式表示\n');

fprintf('\n========================================\n');
fprintf('高通滤波器通用公式推导完成\n');
fprintf('========================================\n');
