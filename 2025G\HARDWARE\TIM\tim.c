#include "tim.h"
#include "delay.h"
#include "AD9959.h"
#include "math.h"

/* ======= 全局变量定义 ======= */
volatile uint32_t g_tim5_capture_val1 = 0;
volatile uint32_t g_tim5_capture_val2 = 0;
volatile uint8_t g_tim5_capture_state = 0;
volatile uint32_t g_capture_count = 0;
volatile float g_measured_freq = 0.0f;
volatile float g_current_signal_rate = 50000.0f;
volatile float g_current_sampling_simulation = 1000000.0f;

/* ======= 静态变量 ======= */
static TIM_HandleTypeDef htim3_mode3;  // 用于ADC/DAC采样触发
static TIM_HandleTypeDef htim5_mode3;  // 用于频率测量

/* ======= 模式3 定时器初始化 ======= */
void TIM_Mode3_Init(void)
{
    printf("Initializing TIM for Mode 3...\r\n");
    
    // 初始化TIM3用于采样触发
    TIM3_Init(1000000);  // 默认1MHz采样频率
    
    // 初始化TIM5用于频率测量
    TIM5_Init();
    
    printf("TIM Mode 3 initialization complete.\r\n");
}

/* ======= TIM3初始化 - 用于ADC/DAC采样触发 ======= */
void TIM3_Init(uint32_t sampling_freq)
{
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    
    // 使能时钟
    __HAL_RCC_TIM3_CLK_ENABLE();
    
    // 计算定时器参数
    // TIM3时钟频率为84MHz (APB1 * 2)
    uint32_t timer_freq = 84000000;
    uint32_t period = timer_freq / sampling_freq - 1;
    
    // 配置TIM3
    htim3_mode3.Instance = TIM3;
    htim3_mode3.Init.Prescaler = 0;
    htim3_mode3.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim3_mode3.Init.Period = period;
    htim3_mode3.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim3_mode3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    
    if (HAL_TIM_Base_Init(&htim3_mode3) != HAL_OK) {
        printf("TIM3 initialization failed!\r\n");
        return;
    }
    
    // 配置主模式 - 输出TRGO信号
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_UPDATE;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim3_mode3, &sMasterConfig) != HAL_OK) {
        printf("TIM3 master configuration failed!\r\n");
        return;
    }
    
    printf("TIM3 configured for %lu Hz sampling.\r\n", sampling_freq);
}

/* ======= TIM5初始化 - 用于频率测量 ======= */
void TIM5_Init(void)
{
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_IC_InitTypeDef sConfigIC = {0};
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能时钟
    __HAL_RCC_TIM5_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // 配置GPIO (PA3 -> TIM5_CH4)
    GPIO_InitStruct.Pin = GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    GPIO_InitStruct.Alternate = GPIO_AF2_TIM5;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 配置TIM5
    htim5_mode3.Instance = TIM5;
    htim5_mode3.Init.Prescaler = 0;
    htim5_mode3.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim5_mode3.Init.Period = 4294967295;  // 32位最大值
    htim5_mode3.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim5_mode3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    
    if (HAL_TIM_IC_Init(&htim5_mode3) != HAL_OK) {
        printf("TIM5 initialization failed!\r\n");
        return;
    }
    
    // 配置主模式
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim5_mode3, &sMasterConfig) != HAL_OK) {
        printf("TIM5 master configuration failed!\r\n");
        return;
    }
    
    // 配置输入捕获
    sConfigIC.ICPolarity = TIM_INPUTCHANNELPOLARITY_RISING;
    sConfigIC.ICSelection = TIM_ICSELECTION_DIRECTTI;
    sConfigIC.ICPrescaler = TIM_ICPSC_DIV1;
    sConfigIC.ICFilter = 0;
    if (HAL_TIM_IC_ConfigChannel(&htim5_mode3, &sConfigIC, TIM_CHANNEL_4) != HAL_OK) {
        printf("TIM5 input capture configuration failed!\r\n");
        return;
    }
    
    // 配置NVIC中断优先级
    HAL_NVIC_SetPriority(TIM5_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(TIM5_IRQn);
    
    printf("TIM5 configured for frequency measurement.\r\n");
}

/* ======= 开始采样定时器 ======= */
void TIM_Start_Sampling_Timer(uint32_t sampling_freq)
{
    // 重新配置TIM3频率
    TIM3_Init(sampling_freq);
    
    // 启动TIM3
    if (HAL_TIM_Base_Start(&htim3_mode3) != HAL_OK) {
        printf("TIM3 start failed!\r\n");
        return;
    }
    
    printf("Sampling timer started at %lu Hz.\r\n", sampling_freq);
}

/* ======= 停止采样定时器 ======= */
void TIM_Stop_Sampling_Timer(void)
{
    HAL_TIM_Base_Stop(&htim3_mode3);
    printf("Sampling timer stopped.\r\n");
}

/* ======= 开始频率测量 ======= */
void TIM_Start_Frequency_Measurement(void)
{
    g_tim5_capture_state = 0;
    g_capture_count = 0;
    g_measured_freq = 0.0f;
    
    if (HAL_TIM_IC_Start_IT(&htim5_mode3, TIM_CHANNEL_4) != HAL_OK) {
        printf("TIM5 input capture start failed!\r\n");
        return;
    }
    
    printf("Frequency measurement started.\r\n");
}

/* ======= 停止频率测量 ======= */
void TIM_Stop_Frequency_Measurement(void)
{
    HAL_TIM_IC_Stop_IT(&htim5_mode3, TIM_CHANNEL_4);
    printf("Frequency measurement stopped.\r\n");
}

/* ======= TIM5中断处理函数 ======= */
void TIM5_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&htim5_mode3);
}

/* ======= 输入捕获回调函数 ======= */
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM5 && htim->Channel == HAL_TIM_ACTIVE_CHANNEL_4) {
        if (g_tim5_capture_state == 0) {
            // 第一个边沿
            g_tim5_capture_val1 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_4);
            g_capture_count = 0;
            g_tim5_capture_state = 1;
        } else if (g_tim5_capture_state == 1) {
            // 后续边沿
            g_capture_count++;
            
            if (g_capture_count >= NUM_PERIODS_TO_AVERAGE) {
                // 读取结束时间
                g_tim5_capture_val2 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_4);
                
                uint32_t total_ticks;
                if (g_tim5_capture_val2 > g_tim5_capture_val1) {
                    total_ticks = g_tim5_capture_val2 - g_tim5_capture_val1;
                } else {
                    // 定时器溢出
                    total_ticks = (0xFFFFFFFF - g_tim5_capture_val1) + g_tim5_capture_val2 + 1;
                }
                
                // 计算频率
                if (total_ticks > 0) {
                    g_measured_freq = ((float)NUM_PERIODS_TO_AVERAGE * TIMER5_TICK_FREQ_HZ) / total_ticks;
                    
                    // 频率取整逻辑
                    float multiplier = g_measured_freq / 200.0f;
                    float rounded_multiplier = roundf(multiplier);
                    if (g_current_signal_rate != rounded_multiplier * 200) {
                        g_current_signal_rate = rounded_multiplier * 200;
                        g_current_sampling_simulation = g_current_signal_rate * 20;
                        Write_frequence(3, (uint32_t)g_current_sampling_simulation);
                    }
                }
                
                // 重置状态
                g_tim5_capture_state = 0;
            }
        }
    }
}
