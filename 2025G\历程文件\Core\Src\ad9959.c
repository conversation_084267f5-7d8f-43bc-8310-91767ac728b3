#include "ad9959.h"

/*
 * 注意：所有旧的引脚宏定义 (例如 #define CS PBout(3)) 都已被移除。
 * CubeMX 会在 main.h 中根据您设置的 User Label 自动生成宏，例如：
 * CS_Pin, CS_GPIO_Port, SCLK_Pin, SCLK_GPIO_Port 等。
 */

/* 寄存器地址定义 (无变化) */
#define CSR_ADD      0x00
#define FR1_ADD      0x01
#define FR2_ADD      0x02
#define CFR_ADD      0x03
#define CFTW0_ADD    0x04
#define CPOW0_ADD    0x05
#define ACR_ADD      0x06
#define LSRR_ADD     0x07
#define RDW_ADD      0x08
#define FDW_ADD      0x09

/* 寄存器默认值 (无变化) */
uchar CSR_DATA0[1] = {0x10}; // CH0
uchar CSR_DATA1[1] = {0x20}; // CH1
uchar CSR_DATA2[1] = {0x40}; // CH2
uchar CSR_DATA3[1] = {0x80}; // CH3

uchar FR1_DATA[3] = {0xD0,0x00,0x00};
uchar CFR_DATA[3] = {0x00,0x03,0x02};
uchar ACR_DATA[3] = {0x00,0x00,0x00}; // 幅度数据将在函数中填充
uchar CPOW0_DATA[2] = {0x00,0x00};    // 相位数据将在函数中填充


/**
  * @brief  复位 AD9959
  * @param  None
  * @retval None
  */
void IntReset(void)
{
    HAL_GPIO_WritePin(Reset_GPIO_Port, Reset_Pin, GPIO_PIN_RESET);
    HAL_Delay(1); // 短暂延时
    HAL_GPIO_WritePin(Reset_GPIO_Port, Reset_Pin, GPIO_PIN_SET);
    HAL_Delay(1);
    HAL_GPIO_WritePin(Reset_GPIO_Port, Reset_Pin, GPIO_PIN_RESET);
}

/**
  * @brief  触发 AD9959 的 I/O 更新，使寄存器设置生效
  * @param  None
  * @retval None
  */
void IO_Update(void)
{
    HAL_GPIO_WritePin(UPDATE_GPIO_Port, UPDATE_Pin, GPIO_PIN_RESET);
    HAL_Delay(1); // 延时应根据您的系统时钟和 AD9959 要求微调
    HAL_GPIO_WritePin(UPDATE_GPIO_Port, UPDATE_Pin, GPIO_PIN_SET);
    HAL_Delay(1);
    HAL_GPIO_WritePin(UPDATE_GPIO_Port, UPDATE_Pin, GPIO_PIN_RESET);
}

/**
  * @brief  初始化 AD9959 的 IO 口状态
  * @param  None
  * @retval None
  */
void Intserve(void)
{
    // 使用 HAL 函数设置所有控制引脚的初始状态
    HAL_GPIO_WritePin(PWR1_GPIO_Port, PWR1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(UPDATE_GPIO_Port, UPDATE_Pin, GPIO_PIN_RESET);

    HAL_GPIO_WritePin(PS0_GPIO_Port, PS0_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(PS1_GPIO_Port, PS1_Pin, GPIO_PIN_RESET); // 已映射到 PC8
    HAL_GPIO_WritePin(PS2_GPIO_Port, PS2_Pin, GPIO_PIN_RESET); // 已映射到 PC9
    HAL_GPIO_WritePin(PS3_GPIO_Port, PS3_Pin, GPIO_PIN_RESET);

    HAL_GPIO_WritePin(SDIO0_GPIO_Port, SDIO0_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(SDIO1_GPIO_Port, SDIO1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(SDIO2_GPIO_Port, SDIO2_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(SDIO3_GPIO_Port, SDIO3_Pin, GPIO_PIN_RESET); // 已映射到 PD12
}

/**
  * @brief  通过模拟 SPI 向 AD9959 写入数据
  * @param  RegisterAddress: 目标寄存器地址
  * @param  NumberofRegisters: 要写入的字节数
  * @param  RegisterData: 指向数据的指针
  * @retval None
  */
void WriteData_AD9959(uchar RegisterAddress, uchar NumberofRegisters, uchar *RegisterData)
{
    uchar ControlValue = RegisterAddress;
    uchar ValueToWrite = 0;
    uchar RegisterIndex = 0;
    uchar i = 0;

    HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_RESET); // 拉低 CS，开始通信

    // 写入 8-bit 的指令头 (寄存器地址)
    for(i = 0; i < 8; i++)
    {
        HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_RESET);
        if(0x80 == (ControlValue & 0x80))
        {
            HAL_GPIO_WritePin(SDIO0_GPIO_Port, SDIO0_Pin, GPIO_PIN_SET);
        }
        else
        {
            HAL_GPIO_WritePin(SDIO0_GPIO_Port, SDIO0_Pin, GPIO_PIN_RESET);
        }
        HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_SET);
        ControlValue <<= 1;
    }
    HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_RESET);

    // 写入数据
    for (RegisterIndex = 0; RegisterIndex < NumberofRegisters; RegisterIndex++)
    {
        ValueToWrite = RegisterData[RegisterIndex];
        for (i = 0; i < 8; i++)
        {
            HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_RESET);
            if(0x80 == (ValueToWrite & 0x80))
            {
                HAL_GPIO_WritePin(SDIO0_GPIO_Port, SDIO0_Pin, GPIO_PIN_SET);
            }
            else
            {
                HAL_GPIO_WritePin(SDIO0_GPIO_Port, SDIO0_Pin, GPIO_PIN_RESET);
            }
            HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_SET);
            ValueToWrite <<= 1;
        }
        HAL_GPIO_WritePin(SCLK_GPIO_Port, SCLK_Pin, GPIO_PIN_RESET);
    }
    HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_SET); // 拉高 CS，结束通信
}

/**
  * @brief  初始化 AD9959 芯片
  * @param  None
  * @retval None
  */
void Init_AD9959(void)
{
    Intserve(); // 初始化 IO 口状态
    IntReset(); // 复位芯片

    WriteData_AD9959(FR1_ADD, 3, FR1_DATA); // 配置功能寄存器1 (20倍频等)
    IO_Update(); // 更新生效

    // 依次设置四个通道的默认频率
    Write_frequence(0, 100000000); // CH0: 100MHz
    Write_frequence(1, 80000000);  // CH1: 80MHz
    Write_frequence(2, 10000000);  // CH2: 10MHz
    Write_frequence(3, 1000000);   // CH3: 1MHz
}

/**
  * @brief  设置指定通道的频率
  * @param  Channel: 通道号 (0-3)
  * @param  Freq: 频率值 (Hz)
  * @retval None
  */
void Write_frequence(uchar Channel, ulong Freq)
{
    uchar CFTW0_DATA[4] = {0x00, 0x00, 0x00, 0x00};
    ulong Temp;
    // 频率转换公式: Temp = (Freq * 2^32) / SysClock
    // 假设 SysClock = 500MHz, (2^32)/500M = 8.589934592
    Temp = (ulong)(Freq * 8.589934592);
    CFTW0_DATA[3] = (uchar)Temp;
    CFTW0_DATA[2] = (uchar)(Temp >> 8);
    CFTW0_DATA[1] = (uchar)(Temp >> 16);
    CFTW0_DATA[0] = (uchar)(Temp >> 24);

    // 根据通道选择对应的 CSR 值
    if(Channel == 0) WriteData_AD9959(CSR_ADD, 1, CSR_DATA0);
    if(Channel == 1) WriteData_AD9959(CSR_ADD, 1, CSR_DATA1);
    if(Channel == 2) WriteData_AD9959(CSR_ADD, 1, CSR_DATA2);
    if(Channel == 3) WriteData_AD9959(CSR_ADD, 1, CSR_DATA3);

    // 写入频率调谐字
    WriteData_AD9959(CFTW0_ADD, 4, CFTW0_DATA);
    IO_Update(); // 每次设置后都更新
}

/**
  * @brief  设置指定通道的幅度
  * @param  Channel: 通道号 (0-3)
  * @param  Ampli: 幅度值 (0-1023)
  * @retval None
  */
void Write_Amplitude(uchar Channel, uint Ampli)
{
    uint A_temp = Ampli & 0x03FF; // 确保幅度值在 10-bit 范围内
    A_temp |= 0x1000; // 使能幅度缩放因子
    ACR_DATA[2] = (uchar)A_temp;
    ACR_DATA[1] = (uchar)(A_temp >> 8);
    // ACR_DATA[0] 保持默认，可以根据需要修改

    if(Channel == 0) WriteData_AD9959(CSR_ADD, 1, CSR_DATA0);
    if(Channel == 1) WriteData_AD9959(CSR_ADD, 1, CSR_DATA1);
    if(Channel == 2) WriteData_AD9959(CSR_ADD, 1, CSR_DATA2);
    if(Channel == 3) WriteData_AD9959(CSR_ADD, 1, CSR_DATA3);

    WriteData_AD9959(ACR_ADD, 3, ACR_DATA);
    IO_Update(); // 每次设置后都更新
}

/**
  * @brief  设置指定通道的相位
  * @param  Channel: 通道号 (0-3)
  * @param  Phase: 相位值 (0-359 度)
  * @retval None
  */
void Write_Phase(uchar Channel, uint Phase)
{
    uint P_temp = 0;
    // 相位转换公式: P_temp = (Phase * 2^14) / 360
    // (2^14)/360 = 45.5111
    P_temp = (uint)(Phase * 45.511111);
    CPOW0_DATA[1] = (uchar)P_temp;
    CPOW0_DATA[0] = (uchar)(P_temp >> 8);

    if(Channel == 0) WriteData_AD9959(CSR_ADD, 1, CSR_DATA0);
    if(Channel == 1) WriteData_AD9959(CSR_ADD, 1, CSR_DATA1);
    if(Channel == 2) WriteData_AD9959(CSR_ADD, 1, CSR_DATA2);
    if(Channel == 3) WriteData_AD9959(CSR_ADD, 1, CSR_DATA3);

    WriteData_AD9959(CPOW0_ADD, 2, CPOW0_DATA);
    IO_Update(); // 每次设置后都更新
}
