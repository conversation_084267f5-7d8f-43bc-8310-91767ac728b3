Component: Arm Compiler for Embedded 6.19 Tool: armlink [5e73cb00]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lcd.o(.text.lcd_ex_st7789_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_st7789_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.text.lcd_ex_st7789_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.ARM.exidx.text.lcd_ex_st7789_reginit) refers to lcd.o(.text.lcd_ex_st7789_reginit) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.lcd_wr_regno) refers to lcd.o(.text.lcd_wr_regno) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.lcd_wr_data) refers to lcd.o(.text.lcd_wr_data) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_ili9341_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_ili9341_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.text.lcd_ex_ili9341_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.lcd_ex_ili9341_reginit) refers to lcd.o(.text.lcd_ex_ili9341_reginit) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_nt35310_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_nt35310_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.text.lcd_ex_nt35310_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.lcd_ex_nt35310_reginit) refers to lcd.o(.text.lcd_ex_nt35310_reginit) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_st7796_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_st7796_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.text.lcd_ex_st7796_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.ARM.exidx.text.lcd_ex_st7796_reginit) refers to lcd.o(.text.lcd_ex_st7796_reginit) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_nt35510_reginit) refers to lcd.o(.text.lcd_write_reg) for lcd_write_reg
    lcd.o(.text.lcd_ex_nt35510_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_nt35510_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.lcd_ex_nt35510_reginit) refers to lcd.o(.text.lcd_ex_nt35510_reginit) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.lcd_write_reg) refers to lcd.o(.text.lcd_write_reg) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_ili9806_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_ili9806_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.text.lcd_ex_ili9806_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.lcd_ex_ili9806_reginit) refers to lcd.o(.text.lcd_ex_ili9806_reginit) for [Anonymous Symbol]
    lcd.o(.text.lcd_ex_ssd1963_reginit) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ex_ssd1963_reginit) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.text.lcd_ex_ssd1963_reginit) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.lcd_ex_ssd1963_reginit) refers to lcd.o(.text.lcd_ex_ssd1963_reginit) for [Anonymous Symbol]
    lcd.o(.text.lcd_write_ram_prepare) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.ARM.exidx.text.lcd_write_ram_prepare) refers to lcd.o(.text.lcd_write_ram_prepare) for [Anonymous Symbol]
    lcd.o(.text.lcd_read_point) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_read_point) refers to lcd.o(.text.lcd_set_cursor) for lcd_set_cursor
    lcd.o(.text.lcd_read_point) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_read_point) refers to lcd.o(.text.lcd_rd_data) for lcd_rd_data
    lcd.o(.ARM.exidx.text.lcd_read_point) refers to lcd.o(.text.lcd_read_point) for [Anonymous Symbol]
    lcd.o(.text.lcd_set_cursor) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_set_cursor) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_set_cursor) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.ARM.exidx.text.lcd_set_cursor) refers to lcd.o(.text.lcd_set_cursor) for [Anonymous Symbol]
    lcd.o(.text.lcd_rd_data) refers to lcd.o(.text.lcd_opt_delay) for lcd_opt_delay
    lcd.o(.ARM.exidx.text.lcd_rd_data) refers to lcd.o(.text.lcd_rd_data) for [Anonymous Symbol]
    lcd.o(.text.lcd_display_on) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_display_on) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.ARM.exidx.text.lcd_display_on) refers to lcd.o(.text.lcd_display_on) for [Anonymous Symbol]
    lcd.o(.text.lcd_display_off) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_display_off) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.ARM.exidx.text.lcd_display_off) refers to lcd.o(.text.lcd_display_off) for [Anonymous Symbol]
    lcd.o(.text.lcd_scan_dir) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_scan_dir) refers to lcd.o(.text.lcd_write_reg) for lcd_write_reg
    lcd.o(.text.lcd_scan_dir) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_scan_dir) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.ARM.exidx.text.lcd_scan_dir) refers to lcd.o(.text.lcd_scan_dir) for [Anonymous Symbol]
    lcd.o(.text.lcd_draw_point) refers to lcd.o(.text.lcd_set_cursor) for lcd_set_cursor
    lcd.o(.text.lcd_draw_point) refers to lcd.o(.text.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(.ARM.exidx.text.lcd_draw_point) refers to lcd.o(.text.lcd_draw_point) for [Anonymous Symbol]
    lcd.o(.text.lcd_ssd_backlight_set) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_ssd_backlight_set) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.text.lcd_ssd_backlight_set) refers to dflti.o(.text) for __aeabi_i2d
    lcd.o(.text.lcd_ssd_backlight_set) refers to dmul.o(.text) for __aeabi_dmul
    lcd.o(.text.lcd_ssd_backlight_set) refers to dfixi.o(.text) for __aeabi_d2iz
    lcd.o(.ARM.exidx.text.lcd_ssd_backlight_set) refers to lcd.o(.text.lcd_ssd_backlight_set) for [Anonymous Symbol]
    lcd.o(.text.lcd_display_dir) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_display_dir) refers to lcd.o(.text.lcd_scan_dir) for lcd_scan_dir
    lcd.o(.ARM.exidx.text.lcd_display_dir) refers to lcd.o(.text.lcd_display_dir) for [Anonymous Symbol]
    lcd.o(.text.lcd_set_window) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_set_window) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_set_window) refers to lcd.o(.text.lcd_wr_data) for lcd_wr_data
    lcd.o(.ARM.exidx.text.lcd_set_window) refers to lcd.o(.text.lcd_set_window) for [Anonymous Symbol]
    lcd.o(.text.lcd_init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_wr_regno) for lcd_wr_regno
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_rd_data) for lcd_rd_data
    lcd.o(.text.lcd_init) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_write_reg) for lcd_write_reg
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_st7789_reginit) for lcd_ex_st7789_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_ili9341_reginit) for lcd_ex_ili9341_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_nt35310_reginit) for lcd_ex_nt35310_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_st7796_reginit) for lcd_ex_st7796_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_nt35510_reginit) for lcd_ex_nt35510_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_ili9806_reginit) for lcd_ex_ili9806_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ex_ssd1963_reginit) for lcd_ex_ssd1963_reginit
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_ssd_backlight_set) for lcd_ssd_backlight_set
    lcd.o(.text.lcd_init) refers to lcd.o(.bss.g_sram_handle) for g_sram_handle
    lcd.o(.text.lcd_init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_display_dir) for lcd_display_dir
    lcd.o(.text.lcd_init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(.text.lcd_init) refers to lcd.o(.text.lcd_clear) for lcd_clear
    lcd.o(.ARM.exidx.text.lcd_init) refers to lcd.o(.text.lcd_init) for [Anonymous Symbol]
    lcd.o(.text.lcd_clear) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_clear) refers to lcd.o(.text.lcd_set_cursor) for lcd_set_cursor
    lcd.o(.text.lcd_clear) refers to lcd.o(.text.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(.ARM.exidx.text.lcd_clear) refers to lcd.o(.text.lcd_clear) for [Anonymous Symbol]
    lcd.o(.text.lcd_fill) refers to lcd.o(.text.lcd_set_cursor) for lcd_set_cursor
    lcd.o(.text.lcd_fill) refers to lcd.o(.text.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(.ARM.exidx.text.lcd_fill) refers to lcd.o(.text.lcd_fill) for [Anonymous Symbol]
    lcd.o(.text.lcd_color_fill) refers to lcd.o(.text.lcd_set_cursor) for lcd_set_cursor
    lcd.o(.text.lcd_color_fill) refers to lcd.o(.text.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(.ARM.exidx.text.lcd_color_fill) refers to lcd.o(.text.lcd_color_fill) for [Anonymous Symbol]
    lcd.o(.text.lcd_draw_line) refers to lcd.o(.text.lcd_draw_point) for lcd_draw_point
    lcd.o(.ARM.exidx.text.lcd_draw_line) refers to lcd.o(.text.lcd_draw_line) for [Anonymous Symbol]
    lcd.o(.text.lcd_draw_hline) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.text.lcd_draw_hline) refers to lcd.o(.text.lcd_fill) for lcd_fill
    lcd.o(.ARM.exidx.text.lcd_draw_hline) refers to lcd.o(.text.lcd_draw_hline) for [Anonymous Symbol]
    lcd.o(.text.lcd_draw_rectangle) refers to lcd.o(.text.lcd_draw_line) for lcd_draw_line
    lcd.o(.ARM.exidx.text.lcd_draw_rectangle) refers to lcd.o(.text.lcd_draw_rectangle) for [Anonymous Symbol]
    lcd.o(.text.lcd_draw_circle) refers to lcd.o(.text.lcd_draw_point) for lcd_draw_point
    lcd.o(.ARM.exidx.text.lcd_draw_circle) refers to lcd.o(.text.lcd_draw_circle) for [Anonymous Symbol]
    lcd.o(.text.lcd_fill_circle) refers to lcd.o(.text.lcd_draw_hline) for lcd_draw_hline
    lcd.o(.ARM.exidx.text.lcd_fill_circle) refers to lcd.o(.text.lcd_fill_circle) for [Anonymous Symbol]
    lcd.o(.text.lcd_show_char) refers to lcd.o(.rodata.asc2_1206) for asc2_1206
    lcd.o(.text.lcd_show_char) refers to lcd.o(.rodata.asc2_1608) for asc2_1608
    lcd.o(.text.lcd_show_char) refers to lcd.o(.rodata.asc2_2412) for asc2_2412
    lcd.o(.text.lcd_show_char) refers to lcd.o(.rodata.asc2_3216) for asc2_3216
    lcd.o(.text.lcd_show_char) refers to lcd.o(.text.lcd_draw_point) for lcd_draw_point
    lcd.o(.text.lcd_show_char) refers to lcd.o(.data.g_back_color) for g_back_color
    lcd.o(.text.lcd_show_char) refers to lcd.o(.bss.lcddev) for lcddev
    lcd.o(.ARM.exidx.text.lcd_show_char) refers to lcd.o(.text.lcd_show_char) for [Anonymous Symbol]
    lcd.o(.text.lcd_show_num) refers to lcd.o(.text.lcd_pow) for lcd_pow
    lcd.o(.text.lcd_show_num) refers to lcd.o(.text.lcd_show_char) for lcd_show_char
    lcd.o(.ARM.exidx.text.lcd_show_num) refers to lcd.o(.text.lcd_show_num) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.lcd_pow) refers to lcd.o(.text.lcd_pow) for [Anonymous Symbol]
    lcd.o(.text.lcd_show_xnum) refers to lcd.o(.text.lcd_pow) for lcd_pow
    lcd.o(.text.lcd_show_xnum) refers to lcd.o(.text.lcd_show_char) for lcd_show_char
    lcd.o(.ARM.exidx.text.lcd_show_xnum) refers to lcd.o(.text.lcd_show_xnum) for [Anonymous Symbol]
    lcd.o(.text.lcd_show_string) refers to lcd.o(.text.lcd_show_char) for lcd_show_char
    lcd.o(.ARM.exidx.text.lcd_show_string) refers to lcd.o(.text.lcd_show_string) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.lcd_opt_delay) refers to lcd.o(.text.lcd_opt_delay) for [Anonymous Symbol]
    ad9959.o(.text.IntReset) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.IntReset) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.IntReset) refers to ad9959.o(.text.IntReset) for [Anonymous Symbol]
    ad9959.o(.text.IO_Update) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.IO_Update) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.IO_Update) refers to ad9959.o(.text.IO_Update) for [Anonymous Symbol]
    ad9959.o(.text.Intserve) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.Intserve) refers to ad9959.o(.text.Intserve) for [Anonymous Symbol]
    ad9959.o(.text.WriteData_AD9959) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.WriteData_AD9959) refers to ad9959.o(.text.WriteData_AD9959) for [Anonymous Symbol]
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.text.Intserve) for Intserve
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.text.IntReset) for IntReset
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.data.FR1_DATA) for FR1_DATA
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.text.WriteData_AD9959) for WriteData_AD9959
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.text.IO_Update) for IO_Update
    ad9959.o(.text.Init_AD9959) refers to ad9959.o(.text.Write_frequence) for Write_frequence
    ad9959.o(.ARM.exidx.text.Init_AD9959) refers to ad9959.o(.text.Init_AD9959) for [Anonymous Symbol]
    ad9959.o(.text.Write_frequence) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.Write_frequence) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.Write_frequence) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.data.CSR_DATA0) for CSR_DATA0
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.text.WriteData_AD9959) for WriteData_AD9959
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.data.CSR_DATA1) for CSR_DATA1
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.data.CSR_DATA2) for CSR_DATA2
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.data.CSR_DATA3) for CSR_DATA3
    ad9959.o(.text.Write_frequence) refers to ad9959.o(.text.IO_Update) for IO_Update
    ad9959.o(.ARM.exidx.text.Write_frequence) refers to ad9959.o(.text.Write_frequence) for [Anonymous Symbol]
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.bss.ACR_DATA) for ACR_DATA
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.data.CSR_DATA0) for CSR_DATA0
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.text.WriteData_AD9959) for WriteData_AD9959
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.data.CSR_DATA1) for CSR_DATA1
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.data.CSR_DATA2) for CSR_DATA2
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.data.CSR_DATA3) for CSR_DATA3
    ad9959.o(.text.Write_Amplitude) refers to ad9959.o(.text.IO_Update) for IO_Update
    ad9959.o(.ARM.exidx.text.Write_Amplitude) refers to ad9959.o(.text.Write_Amplitude) for [Anonymous Symbol]
    ad9959.o(.text.Write_Phase) refers to dflti.o(.text) for __aeabi_i2d
    ad9959.o(.text.Write_Phase) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.Write_Phase) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.bss.CPOW0_DATA) for CPOW0_DATA
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.data.CSR_DATA0) for CSR_DATA0
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.text.WriteData_AD9959) for WriteData_AD9959
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.data.CSR_DATA1) for CSR_DATA1
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.data.CSR_DATA2) for CSR_DATA2
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.data.CSR_DATA3) for CSR_DATA3
    ad9959.o(.text.Write_Phase) refers to ad9959.o(.text.IO_Update) for IO_Update
    ad9959.o(.ARM.exidx.text.Write_Phase) refers to ad9959.o(.text.Write_Phase) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to fsmc.o(.text.MX_FSMC_Init) for MX_FSMC_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC1_Init) for MX_ADC1_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC2_Init) for MX_ADC2_Init
    main.o(.text.main) refers to dac.o(.text.MX_DAC_Init) for MX_DAC_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM5_Init) for MX_TIM5_Init
    main.o(.text.main) refers to lcd.o(.text.lcd_init) for lcd_init
    main.o(.text.main) refers to ad9959.o(.text.Init_AD9959) for Init_AD9959
    main.o(.text.main) refers to main.o(.bss.g_fft_instance) for g_fft_instance
    main.o(.text.main) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) for arm_rfft_fast_init_f32
    main.o(.text.main) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.main) refers to lcd.o(.text.lcd_clear) for lcd_clear
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to main.o(.text.LCD_ShowString_Simplified) for LCD_ShowString_Simplified
    main.o(.text.main) refers to ad9959.o(.text.Write_frequence) for Write_frequence
    main.o(.text.main) refers to ad9959.o(.text.Write_Amplitude) for Write_Amplitude
    main.o(.text.main) refers to main.o(.text.Generate_Flat_Top_Window) for Generate_Flat_Top_Window
    main.o(.text.main) refers to main.o(.text.Generate_Hanning_Window) for Generate_Hanning_Window
    main.o(.text.main) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.text.main) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to main.o(.text.Start_Sweep_And_Analysis) for Start_Sweep_And_Analysis
    main.o(.text.main) refers to main.o(.text.Start_Simulation_Mode) for Start_Simulation_Mode
    main.o(.text.main) refers to main.o(.bss.g_adc1_data_ready) for g_adc1_data_ready
    main.o(.text.main) refers to main.o(.bss.g_adc2_data_ready) for g_adc2_data_ready
    main.o(.text.main) refers to main.o(.data.g_current_freq_sweep) for g_current_freq_sweep
    main.o(.text.main) refers to main.o(.bss.sampling_frequency_sweep) for sampling_frequency_sweep
    main.o(.text.main) refers to main.o(.bss.g_adc1_processing_buffer) for g_adc1_processing_buffer
    main.o(.text.main) refers to main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) for Get_Signal_Accurate_Amplitude_And_Phase
    main.o(.text.main) refers to main.o(.bss.g_adc2_processing_buffer) for g_adc2_processing_buffer
    main.o(.text.main) refers to main.o(.bss.g_sweep_current_step) for g_sweep_current_step
    main.o(.text.main) refers to main.o(.bss.g_H_magnitude) for g_H_magnitude
    main.o(.text.main) refers to main.o(.bss.stop_flag) for stop_flag
    main.o(.text.main) refers to main.o(.bss.g_H_phase) for g_H_phase
    main.o(.text.main) refers to adc.o(.bss.hadc1) for hadc1
    main.o(.text.main) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    main.o(.text.main) refers to adc.o(.bss.hadc2) for hadc2
    main.o(.text.main) refers to main.o(.bss.g_adc1_dma_buffer) for g_adc1_dma_buffer
    main.o(.text.main) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(.text.main) refers to main.o(.bss.g_adc2_dma_buffer) for g_adc2_dma_buffer
    main.o(.text.main) refers to main.o(.text.Analyze_Filter_Response) for Analyze_Filter_Response
    main.o(.text.main) refers to main.o(.text.Display_Results) for Display_Results
    main.o(.text.main) refers to main.o(.text.Stop_Simulation_Mode) for Stop_Simulation_Mode
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    main.o(.text.LCD_ShowString_Simplified) refers to lcd.o(.text.lcd_show_string) for lcd_show_string
    main.o(.ARM.exidx.text.LCD_ShowString_Simplified) refers to main.o(.text.LCD_ShowString_Simplified) for [Anonymous Symbol]
    main.o(.text.Generate_Flat_Top_Window) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main.o(.text.Generate_Flat_Top_Window) refers to main.o(.bss.g_flat_top_window) for g_flat_top_window
    main.o(.ARM.exidx.text.Generate_Flat_Top_Window) refers to main.o(.text.Generate_Flat_Top_Window) for [Anonymous Symbol]
    main.o(.text.Generate_Hanning_Window) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main.o(.text.Generate_Hanning_Window) refers to main.o(.bss.g_hanning_window) for g_hanning_window
    main.o(.ARM.exidx.text.Generate_Hanning_Window) refers to main.o(.text.Generate_Hanning_Window) for [Anonymous Symbol]
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_sweep_current_step) for g_sweep_current_step
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.data.g_current_freq_sweep) for g_current_freq_sweep
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_filter_type) for g_filter_type
    main.o(.text.Start_Sweep_And_Analysis) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.Start_Sweep_And_Analysis) refers to adc.o(.bss.hadc1) for hadc1
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_adc1_dma_buffer) for g_adc1_dma_buffer
    main.o(.text.Start_Sweep_And_Analysis) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.Start_Sweep_And_Analysis) refers to adc.o(.bss.hadc2) for hadc2
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_adc2_dma_buffer) for g_adc2_dma_buffer
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.sampling_frequency_sweep) for sampling_frequency_sweep
    main.o(.text.Start_Sweep_And_Analysis) refers to ad9959.o(.text.Write_frequence) for Write_frequence
    main.o(.text.Start_Sweep_And_Analysis) refers to ad9959.o(.text.Write_Amplitude) for Write_Amplitude
    main.o(.text.Start_Sweep_And_Analysis) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.Start_Sweep_And_Analysis) refers to lcd.o(.text.lcd_clear) for lcd_clear
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.rodata.str1.1) for .L.str.7
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.text.LCD_ShowString_Simplified) for LCD_ShowString_Simplified
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_adc1_data_ready) for g_adc1_data_ready
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_adc2_data_ready) for g_adc2_data_ready
    main.o(.text.Start_Sweep_And_Analysis) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.ARM.exidx.text.Start_Sweep_And_Analysis) refers to main.o(.text.Start_Sweep_And_Analysis) for [Anonymous Symbol]
    main.o(.text.Start_Simulation_Mode) refers to main.o(.bss.g_filter_type) for g_filter_type
    main.o(.text.Start_Simulation_Mode) refers to lcd.o(.text.lcd_clear) for lcd_clear
    main.o(.text.Start_Simulation_Mode) refers to main.o(.rodata.str1.1) for .L.str.3
    main.o(.text.Start_Simulation_Mode) refers to main.o(.text.LCD_ShowString_Simplified) for LCD_ShowString_Simplified
    main.o(.text.Start_Simulation_Mode) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.Start_Simulation_Mode) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.text.Start_Simulation_Mode) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.Start_Simulation_Mode) refers to tim.o(.bss.htim5) for htim5
    main.o(.text.Start_Simulation_Mode) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for HAL_TIM_IC_Start_IT
    main.o(.text.Start_Simulation_Mode) refers to dac.o(.bss.hdac) for hdac
    main.o(.text.Start_Simulation_Mode) refers to main.o(.bss.g_dac_output_buffer) for g_dac_output_buffer
    main.o(.text.Start_Simulation_Mode) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) for HAL_DAC_Start_DMA
    main.o(.text.Start_Simulation_Mode) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.Start_Simulation_Mode) refers to adc.o(.bss.hadc2) for hadc2
    main.o(.text.Start_Simulation_Mode) refers to main.o(.bss.g_adc2_dma_buffer) for g_adc2_dma_buffer
    main.o(.text.Start_Simulation_Mode) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(.text.Start_Simulation_Mode) refers to main.o(.data.g_current_sampling_simulation) for g_current_sampling_simulation
    main.o(.text.Start_Simulation_Mode) refers to ad9959.o(.text.Write_frequence) for Write_frequence
    main.o(.ARM.exidx.text.Start_Simulation_Mode) refers to main.o(.text.Start_Simulation_Mode) for [Anonymous Symbol]
    main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    main.o(.ARM.exidx.text.Get_Signal_Accurate_Amplitude_And_Phase) refers to main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase) for [Anonymous Symbol]
    main.o(.text.Analyze_Filter_Response) refers to main.o(.bss.g_H_magnitude) for g_H_magnitude
    main.o(.text.Analyze_Filter_Response) refers to f2d.o(.text) for __aeabi_f2d
    main.o(.text.Analyze_Filter_Response) refers to dcmpge.o(.text) for __aeabi_dcmpge
    main.o(.text.Analyze_Filter_Response) refers to main.o(.bss.g_filter_type) for g_filter_type
    main.o(.text.Analyze_Filter_Response) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text.Analyze_Filter_Response) refers to dcmple.o(.text) for __aeabi_dcmple
    main.o(.ARM.exidx.text.Analyze_Filter_Response) refers to main.o(.text.Analyze_Filter_Response) for [Anonymous Symbol]
    main.o(.text.Display_Results) refers to lcd.o(.text.lcd_clear) for lcd_clear
    main.o(.text.Display_Results) refers to main.o(.rodata.str1.1) for .L.str.8
    main.o(.text.Display_Results) refers to main.o(.text.LCD_ShowString_Simplified) for LCD_ShowString_Simplified
    main.o(.text.Display_Results) refers to main.o(.bss.g_filter_type) for g_filter_type
    main.o(.ARM.exidx.text.Display_Results) refers to main.o(.text.Display_Results) for [Anonymous Symbol]
    main.o(.text.Stop_Simulation_Mode) refers to dac.o(.bss.hdac) for hdac
    main.o(.text.Stop_Simulation_Mode) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for HAL_DAC_Stop_DMA
    main.o(.text.Stop_Simulation_Mode) refers to adc.o(.bss.hadc2) for hadc2
    main.o(.text.Stop_Simulation_Mode) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    main.o(.text.Stop_Simulation_Mode) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.text.Stop_Simulation_Mode) refers to lcd.o(.text.lcd_clear) for lcd_clear
    main.o(.text.Stop_Simulation_Mode) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.Stop_Simulation_Mode) refers to main.o(.text.LCD_ShowString_Simplified) for LCD_ShowString_Simplified
    main.o(.ARM.exidx.text.Stop_Simulation_Mode) refers to main.o(.text.Stop_Simulation_Mode) for [Anonymous Symbol]
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc1_processing_buffer) for g_adc1_processing_buffer
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc1_dma_buffer) for g_adc1_dma_buffer
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc1_data_ready) for g_adc1_data_ready
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc2_processing_buffer) for g_adc2_processing_buffer
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc2_dma_buffer) for g_adc2_dma_buffer
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_adc2_data_ready) for g_adc2_data_ready
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.data.g_current_signal_rate) for g_current_signal_rate
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.data.g_current_sampling_simulation) for g_current_sampling_simulation
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.bss.g_dac_output_buffer) for g_dac_output_buffer
    main.o(.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.text.Process_Buffer_DFT_SIM) for Process_Buffer_DFT_SIM
    main.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to main.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    main.o(.text.Process_Buffer_DFT_SIM) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main.o(.text.Process_Buffer_DFT_SIM) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    main.o(.text.Process_Buffer_DFT_SIM) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    main.o(.text.Process_Buffer_DFT_SIM) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    main.o(.text.Process_Buffer_DFT_SIM) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    main.o(.text.Process_Buffer_DFT_SIM) refers to main.o(.bss.g_H_magnitude) for g_H_magnitude
    main.o(.text.Process_Buffer_DFT_SIM) refers to main.o(.bss.g_H_phase) for g_H_phase
    main.o(.ARM.exidx.text.Process_Buffer_DFT_SIM) refers to main.o(.text.Process_Buffer_DFT_SIM) for [Anonymous Symbol]
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_app_state) for g_app_state
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc1_dma_buffer) for g_adc1_dma_buffer
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc1_processing_buffer) for g_adc1_processing_buffer
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc1_data_ready) for g_adc1_data_ready
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc2_dma_buffer) for g_adc2_dma_buffer
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc2_processing_buffer) for g_adc2_processing_buffer
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_adc2_data_ready) for g_adc2_data_ready
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.data.g_current_signal_rate) for g_current_signal_rate
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.data.g_current_sampling_simulation) for g_current_sampling_simulation
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.bss.g_dac_output_buffer) for g_dac_output_buffer
    main.o(.text.HAL_ADC_ConvCpltCallback) refers to main.o(.text.Process_Buffer_DFT_SIM) for Process_Buffer_DFT_SIM
    main.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to main.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss.g_tim5_capture_state) for g_tim5_capture_state
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss.g_tim5_capture_val1) for g_tim5_capture_val1
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss.g_capture_count) for g_capture_count
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss.g_tim5_capture_val2) for g_tim5_capture_val2
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss.g_measured_freq) for g_measured_freq
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.data.g_current_signal_rate) for g_current_signal_rate
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.data.g_current_sampling_simulation) for g_current_sampling_simulation
    main.o(.text.HAL_TIM_IC_CaptureCallback) refers to ad9959.o(.text.Write_frequence) for Write_frequence
    main.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to main.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC1_Init) refers to adc.o(.bss.hadc1) for hadc1
    adc.o(.text.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.ARM.exidx.text.MX_ADC1_Init) refers to adc.o(.text.MX_ADC1_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC2_Init) refers to adc.o(.bss.hadc2) for hadc2
    adc.o(.text.MX_ADC2_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC2_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.ARM.exidx.text.MX_ADC2_Init) refers to adc.o(.text.MX_ADC2_Init) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(.text.HAL_ADC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.hdma_adc2) for hdma_adc2
    adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    dac.o(.text.MX_DAC_Init) refers to dac.o(.bss.hdac) for hdac
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(.text.MX_DAC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(.ARM.exidx.text.MX_DAC_Init) refers to dac.o(.text.MX_DAC_Init) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(.text.HAL_DAC_MspInit) refers to dac.o(.bss.hdma_dac1) for hdma_dac1
    dac.o(.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(.text.HAL_DAC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    fsmc.o(.text.MX_FSMC_Init) refers to fsmc.o(.bss.hsram1) for hsram1
    fsmc.o(.text.MX_FSMC_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for HAL_SRAM_Init
    fsmc.o(.text.MX_FSMC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    fsmc.o(.ARM.exidx.text.MX_FSMC_Init) refers to fsmc.o(.text.MX_FSMC_Init) for [Anonymous Symbol]
    fsmc.o(.text.HAL_SRAM_MspInit) refers to fsmc.o(.text.HAL_FSMC_MspInit) for HAL_FSMC_MspInit
    fsmc.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to fsmc.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    fsmc.o(.text.HAL_FSMC_MspInit) refers to fsmc.o(.bss.FSMC_Initialized) for FSMC_Initialized
    fsmc.o(.text.HAL_FSMC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    fsmc.o(.ARM.exidx.text.HAL_FSMC_MspInit) refers to fsmc.o(.text.HAL_FSMC_MspInit) for [Anonymous Symbol]
    fsmc.o(.text.HAL_SRAM_MspDeInit) refers to fsmc.o(.text.HAL_FSMC_MspDeInit) for HAL_FSMC_MspDeInit
    fsmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to fsmc.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    fsmc.o(.text.HAL_FSMC_MspDeInit) refers to fsmc.o(.bss.FSMC_DeInitialized) for FSMC_DeInitialized
    fsmc.o(.text.HAL_FSMC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fsmc.o(.ARM.exidx.text.HAL_FSMC_MspDeInit) refers to fsmc.o(.text.HAL_FSMC_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM5_Init) refers to tim.o(.bss.htim5) for htim5
    tim.o(.text.MX_TIM5_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(.text.MX_TIM5_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM5_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM5_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(.ARM.exidx.text.MX_TIM5_Init) refers to tim.o(.text.MX_TIM5_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_tx) for hdma_usart1_tx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to dac.o(.bss.hdma_dac1) for hdma_dac1
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream0_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to adc.o(.bss.hdma_adc2) for hdma_adc2
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler) refers to usart.o(.bss.hdma_usart1_tx) for hdma_usart1_tx
    stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream7_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) refers to adc.o(.text.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(.text.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_Init) refers to stm32f4xx_hal_adc.o(.text.ADC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to main.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to main.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) refers to main.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.ADC_DMAError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError) refers to stm32f4xx_hal_adc.o(.text.ADC_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) refers to main.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) refers to main.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel) refers to stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Byte) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Byte) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_HalfWord) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_HalfWord) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Word) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Word) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_DoubleWord) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.text.FLASH_SetErrorCode) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_MassErase) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_MassErase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_BOR_LevelConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetWRP) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetRDP) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetUser) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetUser) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetBOR) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.DMA_MultiBufferSetConfig) refers to stm32f4xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_CheckFifoParam) refers to stm32f4xx_hal_dma.o(.text.DMA_CheckFifoParam) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig) refers to stm32f4xx_hal_dma.o(.text.DMA_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping) for __NVIC_SetPriorityGrouping
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.NVIC_EncodePriority) for NVIC_EncodePriority
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.NVIC_EncodePriority) refers to stm32f4xx_hal_cortex.o(.text.NVIC_EncodePriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_DisableIRQ) for __NVIC_DisableIRQ
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.SysTick_Config) for SysTick_Config
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.SysTick_Config) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.SysTick_Config) refers to stm32f4xx_hal_cortex.o(.text.SysTick_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriority) for __NVIC_GetPriority
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.NVIC_DecodePriority) for NVIC_DecodePriority
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.NVIC_DecodePriority) refers to stm32f4xx_hal_cortex.o(.text.NVIC_DecodePriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ) for __NVIC_SetPendingIRQ
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ) for __NVIC_GetPendingIRQ
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetActive) for __NVIC_GetActive
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) refers to dac.o(.text.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Extended_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_DeInit) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_ECC_Enable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_ECC_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_ECC_Disable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_ECC_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_GetECC) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_GetECC) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_IOSpace_Timing_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_IOSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_DeInit) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to fsmc.o(.text.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to fsmc.o(.text.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to main.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to main.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC3_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC3_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC4_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC4_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI3_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI3_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI4_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI4_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_ConfigInputStage) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ITRx_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ITRx_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_ConfigInputStage) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_CCxNChannelCmd) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndTxTransfer) refers to stm32f4xx_hal_uart.o(.text.UART_EndTxTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndRxTransfer) refers to stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32) for arm_rfft_64_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32) for arm_rfft_1024_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32) for arm_rfft_256_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32) for arm_rfft_2048_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32) for arm_rfft_32_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32) for arm_rfft_512_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32) for arm_rfft_128_fast_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32) for arm_rfft_4096_fast_init_f32
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_4096) for twiddleCoef_rfft_4096
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_4096_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_2048) for twiddleCoef_rfft_2048
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_2048_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_1024) for twiddleCoef_rfft_1024
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_1024_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_512) for twiddleCoef_rfft_512
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_512_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_256) for twiddleCoef_rfft_256
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_256_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_128) for twiddleCoef_rfft_128
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_128_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_64) for twiddleCoef_rfft_64
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_64_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32) for [Anonymous Symbol]
    arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for arm_cfft_init_f32
    arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_32) for twiddleCoef_rfft_32
    arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_32_fast_init_f32) refers to arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32) for [Anonymous Symbol]
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len2048) for arm_cfft_sR_f32_len2048
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len32) for arm_cfft_sR_f32_len32
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len512) for arm_cfft_sR_f32_len512
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len128) for arm_cfft_sR_f32_len128
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len1024) for arm_cfft_sR_f32_len1024
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len4096) for arm_cfft_sR_f32_len4096
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len16) for arm_cfft_sR_f32_len16
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len256) for arm_cfft_sR_f32_len256
    arm_cfft_init_f32.o(.text.arm_cfft_init_f32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_f32_len64) for arm_cfft_sR_f32_len64
    arm_cfft_init_f32.o(.ARM.exidx.text.arm_cfft_init_f32) refers to arm_cfft_init_f32.o(.text.arm_cfft_init_f32) for [Anonymous Symbol]
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len16) refers to arm_common_tables.o(.rodata.twiddleCoefF64_16) for twiddleCoefF64_16
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len16) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_16) for armBitRevIndexTableF64_16
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len32) refers to arm_common_tables.o(.rodata.twiddleCoefF64_32) for twiddleCoefF64_32
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_32) for armBitRevIndexTableF64_32
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len64) refers to arm_common_tables.o(.rodata.twiddleCoefF64_64) for twiddleCoefF64_64
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_64) for armBitRevIndexTableF64_64
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len128) refers to arm_common_tables.o(.rodata.twiddleCoefF64_128) for twiddleCoefF64_128
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_128) for armBitRevIndexTableF64_128
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len256) refers to arm_common_tables.o(.rodata.twiddleCoefF64_256) for twiddleCoefF64_256
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_256) for armBitRevIndexTableF64_256
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len512) refers to arm_common_tables.o(.rodata.twiddleCoefF64_512) for twiddleCoefF64_512
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_512) for armBitRevIndexTableF64_512
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len1024) refers to arm_common_tables.o(.rodata.twiddleCoefF64_1024) for twiddleCoefF64_1024
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024) for armBitRevIndexTableF64_1024
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len2048) refers to arm_common_tables.o(.rodata.twiddleCoefF64_2048) for twiddleCoefF64_2048
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048) for armBitRevIndexTableF64_2048
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len4096) refers to arm_common_tables.o(.rodata.twiddleCoefF64_4096) for twiddleCoefF64_4096
    arm_const_structs.o(.rodata.arm_cfft_sR_f64_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096) for armBitRevIndexTableF64_4096
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len16) refers to arm_common_tables.o(.rodata.twiddleCoef_16) for twiddleCoef_16
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len16) refers to arm_common_tables.o(.rodata.armBitRevIndexTable16) for armBitRevIndexTable16
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len32) refers to arm_common_tables.o(.rodata.twiddleCoef_32) for twiddleCoef_32
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTable32) for armBitRevIndexTable32
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len64) refers to arm_common_tables.o(.rodata.twiddleCoef_64) for twiddleCoef_64
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTable64) for armBitRevIndexTable64
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len128) refers to arm_common_tables.o(.rodata.twiddleCoef_128) for twiddleCoef_128
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTable128) for armBitRevIndexTable128
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len256) refers to arm_common_tables.o(.rodata.twiddleCoef_256) for twiddleCoef_256
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTable256) for armBitRevIndexTable256
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len512) refers to arm_common_tables.o(.rodata.twiddleCoef_512) for twiddleCoef_512
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTable512) for armBitRevIndexTable512
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len1024) refers to arm_common_tables.o(.rodata.twiddleCoef_1024) for twiddleCoef_1024
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTable1024) for armBitRevIndexTable1024
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len2048) refers to arm_common_tables.o(.rodata.twiddleCoef_2048) for twiddleCoef_2048
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTable2048) for armBitRevIndexTable2048
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len4096) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_const_structs.o(.rodata.arm_cfft_sR_f32_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTable4096) for armBitRevIndexTable4096
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len16) refers to arm_common_tables.o(.rodata.twiddleCoef_16_q31) for twiddleCoef_16_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len16) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16) for armBitRevIndexTable_fixed_16
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len32) refers to arm_common_tables.o(.rodata.twiddleCoef_32_q31) for twiddleCoef_32_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32) for armBitRevIndexTable_fixed_32
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len64) refers to arm_common_tables.o(.rodata.twiddleCoef_64_q31) for twiddleCoef_64_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64) for armBitRevIndexTable_fixed_64
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len128) refers to arm_common_tables.o(.rodata.twiddleCoef_128_q31) for twiddleCoef_128_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128) for armBitRevIndexTable_fixed_128
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len256) refers to arm_common_tables.o(.rodata.twiddleCoef_256_q31) for twiddleCoef_256_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256) for armBitRevIndexTable_fixed_256
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len512) refers to arm_common_tables.o(.rodata.twiddleCoef_512_q31) for twiddleCoef_512_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512) for armBitRevIndexTable_fixed_512
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len1024) refers to arm_common_tables.o(.rodata.twiddleCoef_1024_q31) for twiddleCoef_1024_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024) for armBitRevIndexTable_fixed_1024
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len2048) refers to arm_common_tables.o(.rodata.twiddleCoef_2048_q31) for twiddleCoef_2048_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048) for armBitRevIndexTable_fixed_2048
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len4096) refers to arm_common_tables.o(.rodata.twiddleCoef_4096_q31) for twiddleCoef_4096_q31
    arm_const_structs.o(.rodata.arm_cfft_sR_q31_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096) for armBitRevIndexTable_fixed_4096
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len16) refers to arm_common_tables.o(.rodata.twiddleCoef_16_q15) for twiddleCoef_16_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len16) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16) for armBitRevIndexTable_fixed_16
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len32) refers to arm_common_tables.o(.rodata.twiddleCoef_32_q15) for twiddleCoef_32_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32) for armBitRevIndexTable_fixed_32
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len64) refers to arm_common_tables.o(.rodata.twiddleCoef_64_q15) for twiddleCoef_64_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64) for armBitRevIndexTable_fixed_64
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len128) refers to arm_common_tables.o(.rodata.twiddleCoef_128_q15) for twiddleCoef_128_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128) for armBitRevIndexTable_fixed_128
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len256) refers to arm_common_tables.o(.rodata.twiddleCoef_256_q15) for twiddleCoef_256_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256) for armBitRevIndexTable_fixed_256
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len512) refers to arm_common_tables.o(.rodata.twiddleCoef_512_q15) for twiddleCoef_512_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512) for armBitRevIndexTable_fixed_512
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len1024) refers to arm_common_tables.o(.rodata.twiddleCoef_1024_q15) for twiddleCoef_1024_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024) for armBitRevIndexTable_fixed_1024
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len2048) refers to arm_common_tables.o(.rodata.twiddleCoef_2048_q15) for twiddleCoef_2048_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048) for armBitRevIndexTable_fixed_2048
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len4096) refers to arm_common_tables.o(.rodata.twiddleCoef_4096_q15) for twiddleCoef_4096_q15
    arm_const_structs.o(.rodata.arm_cfft_sR_q15_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096) for armBitRevIndexTable_fixed_4096
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len32) refers to arm_common_tables.o(.rodata.twiddleCoefF64_16) for twiddleCoefF64_16
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_16) for armBitRevIndexTableF64_16
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len32) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32) for twiddleCoefF64_rfft_32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len64) refers to arm_common_tables.o(.rodata.twiddleCoefF64_32) for twiddleCoefF64_32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_32) for armBitRevIndexTableF64_32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len64) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64) for twiddleCoefF64_rfft_64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len128) refers to arm_common_tables.o(.rodata.twiddleCoefF64_64) for twiddleCoefF64_64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_64) for armBitRevIndexTableF64_64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len128) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128) for twiddleCoefF64_rfft_128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len256) refers to arm_common_tables.o(.rodata.twiddleCoefF64_128) for twiddleCoefF64_128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_128) for armBitRevIndexTableF64_128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len256) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256) for twiddleCoefF64_rfft_256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len512) refers to arm_common_tables.o(.rodata.twiddleCoefF64_256) for twiddleCoefF64_256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_256) for armBitRevIndexTableF64_256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len512) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512) for twiddleCoefF64_rfft_512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len1024) refers to arm_common_tables.o(.rodata.twiddleCoefF64_512) for twiddleCoefF64_512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_512) for armBitRevIndexTableF64_512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len1024) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024) for twiddleCoefF64_rfft_1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len2048) refers to arm_common_tables.o(.rodata.twiddleCoefF64_1024) for twiddleCoefF64_1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024) for armBitRevIndexTableF64_1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len2048) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048) for twiddleCoefF64_rfft_2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len4096) refers to arm_common_tables.o(.rodata.twiddleCoefF64_2048) for twiddleCoefF64_2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048) for armBitRevIndexTableF64_2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len4096) refers to arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096) for twiddleCoefF64_rfft_4096
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len32) refers to arm_common_tables.o(.rodata.twiddleCoef_16) for twiddleCoef_16
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len32) refers to arm_common_tables.o(.rodata.armBitRevIndexTable16) for armBitRevIndexTable16
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len32) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_32) for twiddleCoef_rfft_32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len64) refers to arm_common_tables.o(.rodata.twiddleCoef_32) for twiddleCoef_32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len64) refers to arm_common_tables.o(.rodata.armBitRevIndexTable32) for armBitRevIndexTable32
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len64) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_64) for twiddleCoef_rfft_64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len128) refers to arm_common_tables.o(.rodata.twiddleCoef_64) for twiddleCoef_64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len128) refers to arm_common_tables.o(.rodata.armBitRevIndexTable64) for armBitRevIndexTable64
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len128) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_128) for twiddleCoef_rfft_128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len256) refers to arm_common_tables.o(.rodata.twiddleCoef_128) for twiddleCoef_128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len256) refers to arm_common_tables.o(.rodata.armBitRevIndexTable128) for armBitRevIndexTable128
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len256) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_256) for twiddleCoef_rfft_256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len512) refers to arm_common_tables.o(.rodata.twiddleCoef_256) for twiddleCoef_256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len512) refers to arm_common_tables.o(.rodata.armBitRevIndexTable256) for armBitRevIndexTable256
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len512) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_512) for twiddleCoef_rfft_512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len1024) refers to arm_common_tables.o(.rodata.twiddleCoef_512) for twiddleCoef_512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len1024) refers to arm_common_tables.o(.rodata.armBitRevIndexTable512) for armBitRevIndexTable512
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len1024) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_1024) for twiddleCoef_rfft_1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len2048) refers to arm_common_tables.o(.rodata.twiddleCoef_1024) for twiddleCoef_1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len2048) refers to arm_common_tables.o(.rodata.armBitRevIndexTable1024) for armBitRevIndexTable1024
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len2048) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_2048) for twiddleCoef_rfft_2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len4096) refers to arm_common_tables.o(.rodata.twiddleCoef_2048) for twiddleCoef_2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len4096) refers to arm_common_tables.o(.rodata.armBitRevIndexTable2048) for armBitRevIndexTable2048
    arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len4096) refers to arm_common_tables.o(.rodata.twiddleCoef_rfft_4096) for twiddleCoef_rfft_4096
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len32) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len32) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len16) for arm_cfft_sR_q31_len16
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len64) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len64) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len64) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len32) for arm_cfft_sR_q31_len32
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len128) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len128) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len128) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len64) for arm_cfft_sR_q31_len64
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len256) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len256) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len256) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len128) for arm_cfft_sR_q31_len128
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len512) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len512) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len512) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len256) for arm_cfft_sR_q31_len256
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len1024) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len1024) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len1024) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len512) for arm_cfft_sR_q31_len512
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len2048) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len2048) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len2048) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len1024) for arm_cfft_sR_q31_len1024
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len4096) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len4096) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len4096) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len2048) for arm_cfft_sR_q31_len2048
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len8192) refers to arm_common_tables.o(.rodata.realCoefAQ31) for realCoefAQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len8192) refers to arm_common_tables.o(.rodata.realCoefBQ31) for realCoefBQ31
    arm_const_structs.o(.rodata.arm_rfft_sR_q31_len8192) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q31_len4096) for arm_cfft_sR_q31_len4096
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len32) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len32) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len32) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len16) for arm_cfft_sR_q15_len16
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len64) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len64) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len64) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len32) for arm_cfft_sR_q15_len32
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len128) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len128) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len128) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len64) for arm_cfft_sR_q15_len64
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len256) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len256) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len256) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len128) for arm_cfft_sR_q15_len128
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len512) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len512) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len512) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len256) for arm_cfft_sR_q15_len256
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len1024) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len1024) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len1024) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len512) for arm_cfft_sR_q15_len512
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len2048) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len2048) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len2048) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len1024) for arm_cfft_sR_q15_len1024
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len4096) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len4096) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len4096) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len2048) for arm_cfft_sR_q15_len2048
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len8192) refers to arm_common_tables.o(.rodata.realCoefAQ15) for realCoefAQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len8192) refers to arm_common_tables.o(.rodata.realCoefBQ15) for realCoefBQ15
    arm_const_structs.o(.rodata.arm_rfft_sR_q15_len8192) refers to arm_const_structs.o(.rodata.arm_cfft_sR_q15_len4096) for arm_cfft_sR_q15_len4096
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing lcd.o(.text), (0 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_st7789_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_wr_regno), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_wr_data), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_ili9341_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_nt35310_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_st7796_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_nt35510_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_write_reg), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_ili9806_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ex_ssd1963_reginit), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_write_ram_prepare), (8 bytes).
    Removing lcd.o(.text.lcd_read_point), (260 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_read_point), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_set_cursor), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_rd_data), (8 bytes).
    Removing lcd.o(.text.lcd_display_on), (42 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_display_on), (8 bytes).
    Removing lcd.o(.text.lcd_display_off), (42 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_display_off), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_scan_dir), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_draw_point), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_ssd_backlight_set), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_display_dir), (8 bytes).
    Removing lcd.o(.text.lcd_set_window), (512 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_set_window), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_init), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_clear), (8 bytes).
    Removing lcd.o(.text.lcd_fill), (150 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_fill), (8 bytes).
    Removing lcd.o(.text.lcd_color_fill), (186 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_color_fill), (8 bytes).
    Removing lcd.o(.text.lcd_draw_line), (304 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_draw_line), (8 bytes).
    Removing lcd.o(.text.lcd_draw_hline), (110 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_draw_hline), (8 bytes).
    Removing lcd.o(.text.lcd_draw_rectangle), (144 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_draw_rectangle), (8 bytes).
    Removing lcd.o(.text.lcd_draw_circle), (340 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_draw_circle), (8 bytes).
    Removing lcd.o(.text.lcd_fill_circle), (316 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_fill_circle), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_show_char), (8 bytes).
    Removing lcd.o(.text.lcd_show_num), (280 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_show_num), (8 bytes).
    Removing lcd.o(.text.lcd_pow), (48 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_pow), (8 bytes).
    Removing lcd.o(.text.lcd_show_xnum), (372 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_show_xnum), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_show_string), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.lcd_opt_delay), (8 bytes).
    Removing lcd.o(.data.g_point_color), (4 bytes).
    Removing ad9959.o(.text), (0 bytes).
    Removing ad9959.o(.ARM.exidx.text.IntReset), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.IO_Update), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.Intserve), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.WriteData_AD9959), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.Init_AD9959), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.Write_frequence), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.Write_Amplitude), (8 bytes).
    Removing ad9959.o(.text.Write_Phase), (224 bytes).
    Removing ad9959.o(.ARM.exidx.text.Write_Phase), (8 bytes).
    Removing ad9959.o(.data.CFR_DATA), (3 bytes).
    Removing ad9959.o(.bss.CPOW0_DATA), (2 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.exidx.text.LCD_ShowString_Simplified), (8 bytes).
    Removing main.o(.ARM.exidx.text.Generate_Flat_Top_Window), (8 bytes).
    Removing main.o(.ARM.exidx.text.Generate_Hanning_Window), (8 bytes).
    Removing main.o(.ARM.exidx.text.Start_Sweep_And_Analysis), (8 bytes).
    Removing main.o(.ARM.exidx.text.Start_Simulation_Mode), (8 bytes).
    Removing main.o(.ARM.exidx.text.Get_Signal_Accurate_Amplitude_And_Phase), (8 bytes).
    Removing main.o(.ARM.exidx.text.Analyze_Filter_Response), (8 bytes).
    Removing main.o(.ARM.exidx.text.Display_Results), (8 bytes).
    Removing main.o(.ARM.exidx.text.Stop_Simulation_Mode), (8 bytes).
    Removing main.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing main.o(.ARM.exidx.text.Process_Buffer_DFT_SIM), (8 bytes).
    Removing main.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing main.o(.text.HAL_TIM_IC_CaptureCallback), (440 bytes).
    Removing main.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing main.o(.bss.g_tim5_capture_val1), (4 bytes).
    Removing main.o(.bss.g_tim5_capture_val2), (4 bytes).
    Removing main.o(.bss.g_tim5_capture_state), (1 bytes).
    Removing main.o(.bss.g_capture_count), (4 bytes).
    Removing main.o(.bss.g_measured_freq), (4 bytes).
    Removing main.o(.bss.g_fft_input_buffer), (4096 bytes).
    Removing main.o(.bss.g_fft_output_buffer), (4096 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC1_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC2_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing adc.o(.text.HAL_ADC_MspDeInit), (124 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing dac.o(.text), (0 bytes).
    Removing dac.o(.ARM.exidx.text.MX_DAC_Init), (8 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing dac.o(.text.HAL_DAC_MspDeInit), (66 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing fsmc.o(.text), (0 bytes).
    Removing fsmc.o(.ARM.exidx.text.MX_FSMC_Init), (8 bytes).
    Removing fsmc.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing fsmc.o(.ARM.exidx.text.HAL_FSMC_MspInit), (8 bytes).
    Removing fsmc.o(.text.HAL_SRAM_MspDeInit), (14 bytes).
    Removing fsmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing fsmc.o(.text.HAL_FSMC_MspDeInit), (118 bytes).
    Removing fsmc.o(.ARM.exidx.text.HAL_FSMC_MspDeInit), (8 bytes).
    Removing fsmc.o(.bss.FSMC_DeInitialized), (4 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM5_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_IC_MspDeInit), (58 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (74 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream0_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream7_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_Init), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_DeInit), (108 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Start), (464 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop), (108 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForConversion), (298 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_PollForEvent), (198 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_IT), (480 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_IT), (126 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler), (540 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig), (184 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetState), (12 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.text.HAL_ADC_GetError), (12 bytes).
    Removing stm32f4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart), (398 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT), (410 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop), (162 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion), (264 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT), (174 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA), (430 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAConvCplt), (156 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAHalfConvCplt), (22 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAHalfConvCplt), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.ADC_MultiModeDMAError), (38 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.ADC_MultiModeDMAError), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA), (162 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue), (22 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel), (722 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel), (8 bytes).
    Removing stm32f4xx_ll_adc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (208 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (320 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (84 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (564 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (80 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (198 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (176 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (80 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (512 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (210 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (164 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_Program_Byte), (48 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Byte), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_Program_HalfWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_HalfWord), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_Program_Word), (48 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Word), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_Program_DoubleWord), (60 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_DoubleWord), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (138 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (394 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_SetErrorCode), (242 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (86 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (72 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (264 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_MassErase), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_MassErase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (132 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (162 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (138 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (232 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP), (60 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_EnableWRP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP), (60 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_DisableWRP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig), (58 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig), (96 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_UserConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig), (38 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_BOR_LevelConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetWRP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP), (74 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetRDP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetUser), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetBOR), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (548 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (38 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (86 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (56 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (176 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig), (68 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.DMA_MultiBufferSetConfig), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (8244 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (44 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_CheckFifoParam), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_CalcBaseAndBitshift), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (180 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (506 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (182 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (210 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (14 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (12 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (210 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (62 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (98 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (92 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (90 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (156 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.NVIC_EncodePriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (20 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_DisableIRQ), (56 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (128 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.NVIC_DecodePriority), (118 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.NVIC_DecodePriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriority), (66 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (20 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (20 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ), (64 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (20 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (20 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_GetActive), (64 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (52 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (106 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (106 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (358 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (268 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (196 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (48 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (76 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (50 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (34 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit), (68 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Start), (190 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop), (62 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler), (170 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue), (86 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue), (42 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState), (12 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError), (12 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart), (180 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop), (64 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate), (126 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate), (126 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue), (94 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue), (40 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text), (0 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_DeInit), (86 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_Extended_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Enable), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_WriteOperation_Disable), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NORSRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_Init), (148 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_CommonSpace_Timing_Init), (78 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_AttributeSpace_Timing_Init), (78 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_DeInit), (104 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_ECC_Enable), (44 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_ECC_Enable), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_ECC_Disable), (44 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_ECC_Disable), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_NAND_GetECC), (144 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_NAND_GetECC), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_Init), (52 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_CommonSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_AttributeSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_IOSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_IOSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fsmc.o(.text.FSMC_PCCARD_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fsmc.o(.ARM.exidx.text.FSMC_PCCARD_DeInit), (8 bytes).
    Removing stm32f4xx_hal_nor.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sram.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit), (50 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b), (180 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b), (160 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b), (230 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b), (226 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b), (180 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b), (160 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA), (198 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMACplt), (42 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACplt), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMACpltProt), (42 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMACpltProt), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.SRAM_DMAError), (42 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.SRAM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA), (152 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable), (116 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable), (116 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState), (14 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_nand.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pccard.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (166 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (234 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (70 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (246 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (82 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (374 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (42 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (154 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (166 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (458 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (262 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (572 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (374 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (986 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (116 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (406 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (166 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (458 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (262 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (572 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (374 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (986 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (406 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (166 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (566 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (234 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (346 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (1018 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (212 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (116 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (376 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (144 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (128 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (224 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (254 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (248 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (304 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (128 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (338 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (382 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (386 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (896 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (418 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (538 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (164 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC1_SetConfig), (234 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (244 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC3_SetConfig), (242 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC3_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC4_SetConfig), (164 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC4_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI3_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI4_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel), (322 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (394 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (62 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (736 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (42 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (202 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (62 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (736 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (202 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (384 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource), (388 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_ConfigInputStage), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ITRx_SetConfig), (42 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ITRx_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage), (82 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_ConfigInputStage), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (44 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (296 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (86 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (12 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (14 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (334 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (346 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (114 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (110 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (402 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_CCxNChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (508 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (338 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (836 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (148 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (116 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (326 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (402 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (508 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (338 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (836 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (326 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (186 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (182 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (206 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (186 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (186 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (14 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (94 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (158 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (184 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (202 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit), (304 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout), (218 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (318 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (86 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (98 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (252 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (122 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (22 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAError), (124 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (86 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (304 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (230 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (226 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (204 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_EndTxTransfer), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndTxTransfer), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndRxTransfer), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (448 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (194 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (184 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (12 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (406 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (186 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (278 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (86 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (86 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (194 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (36 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (290 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_EndTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (126 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (126 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (118 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (118 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (40 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (12 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt), (244 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt), (52 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (288 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.text), (0 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_4096_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_2048_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_1024_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_512_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_256_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_128_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_64_fast_init_f32), (8 bytes).
    Removing arm_rfft_fast_init_f32.o(.ARM.exidx.text.arm_rfft_32_fast_init_f32), (8 bytes).
    Removing arm_cfft_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_init_f32.o(.ARM.exidx.text.arm_cfft_init_f32), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevTable), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_f32), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing arm_const_structs.o(.text), (0 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len16), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len32), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len64), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len128), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len256), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len512), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len1024), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len2048), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_f64_len4096), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len16), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len32), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len64), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len128), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len256), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len512), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len1024), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len2048), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q31_len4096), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len16), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len32), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len64), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len128), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len256), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len512), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len1024), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len2048), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_cfft_sR_q15_len4096), (16 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len32), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len64), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len128), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len256), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len512), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len1024), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len2048), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f64_len4096), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len32), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len64), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len128), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len256), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len512), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len1024), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len2048), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_fast_sR_f32_len4096), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len32), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len64), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len128), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len256), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len512), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len1024), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len2048), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len4096), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q31_len8192), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len32), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len64), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len128), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len256), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len512), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len1024), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len2048), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len4096), (24 bytes).
    Removing arm_const_structs.o(.rodata.arm_rfft_sR_q15_len8192), (24 bytes).

1274 unused section(s) (total 887552 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ad9959.c                                 0x00000000   Number         0  ad9959.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    arm_cfft_init_f32.c                      0x00000000   Number         0  arm_cfft_init_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_const_structs.c                      0x00000000   Number         0  arm_const_structs.o ABSOLUTE
    arm_rfft_fast_init_f32.c                 0x00000000   Number         0  arm_rfft_fast_init_f32.o ABSOLUTE
    dac.c                                    0x00000000   Number         0  dac.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    fsmc.c                                   0x00000000   Number         0  fsmc.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    lcd.c                                    0x00000000   Number         0  lcd.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_adc.c                      0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    stm32f4xx_hal_adc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dac.c                      0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    stm32f4xx_hal_dac_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_nand.c                     0x00000000   Number         0  stm32f4xx_hal_nand.o ABSOLUTE
    stm32f4xx_hal_nor.c                      0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    stm32f4xx_hal_pccard.c                   0x00000000   Number         0  stm32f4xx_hal_pccard.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_sram.c                     0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_ll_adc.c                       0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    stm32f4xx_ll_fsmc.c                      0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memcpya.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  dmul.o(.text)
    .text                                    0x0800034e   Section        0  dcmple.o(.text)
    .text                                    0x08000384   Section        0  dcmpge.o(.text)
    .text                                    0x080003ba   Section        0  dflti.o(.text)
    .text                                    0x080003dc   Section        0  dfltui.o(.text)
    .text                                    0x080003f6   Section        0  dfixi.o(.text)
    .text                                    0x08000434   Section        0  dfixui.o(.text)
    .text                                    0x08000466   Section        0  f2d.o(.text)
    .text                                    0x0800048c   Section        0  llshl.o(.text)
    .text                                    0x080004aa   Section        0  llushr.o(.text)
    .text                                    0x080004ca   Section        0  iusefp.o(.text)
    .text                                    0x080004ca   Section        0  frnd.o(.text)
    .text                                    0x08000506   Section        0  depilogue.o(.text)
    .text                                    0x080005c0   Section       36  init.o(.text)
    .text                                    0x080005e4   Section        0  fepilogue.o(.text)
    ADC_DMAConvCplt                          0x08000655   Thumb Code   178  stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    [Anonymous Symbol]                       0x08000654   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    ADC_DMAError                             0x08000709   Thumb Code    38  stm32f4xx_hal_adc.o(.text.ADC_DMAError)
    [Anonymous Symbol]                       0x08000708   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x08000731   Thumb Code    22  stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    [Anonymous Symbol]                       0x08000730   Section        0  stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    ADC_Init                                 0x08000749   Thumb Code   394  stm32f4xx_hal_adc.o(.text.ADC_Init)
    [Anonymous Symbol]                       0x08000748   Section        0  stm32f4xx_hal_adc.o(.text.ADC_Init)
    [Anonymous Symbol]                       0x080008d8   Section        0  main.o(.text.Analyze_Filter_Response)
    [Anonymous Symbol]                       0x08000aa4   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000aa8   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1)
    [Anonymous Symbol]                       0x08000ac4   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2)
    [Anonymous Symbol]                       0x08000ae0   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1)
    [Anonymous Symbol]                       0x08000b08   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2)
    [Anonymous Symbol]                       0x08000b30   Section        0  stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1)
    [Anonymous Symbol]                       0x08000b48   Section        0  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2)
    [Anonymous Symbol]                       0x08000b60   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x08000b70   Section        0  stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler)
    [Anonymous Symbol]                       0x08000b80   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08000b90   Section        0  stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler)
    DMA_CalcBaseAndBitshift                  0x08000ba1   Thumb Code    86  stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift)
    [Anonymous Symbol]                       0x08000ba0   Section        0  stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift)
    DMA_CheckFifoParam                       0x08000bf9   Thumb Code   240  stm32f4xx_hal_dma.o(.text.DMA_CheckFifoParam)
    [Anonymous Symbol]                       0x08000bf8   Section        0  stm32f4xx_hal_dma.o(.text.DMA_CheckFifoParam)
    DMA_SetConfig                            0x08000ce9   Thumb Code    80  stm32f4xx_hal_dma.o(.text.DMA_SetConfig)
    [Anonymous Symbol]                       0x08000ce8   Section        0  stm32f4xx_hal_dma.o(.text.DMA_SetConfig)
    [Anonymous Symbol]                       0x08000d38   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000d3c   Section        0  main.o(.text.Display_Results)
    [Anonymous Symbol]                       0x08000dc4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08000dcc   Section        0  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init)
    [Anonymous Symbol]                       0x08000e30   Section        0  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Init)
    [Anonymous Symbol]                       0x08000eec   Section        0  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Timing_Init)
    [Anonymous Symbol]                       0x08000f38   Section        0  main.o(.text.Generate_Flat_Top_Window)
    [Anonymous Symbol]                       0x08001088   Section        0  main.o(.text.Generate_Hanning_Window)
    [Anonymous Symbol]                       0x080010f4   Section        0  main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase)
    [Anonymous Symbol]                       0x08001288   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    [Anonymous Symbol]                       0x080014a0   Section        0  main.o(.text.HAL_ADC_ConvCpltCallback)
    [Anonymous Symbol]                       0x0800158c   Section        0  main.o(.text.HAL_ADC_ConvHalfCpltCallback)
    [Anonymous Symbol]                       0x0800166c   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    [Anonymous Symbol]                       0x08001674   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Init)
    [Anonymous Symbol]                       0x0800171c   Section        0  adc.o(.text.HAL_ADC_MspInit)
    [Anonymous Symbol]                       0x080018d8   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    [Anonymous Symbol]                       0x08001b28   Section        0  stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA)
    [Anonymous Symbol]                       0x08001be8   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2)
    [Anonymous Symbol]                       0x08001bf0   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2)
    [Anonymous Symbol]                       0x08001bf8   Section        0  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2)
    [Anonymous Symbol]                       0x08001c00   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    [Anonymous Symbol]                       0x08001cc0   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1)
    [Anonymous Symbol]                       0x08001cc8   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1)
    [Anonymous Symbol]                       0x08001cd0   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1)
    [Anonymous Symbol]                       0x08001cd8   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    [Anonymous Symbol]                       0x08001d24   Section        0  dac.o(.text.HAL_DAC_MspInit)
    [Anonymous Symbol]                       0x08001e0c   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
    [Anonymous Symbol]                       0x08001fb0   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA)
    [Anonymous Symbol]                       0x0800203c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08002128   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x0800216c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x0800248c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x080025fc   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x080026c0   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_FSMC_MspInit                         0x08002705   Thumb Code   258  fsmc.o(.text.HAL_FSMC_MspInit)
    [Anonymous Symbol]                       0x08002704   Section        0  fsmc.o(.text.HAL_FSMC_MspInit)
    [Anonymous Symbol]                       0x08002808   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08002bc0   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x08002bf0   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08002c20   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08002c2c   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08002c48   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08002c80   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08002cf0   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08002d3c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08002d50   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08002d84   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08002d94   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08002ff8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq)
    [Anonymous Symbol]                       0x08003004   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08003028   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x0800304c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08003134   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x080037f4   Section        0  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    [Anonymous Symbol]                       0x08003874   Section        0  fsmc.o(.text.HAL_SRAM_MspInit)
    [Anonymous Symbol]                       0x08003884   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08003894   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x080039b8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    [Anonymous Symbol]                       0x08003ae8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    [Anonymous Symbol]                       0x08003b84   Section        0  tim.o(.text.HAL_TIM_IC_MspInit)
    [Anonymous Symbol]                       0x08003c10   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT)
    [Anonymous Symbol]                       0x08003eb8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08003ec4   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08003ecc   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08004328   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x080043c8   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x080044c0   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x080044c8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x080044d0   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080044d4   Section        0  ad9959.o(.text.IO_Update)
    [Anonymous Symbol]                       0x08004514   Section        0  ad9959.o(.text.Init_AD9959)
    [Anonymous Symbol]                       0x08004574   Section        0  ad9959.o(.text.IntReset)
    [Anonymous Symbol]                       0x080045b8   Section        0  ad9959.o(.text.Intserve)
    [Anonymous Symbol]                       0x08004668   Section        0  main.o(.text.LCD_ShowString_Simplified)
    [Anonymous Symbol]                       0x080046ac   Section        0  adc.o(.text.MX_ADC1_Init)
    [Anonymous Symbol]                       0x0800472c   Section        0  adc.o(.text.MX_ADC2_Init)
    [Anonymous Symbol]                       0x080047b0   Section        0  dac.o(.text.MX_DAC_Init)
    [Anonymous Symbol]                       0x08004800   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08004898   Section        0  fsmc.o(.text.MX_FSMC_Init)
    [Anonymous Symbol]                       0x08004948   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08004bec   Section        0  tim.o(.text.MX_TIM5_Init)
    [Anonymous Symbol]                       0x08004c78   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08004cb8   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08004cbc   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    NVIC_EncodePriority                      0x08004cc1   Thumb Code   108  stm32f4xx_hal_cortex.o(.text.NVIC_EncodePriority)
    [Anonymous Symbol]                       0x08004cc0   Section        0  stm32f4xx_hal_cortex.o(.text.NVIC_EncodePriority)
    [Anonymous Symbol]                       0x08004d2c   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08004d30   Section        0  main.o(.text.Process_Buffer_DFT_SIM)
    [Anonymous Symbol]                       0x080050f4   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080050f8   Section        0  main.o(.text.Start_Simulation_Mode)
    [Anonymous Symbol]                       0x0800524c   Section        0  main.o(.text.Start_Sweep_And_Analysis)
    [Anonymous Symbol]                       0x08005358   Section        0  main.o(.text.Stop_Simulation_Mode)
    SysTick_Config                           0x080053b9   Thumb Code    82  stm32f4xx_hal_cortex.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x080053b8   Section        0  stm32f4xx_hal_cortex.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x0800540c   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08005414   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x080054d4   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x080054e8   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x0800568c   Section        0  stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd)
    [Anonymous Symbol]                       0x080056c4   Section        0  stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig)
    TIM_TI2_SetConfig                        0x080057dd   Thumb Code   108  stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig)
    [Anonymous Symbol]                       0x080057dc   Section        0  stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig)
    TIM_TI3_SetConfig                        0x08005849   Thumb Code   106  stm32f4xx_hal_tim.o(.text.TIM_TI3_SetConfig)
    [Anonymous Symbol]                       0x08005848   Section        0  stm32f4xx_hal_tim.o(.text.TIM_TI3_SetConfig)
    TIM_TI4_SetConfig                        0x080058b5   Thumb Code   108  stm32f4xx_hal_tim.o(.text.TIM_TI4_SetConfig)
    [Anonymous Symbol]                       0x080058b4   Section        0  stm32f4xx_hal_tim.o(.text.TIM_TI4_SetConfig)
    UART_DMAAbortOnError                     0x08005921   Thumb Code    28  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08005920   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_EndRxTransfer                       0x0800593d   Thumb Code   148  stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer)
    [Anonymous Symbol]                       0x0800593c   Section        0  stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer)
    UART_EndTransmit_IT                      0x080059d1   Thumb Code    38  stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT)
    [Anonymous Symbol]                       0x080059d0   Section        0  stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT)
    UART_Receive_IT                          0x080059f9   Thumb Code   360  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x080059f8   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08005b61   Thumb Code   314  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08005b60   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    UART_Transmit_IT                         0x08005c9d   Thumb Code   148  stm32f4xx_hal_uart.o(.text.UART_Transmit_IT)
    [Anonymous Symbol]                       0x08005c9c   Section        0  stm32f4xx_hal_uart.o(.text.UART_Transmit_IT)
    [Anonymous Symbol]                       0x08005d30   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08005d40   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08005d44   Section        0  ad9959.o(.text.WriteData_AD9959)
    [Anonymous Symbol]                       0x08005ed4   Section        0  ad9959.o(.text.Write_Amplitude)
    [Anonymous Symbol]                       0x08005f98   Section        0  ad9959.o(.text.Write_frequence)
    __NVIC_EnableIRQ                         0x08006079   Thumb Code    48  stm32f4xx_hal_cortex.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08006078   Section        0  stm32f4xx_hal_cortex.o(.text.__NVIC_EnableIRQ)
    __NVIC_GetPriorityGrouping               0x080060a9   Thumb Code    16  stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping)
    [Anonymous Symbol]                       0x080060a8   Section        0  stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping)
    __NVIC_SetPriority                       0x080060b9   Thumb Code    66  stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x080060b8   Section        0  stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority)
    __NVIC_SetPriorityGrouping               0x080060fd   Thumb Code    60  stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x080060fc   Section        0  stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08006138   Section        0  arm_cfft_init_f32.o(.text.arm_cfft_init_f32)
    arm_rfft_1024_fast_init_f32              0x080061ed   Thumb Code    42  arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32)
    [Anonymous Symbol]                       0x080061ec   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32)
    arm_rfft_128_fast_init_f32               0x08006217   Thumb Code    38  arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32)
    [Anonymous Symbol]                       0x08006216   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32)
    arm_rfft_2048_fast_init_f32              0x0800623d   Thumb Code    42  arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32)
    [Anonymous Symbol]                       0x0800623c   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32)
    arm_rfft_256_fast_init_f32               0x08006267   Thumb Code    40  arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32)
    [Anonymous Symbol]                       0x08006266   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32)
    arm_rfft_32_fast_init_f32                0x0800628f   Thumb Code    38  arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32)
    [Anonymous Symbol]                       0x0800628e   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32)
    arm_rfft_4096_fast_init_f32              0x080062b5   Thumb Code    42  arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32)
    [Anonymous Symbol]                       0x080062b4   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32)
    arm_rfft_512_fast_init_f32               0x080062df   Thumb Code    42  arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32)
    [Anonymous Symbol]                       0x080062de   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32)
    arm_rfft_64_fast_init_f32                0x08006309   Thumb Code    38  arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32)
    [Anonymous Symbol]                       0x08006308   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32)
    [Anonymous Symbol]                       0x0800632e   Section        0  arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
    [Anonymous Symbol]                       0x080063c0   Section        0  lcd.o(.text.lcd_clear)
    [Anonymous Symbol]                       0x08006418   Section        0  lcd.o(.text.lcd_display_dir)
    [Anonymous Symbol]                       0x0800662c   Section        0  lcd.o(.text.lcd_draw_point)
    [Anonymous Symbol]                       0x08006658   Section        0  lcd.o(.text.lcd_ex_ili9341_reginit)
    [Anonymous Symbol]                       0x080068a0   Section        0  lcd.o(.text.lcd_ex_ili9806_reginit)
    [Anonymous Symbol]                       0x08006c10   Section        0  lcd.o(.text.lcd_ex_nt35310_reginit)
    [Anonymous Symbol]                       0x08007ba4   Section        0  lcd.o(.text.lcd_ex_nt35510_reginit)
    [Anonymous Symbol]                       0x08008b5c   Section        0  lcd.o(.text.lcd_ex_ssd1963_reginit)
    [Anonymous Symbol]                       0x08008ce0   Section        0  lcd.o(.text.lcd_ex_st7789_reginit)
    [Anonymous Symbol]                       0x08008ea4   Section        0  lcd.o(.text.lcd_ex_st7796_reginit)
    [Anonymous Symbol]                       0x08009084   Section        0  lcd.o(.text.lcd_init)
    lcd_opt_delay                            0x080094c1   Thumb Code    22  lcd.o(.text.lcd_opt_delay)
    [Anonymous Symbol]                       0x080094c0   Section        0  lcd.o(.text.lcd_opt_delay)
    lcd_rd_data                              0x080094d9   Thumb Code    30  lcd.o(.text.lcd_rd_data)
    [Anonymous Symbol]                       0x080094d8   Section        0  lcd.o(.text.lcd_rd_data)
    [Anonymous Symbol]                       0x080094f8   Section        0  lcd.o(.text.lcd_scan_dir)
    [Anonymous Symbol]                       0x08009854   Section        0  lcd.o(.text.lcd_set_cursor)
    [Anonymous Symbol]                       0x080099dc   Section        0  lcd.o(.text.lcd_show_char)
    [Anonymous Symbol]                       0x08009bb8   Section        0  lcd.o(.text.lcd_show_string)
    [Anonymous Symbol]                       0x08009ca8   Section        0  lcd.o(.text.lcd_ssd_backlight_set)
    [Anonymous Symbol]                       0x08009d10   Section        0  lcd.o(.text.lcd_wr_data)
    [Anonymous Symbol]                       0x08009d30   Section        0  lcd.o(.text.lcd_wr_regno)
    [Anonymous Symbol]                       0x08009d50   Section        0  lcd.o(.text.lcd_write_ram_prepare)
    [Anonymous Symbol]                       0x08009d64   Section        0  lcd.o(.text.lcd_write_reg)
    [Anonymous Symbol]                       0x08009d8c   Section        0  main.o(.text.main)
    i.__ARM_fpclassifyf                      0x0800a318   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan2f                        0x0800a340   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_cosf                          0x0800a5ec   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_roundf                        0x0800a73c   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sinf                          0x0800a7d8   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x0800a968   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan                   0x0800a9a2   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x0800a9a8   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x0800a9b0   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x0800a9c0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x0800a9d0   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x0800ab24   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800ab32   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800ab34   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800ab44   Section        0  errno.o(i.__set_errno)
    twooverpi                                0x0800ab50   Data          32  rredf.o(.constdata)
    .constdata                               0x0800ab50   Section       32  rredf.o(.constdata)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x0800ab88   Data           8  stm32f4xx_hal_dma.o(.rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset)
    [Anonymous Symbol]                       0x0800ab88   Section        0  stm32f4xx_hal_dma.o(.rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset)
    .L.str.8                                 0x08013078   Data          19  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08013078   Section        0  main.o(.rodata.str1.1)
    .L.str.3                                 0x0801308b   Data           7  main.o(.rodata.str1.1)
    .L.str.7                                 0x08013092   Data          12  main.o(.rodata.str1.1)
    .L.str.4                                 0x0801309e   Data          22  main.o(.rodata.str1.1)
    .L.str                                   0x080130b4   Data          14  main.o(.rodata.str1.1)
    .L.str.1                                 0x080130c2   Data          17  main.o(.rodata.str1.1)
    .L.str.5                                 0x080130d3   Data          18  main.o(.rodata.str1.1)
    .L.str.9                                 0x080130e5   Data          14  main.o(.rodata.str1.1)
    .L.str.6                                 0x080130f3   Data          22  main.o(.rodata.str1.1)
    .L.str.13                                0x08013109   Data          16  main.o(.rodata.str1.1)
    .L.str.12                                0x08013119   Data          16  main.o(.rodata.str1.1)
    .L.str.11                                0x08013129   Data          16  main.o(.rodata.str1.1)
    .L.str.10                                0x08013139   Data          15  main.o(.rodata.str1.1)
    .L.str.2                                 0x08013148   Data          20  main.o(.rodata.str1.1)
    _errno                                   0x20000000   Data           4  errno.o(.data)
    .data                                    0x20000000   Section        4  errno.o(.data)
    FSMC_Initialized                         0x2000002c   Data           4  fsmc.o(.bss.FSMC_Initialized)
    [Anonymous Symbol]                       0x2000002c   Section        0  fsmc.o(.bss.FSMC_Initialized)
    STACK                                    0x20012d68   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000223   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x0800026b   Thumb Code   228  dmul.o(.text)
    __aeabi_dcmple                           0x0800034f   Thumb Code    54  dcmple.o(.text)
    __aeabi_dcmpge                           0x08000385   Thumb Code    54  dcmpge.o(.text)
    __aeabi_i2d                              0x080003bb   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x080003dd   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2iz                             0x080003f7   Thumb Code    62  dfixi.o(.text)
    __aeabi_d2uiz                            0x08000435   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x08000467   Thumb Code    38  f2d.o(.text)
    __aeabi_llsl                             0x0800048d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800048d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080004ab   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080004ab   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x080004cb   Thumb Code     0  iusefp.o(.text)
    _frnd                                    0x080004cb   Thumb Code    60  frnd.o(.text)
    _double_round                            0x08000507   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000525   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x080005c1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080005c1   Thumb Code     0  init.o(.text)
    _float_round                             0x080005e5   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080005f7   Thumb Code    92  fepilogue.o(.text)
    Analyze_Filter_Response                  0x080008d9   Thumb Code   460  main.o(.text.Analyze_Filter_Response)
    BusFault_Handler                         0x08000aa5   Thumb Code     4  stm32f4xx_it.o(.text.BusFault_Handler)
    DAC_DMAConvCpltCh1                       0x08000aa9   Thumb Code    28  stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1)
    DAC_DMAConvCpltCh2                       0x08000ac5   Thumb Code    28  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2)
    DAC_DMAErrorCh1                          0x08000ae1   Thumb Code    38  stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1)
    DAC_DMAErrorCh2                          0x08000b09   Thumb Code    38  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2)
    DAC_DMAHalfConvCpltCh1                   0x08000b31   Thumb Code    22  stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1)
    DAC_DMAHalfConvCpltCh2                   0x08000b49   Thumb Code    22  stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2)
    DMA1_Stream5_IRQHandler                  0x08000b61   Thumb Code    16  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x08000b71   Thumb Code    16  stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000b81   Thumb Code    16  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x08000b91   Thumb Code    16  stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler)
    DebugMon_Handler                         0x08000d39   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Display_Results                          0x08000d3d   Thumb Code   136  main.o(.text.Display_Results)
    Error_Handler                            0x08000dc5   Thumb Code     6  main.o(.text.Error_Handler)
    FSMC_NORSRAM_Extended_Timing_Init        0x08000dcd   Thumb Code   100  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init)
    FSMC_NORSRAM_Init                        0x08000e31   Thumb Code   186  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Init)
    FSMC_NORSRAM_Timing_Init                 0x08000eed   Thumb Code    74  stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Timing_Init)
    Generate_Flat_Top_Window                 0x08000f39   Thumb Code   336  main.o(.text.Generate_Flat_Top_Window)
    Generate_Hanning_Window                  0x08001089   Thumb Code   108  main.o(.text.Generate_Hanning_Window)
    Get_Signal_Accurate_Amplitude_And_Phase  0x080010f5   Thumb Code   404  main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase)
    HAL_ADC_ConfigChannel                    0x08001289   Thumb Code   536  stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x080014a1   Thumb Code   236  main.o(.text.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x0800158d   Thumb Code   224  main.o(.text.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x0800166d   Thumb Code     8  stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08001675   Thumb Code   168  stm32f4xx_hal_adc.o(.text.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x0800171d   Thumb Code   444  adc.o(.text.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x080018d9   Thumb Code   590  stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x08001b29   Thumb Code   190  stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA)
    HAL_DACEx_ConvCpltCallbackCh2            0x08001be9   Thumb Code     8  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2)
    HAL_DACEx_ConvHalfCpltCallbackCh2        0x08001bf1   Thumb Code     8  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2)
    HAL_DACEx_ErrorCallbackCh2               0x08001bf9   Thumb Code     8  stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2)
    HAL_DAC_ConfigChannel                    0x08001c01   Thumb Code   190  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    HAL_DAC_ConvCpltCallbackCh1              0x08001cc1   Thumb Code     8  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1)
    HAL_DAC_ConvHalfCpltCallbackCh1          0x08001cc9   Thumb Code     8  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1)
    HAL_DAC_ErrorCallbackCh1                 0x08001cd1   Thumb Code     8  stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1)
    HAL_DAC_Init                             0x08001cd9   Thumb Code    74  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08001d25   Thumb Code   232  dac.o(.text.HAL_DAC_MspInit)
    HAL_DAC_Start_DMA                        0x08001e0d   Thumb Code   418  stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
    HAL_DAC_Stop_DMA                         0x08001fb1   Thumb Code   140  stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA)
    HAL_DMA_Abort                            0x0800203d   Thumb Code   236  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002129   Thumb Code    68  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x0800216d   Thumb Code   798  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800248d   Thumb Code   366  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080025fd   Thumb Code   194  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x080026c1   Thumb Code    66  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x08002809   Thumb Code   950  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002bc1   Thumb Code    46  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08002bf1   Thumb Code    46  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002c21   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x08002c2d   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08002c49   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08002c81   Thumb Code   112  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08002cf1   Thumb Code    74  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002d3d   Thumb Code    20  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002d51   Thumb Code    50  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002d85   Thumb Code    16  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002d95   Thumb Code   610  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08002ff9   Thumb Code    12  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003005   Thumb Code    34  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003029   Thumb Code    34  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800304d   Thumb Code   230  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003135   Thumb Code  1726  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x080037f5   Thumb Code   128  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08003875   Thumb Code    14  fsmc.o(.text.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08003885   Thumb Code    16  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08003895   Thumb Code   290  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_IC_ConfigChannel                 0x080039b9   Thumb Code   304  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08003ae9   Thumb Code   156  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08003b85   Thumb Code   138  tim.o(.text.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_IT                      0x08003c11   Thumb Code   678  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT)
    HAL_UARTEx_RxEventCallback               0x08003eb9   Thumb Code    12  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003ec5   Thumb Code     8  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003ecd   Thumb Code  1116  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004329   Thumb Code   158  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x080043c9   Thumb Code   248  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x080044c1   Thumb Code     8  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x080044c9   Thumb Code     8  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080044d1   Thumb Code     4  stm32f4xx_it.o(.text.HardFault_Handler)
    IO_Update                                0x080044d5   Thumb Code    64  ad9959.o(.text.IO_Update)
    Init_AD9959                              0x08004515   Thumb Code    96  ad9959.o(.text.Init_AD9959)
    IntReset                                 0x08004575   Thumb Code    66  ad9959.o(.text.IntReset)
    Intserve                                 0x080045b9   Thumb Code   174  ad9959.o(.text.Intserve)
    LCD_ShowString_Simplified                0x08004669   Thumb Code    66  main.o(.text.LCD_ShowString_Simplified)
    MX_ADC1_Init                             0x080046ad   Thumb Code   128  adc.o(.text.MX_ADC1_Init)
    MX_ADC2_Init                             0x0800472d   Thumb Code   130  adc.o(.text.MX_ADC2_Init)
    MX_DAC_Init                              0x080047b1   Thumb Code    78  dac.o(.text.MX_DAC_Init)
    MX_DMA_Init                              0x08004801   Thumb Code   150  dma.o(.text.MX_DMA_Init)
    MX_FSMC_Init                             0x08004899   Thumb Code   174  fsmc.o(.text.MX_FSMC_Init)
    MX_GPIO_Init                             0x08004949   Thumb Code   676  gpio.o(.text.MX_GPIO_Init)
    MX_TIM5_Init                             0x08004bed   Thumb Code   138  tim.o(.text.MX_TIM5_Init)
    MX_USART1_UART_Init                      0x08004c79   Thumb Code    64  usart.o(.text.MX_USART1_UART_Init)
    MemManage_Handler                        0x08004cb9   Thumb Code     4  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08004cbd   Thumb Code     4  stm32f4xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x08004d2d   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    Process_Buffer_DFT_SIM                   0x08004d31   Thumb Code   964  main.o(.text.Process_Buffer_DFT_SIM)
    SVC_Handler                              0x080050f5   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    Start_Simulation_Mode                    0x080050f9   Thumb Code   338  main.o(.text.Start_Simulation_Mode)
    Start_Sweep_And_Analysis                 0x0800524d   Thumb Code   268  main.o(.text.Start_Sweep_And_Analysis)
    Stop_Simulation_Mode                     0x08005359   Thumb Code    94  main.o(.text.Stop_Simulation_Mode)
    SysTick_Handler                          0x0800540d   Thumb Code     8  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08005415   Thumb Code   190  main.o(.text.SystemClock_Config)
    SystemInit                               0x080054d5   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x080054e9   Thumb Code   420  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x0800568d   Thumb Code    54  stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd)
    TIM_TI1_SetConfig                        0x080056c5   Thumb Code   278  stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig)
    USART1_IRQHandler                        0x08005d31   Thumb Code    16  stm32f4xx_it.o(.text.USART1_IRQHandler)
    UsageFault_Handler                       0x08005d41   Thumb Code     4  stm32f4xx_it.o(.text.UsageFault_Handler)
    WriteData_AD9959                         0x08005d45   Thumb Code   398  ad9959.o(.text.WriteData_AD9959)
    Write_Amplitude                          0x08005ed5   Thumb Code   190  ad9959.o(.text.Write_Amplitude)
    Write_frequence                          0x08005f99   Thumb Code   224  ad9959.o(.text.Write_frequence)
    arm_cfft_init_f32                        0x08006139   Thumb Code   180  arm_cfft_init_f32.o(.text.arm_cfft_init_f32)
    arm_rfft_fast_init_f32                   0x0800632f   Thumb Code   144  arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
    lcd_clear                                0x080063c1   Thumb Code    86  lcd.o(.text.lcd_clear)
    lcd_display_dir                          0x08006419   Thumb Code   530  lcd.o(.text.lcd_display_dir)
    lcd_draw_point                           0x0800662d   Thumb Code    44  lcd.o(.text.lcd_draw_point)
    lcd_ex_ili9341_reginit                   0x08006659   Thumb Code   584  lcd.o(.text.lcd_ex_ili9341_reginit)
    lcd_ex_ili9806_reginit                   0x080068a1   Thumb Code   878  lcd.o(.text.lcd_ex_ili9806_reginit)
    lcd_ex_nt35310_reginit                   0x08006c11   Thumb Code  3986  lcd.o(.text.lcd_ex_nt35310_reginit)
    lcd_ex_nt35510_reginit                   0x08007ba5   Thumb Code  4022  lcd.o(.text.lcd_ex_nt35510_reginit)
    lcd_ex_ssd1963_reginit                   0x08008b5d   Thumb Code   386  lcd.o(.text.lcd_ex_ssd1963_reginit)
    lcd_ex_st7789_reginit                    0x08008ce1   Thumb Code   452  lcd.o(.text.lcd_ex_st7789_reginit)
    lcd_ex_st7796_reginit                    0x08008ea5   Thumb Code   480  lcd.o(.text.lcd_ex_st7796_reginit)
    lcd_init                                 0x08009085   Thumb Code  1084  lcd.o(.text.lcd_init)
    lcd_scan_dir                             0x080094f9   Thumb Code   860  lcd.o(.text.lcd_scan_dir)
    lcd_set_cursor                           0x08009855   Thumb Code   392  lcd.o(.text.lcd_set_cursor)
    lcd_show_char                            0x080099dd   Thumb Code   474  lcd.o(.text.lcd_show_char)
    lcd_show_string                          0x08009bb9   Thumb Code   234  lcd.o(.text.lcd_show_string)
    lcd_ssd_backlight_set                    0x08009ca9   Thumb Code   104  lcd.o(.text.lcd_ssd_backlight_set)
    lcd_wr_data                              0x08009d11   Thumb Code    30  lcd.o(.text.lcd_wr_data)
    lcd_wr_regno                             0x08009d31   Thumb Code    30  lcd.o(.text.lcd_wr_regno)
    lcd_write_ram_prepare                    0x08009d51   Thumb Code    20  lcd.o(.text.lcd_write_ram_prepare)
    lcd_write_reg                            0x08009d65   Thumb Code    38  lcd.o(.text.lcd_write_reg)
    main                                     0x08009d8d   Thumb Code  1420  main.o(.text.main)
    __ARM_fpclassifyf                        0x0800a319   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x0800a341   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_cosf                            0x0800a5ed   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_roundf                          0x0800a73d   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sinf                            0x0800a7d9   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x0800a969   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan                     0x0800a9a3   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x0800a9a9   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x0800a9b1   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x0800a9c1   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x0800a9d1   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x0800ab25   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800ab33   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800ab35   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800ab45   Thumb Code     6  errno.o(i.__set_errno)
    AHBPrescTable                            0x0800ab70   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x0800ab80   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    armBitRevIndexTable1024                  0x0800ab90   Data        3600  arm_common_tables.o(.rodata.armBitRevIndexTable1024)
    armBitRevIndexTable128                   0x0800b9a0   Data         416  arm_common_tables.o(.rodata.armBitRevIndexTable128)
    armBitRevIndexTable16                    0x0800bb40   Data          40  arm_common_tables.o(.rodata.armBitRevIndexTable16)
    armBitRevIndexTable2048                  0x0800bb68   Data        7616  arm_common_tables.o(.rodata.armBitRevIndexTable2048)
    armBitRevIndexTable256                   0x0800d928   Data         880  arm_common_tables.o(.rodata.armBitRevIndexTable256)
    armBitRevIndexTable32                    0x0800dc98   Data          96  arm_common_tables.o(.rodata.armBitRevIndexTable32)
    armBitRevIndexTable4096                  0x0800dcf8   Data        8064  arm_common_tables.o(.rodata.armBitRevIndexTable4096)
    armBitRevIndexTable512                   0x0800fc78   Data         896  arm_common_tables.o(.rodata.armBitRevIndexTable512)
    armBitRevIndexTable64                    0x0800fff8   Data         112  arm_common_tables.o(.rodata.armBitRevIndexTable64)
    arm_cfft_sR_f32_len1024                  0x08010068   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len1024)
    arm_cfft_sR_f32_len128                   0x08010078   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len128)
    arm_cfft_sR_f32_len16                    0x08010088   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len16)
    arm_cfft_sR_f32_len2048                  0x08010098   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len2048)
    arm_cfft_sR_f32_len256                   0x080100a8   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len256)
    arm_cfft_sR_f32_len32                    0x080100b8   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len32)
    arm_cfft_sR_f32_len4096                  0x080100c8   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len4096)
    arm_cfft_sR_f32_len512                   0x080100d8   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len512)
    arm_cfft_sR_f32_len64                    0x080100e8   Data          16  arm_const_structs.o(.rodata.arm_cfft_sR_f32_len64)
    asc2_1206                                0x080100f8   Data        1140  lcd.o(.rodata.asc2_1206)
    asc2_1608                                0x0801056c   Data        1520  lcd.o(.rodata.asc2_1608)
    asc2_2412                                0x08010b5c   Data        3420  lcd.o(.rodata.asc2_2412)
    asc2_3216                                0x080118b8   Data        6080  lcd.o(.rodata.asc2_3216)
    twiddleCoef_1024                         0x0801315c   Data        8192  arm_common_tables.o(.rodata.twiddleCoef_1024)
    twiddleCoef_128                          0x0801515c   Data        1024  arm_common_tables.o(.rodata.twiddleCoef_128)
    twiddleCoef_16                           0x0801555c   Data         128  arm_common_tables.o(.rodata.twiddleCoef_16)
    twiddleCoef_2048                         0x080155dc   Data       16384  arm_common_tables.o(.rodata.twiddleCoef_2048)
    twiddleCoef_256                          0x080195dc   Data        2048  arm_common_tables.o(.rodata.twiddleCoef_256)
    twiddleCoef_32                           0x08019ddc   Data         256  arm_common_tables.o(.rodata.twiddleCoef_32)
    twiddleCoef_4096                         0x08019edc   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    twiddleCoef_512                          0x08021edc   Data        4096  arm_common_tables.o(.rodata.twiddleCoef_512)
    twiddleCoef_64                           0x08022edc   Data         512  arm_common_tables.o(.rodata.twiddleCoef_64)
    twiddleCoef_rfft_1024                    0x080230dc   Data        4096  arm_common_tables.o(.rodata.twiddleCoef_rfft_1024)
    twiddleCoef_rfft_128                     0x080240dc   Data         512  arm_common_tables.o(.rodata.twiddleCoef_rfft_128)
    twiddleCoef_rfft_2048                    0x080242dc   Data        8192  arm_common_tables.o(.rodata.twiddleCoef_rfft_2048)
    twiddleCoef_rfft_256                     0x080262dc   Data        1024  arm_common_tables.o(.rodata.twiddleCoef_rfft_256)
    twiddleCoef_rfft_32                      0x080266dc   Data         128  arm_common_tables.o(.rodata.twiddleCoef_rfft_32)
    twiddleCoef_rfft_4096                    0x0802675c   Data       16384  arm_common_tables.o(.rodata.twiddleCoef_rfft_4096)
    twiddleCoef_rfft_512                     0x0802a75c   Data        2048  arm_common_tables.o(.rodata.twiddleCoef_rfft_512)
    twiddleCoef_rfft_64                      0x0802af5c   Data         256  arm_common_tables.o(.rodata.twiddleCoef_rfft_64)
    Region$$Table$$Base                      0x0802b05c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0802b07c   Number         0  anon$$obj.o(Region$$Table)
    CSR_DATA0                                0x20000004   Data           1  ad9959.o(.data.CSR_DATA0)
    CSR_DATA1                                0x20000005   Data           1  ad9959.o(.data.CSR_DATA1)
    CSR_DATA2                                0x20000006   Data           1  ad9959.o(.data.CSR_DATA2)
    CSR_DATA3                                0x20000007   Data           1  ad9959.o(.data.CSR_DATA3)
    FR1_DATA                                 0x20000008   Data           3  ad9959.o(.data.FR1_DATA)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    g_back_color                             0x20000010   Data           4  lcd.o(.data.g_back_color)
    g_current_freq_sweep                     0x20000014   Data           4  main.o(.data.g_current_freq_sweep)
    g_current_sampling_simulation            0x20000018   Data           4  main.o(.data.g_current_sampling_simulation)
    g_current_signal_rate                    0x2000001c   Data           4  main.o(.data.g_current_signal_rate)
    uwTickFreq                               0x20000020   Data           1  stm32f4xx_hal.o(.data.uwTickFreq)
    uwTickPrio                               0x20000024   Data           4  stm32f4xx_hal.o(.data.uwTickPrio)
    ACR_DATA                                 0x20000028   Data           3  ad9959.o(.bss.ACR_DATA)
    g_H_magnitude                            0x20000030   Data       10000  main.o(.bss.g_H_magnitude)
    g_H_phase                                0x20002740   Data       10000  main.o(.bss.g_H_phase)
    g_adc1_data_ready                        0x20004e50   Data           1  main.o(.bss.g_adc1_data_ready)
    g_adc1_dma_buffer                        0x20004e52   Data       12000  main.o(.bss.g_adc1_dma_buffer)
    g_adc1_processing_buffer                 0x20007d32   Data        6000  main.o(.bss.g_adc1_processing_buffer)
    g_adc2_data_ready                        0x200094a2   Data           1  main.o(.bss.g_adc2_data_ready)
    g_adc2_dma_buffer                        0x200094a4   Data       12000  main.o(.bss.g_adc2_dma_buffer)
    g_adc2_processing_buffer                 0x2000c384   Data        6000  main.o(.bss.g_adc2_processing_buffer)
    g_app_state                              0x2000daf4   Data           1  main.o(.bss.g_app_state)
    g_dac_output_buffer                      0x2000daf6   Data       12000  main.o(.bss.g_dac_output_buffer)
    g_fft_instance                           0x200109d8   Data          24  main.o(.bss.g_fft_instance)
    g_filter_type                            0x200109f0   Data           1  main.o(.bss.g_filter_type)
    g_flat_top_window                        0x200109f4   Data        4096  main.o(.bss.g_flat_top_window)
    g_hanning_window                         0x200119f4   Data        4096  main.o(.bss.g_hanning_window)
    g_sram_handle                            0x200129f4   Data          80  lcd.o(.bss.g_sram_handle)
    g_sweep_current_step                     0x20012a44   Data           4  main.o(.bss.g_sweep_current_step)
    hadc1                                    0x20012a48   Data          72  adc.o(.bss.hadc1)
    hadc2                                    0x20012a90   Data          72  adc.o(.bss.hadc2)
    hdac                                     0x20012ad8   Data          20  dac.o(.bss.hdac)
    hdma_adc1                                0x20012aec   Data          96  adc.o(.bss.hdma_adc1)
    hdma_adc2                                0x20012b4c   Data          96  adc.o(.bss.hdma_adc2)
    hdma_dac1                                0x20012bac   Data          96  dac.o(.bss.hdma_dac1)
    hdma_usart1_tx                           0x20012c0c   Data          96  usart.o(.bss.hdma_usart1_tx)
    hsram1                                   0x20012c6c   Data          80  fsmc.o(.bss.hsram1)
    htim5                                    0x20012cbc   Data          72  tim.o(.bss.htim5)
    huart1                                   0x20012d04   Data          72  usart.o(.bss.huart1)
    lcddev                                   0x20012d4c   Data          14  lcd.o(.bss.lcddev)
    sampling_frequency_sweep                 0x20012d5c   Data           4  main.o(.bss.sampling_frequency_sweep)
    stop_flag                                0x20012d60   Data           1  main.o(.bss.stop_flag)
    uwTick                                   0x20012d64   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20013168   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0002b0a8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0002b07c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         1787  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1833    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1836    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1838    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1840    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1841    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1843    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1845    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1834    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         1790    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         1792    .text               mc_w.l(memcpya.o)
    0x08000246   0x08000246   0x00000024   Code   RO         1794    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x000000e4   Code   RO         1798    .text               mf_w.l(dmul.o)
    0x0800034e   0x0800034e   0x00000036   Code   RO         1800    .text               mf_w.l(dcmple.o)
    0x08000384   0x08000384   0x00000036   Code   RO         1802    .text               mf_w.l(dcmpge.o)
    0x080003ba   0x080003ba   0x00000022   Code   RO         1804    .text               mf_w.l(dflti.o)
    0x080003dc   0x080003dc   0x0000001a   Code   RO         1806    .text               mf_w.l(dfltui.o)
    0x080003f6   0x080003f6   0x0000003e   Code   RO         1808    .text               mf_w.l(dfixi.o)
    0x08000434   0x08000434   0x00000032   Code   RO         1810    .text               mf_w.l(dfixui.o)
    0x08000466   0x08000466   0x00000026   Code   RO         1812    .text               mf_w.l(f2d.o)
    0x0800048c   0x0800048c   0x0000001e   Code   RO         1847    .text               mc_w.l(llshl.o)
    0x080004aa   0x080004aa   0x00000020   Code   RO         1849    .text               mc_w.l(llushr.o)
    0x080004ca   0x080004ca   0x00000000   Code   RO         1858    .text               mc_w.l(iusefp.o)
    0x080004ca   0x080004ca   0x0000003c   Code   RO         1859    .text               mf_w.l(frnd.o)
    0x08000506   0x08000506   0x000000ba   Code   RO         1861    .text               mf_w.l(depilogue.o)
    0x080005c0   0x080005c0   0x00000024   Code   RO         1863    .text               mc_w.l(init.o)
    0x080005e4   0x080005e4   0x0000006e   Code   RO         1865    .text               mf_w.l(fepilogue.o)
    0x08000652   0x08000652   0x00000002   PAD
    0x08000654   0x08000654   0x000000b2   Code   RO          371    .text.ADC_DMAConvCplt  stm32f4xx_hal_adc.o
    0x08000706   0x08000706   0x00000002   PAD
    0x08000708   0x08000708   0x00000026   Code   RO          375    .text.ADC_DMAError  stm32f4xx_hal_adc.o
    0x0800072e   0x0800072e   0x00000002   PAD
    0x08000730   0x08000730   0x00000016   Code   RO          373    .text.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08000746   0x08000746   0x00000002   PAD
    0x08000748   0x08000748   0x0000018a   Code   RO          343    .text.ADC_Init      stm32f4xx_hal_adc.o
    0x080008d2   0x080008d2   0x00000006   PAD
    0x080008d8   0x080008d8   0x000001cc   Code   RO          147    .text.Analyze_Filter_Response  main.o
    0x08000aa4   0x08000aa4   0x00000004   Code   RO          302    .text.BusFault_Handler  stm32f4xx_it.o
    0x08000aa8   0x08000aa8   0x0000001c   Code   RO          888    .text.DAC_DMAConvCpltCh1  stm32f4xx_hal_dac.o
    0x08000ac4   0x08000ac4   0x0000001c   Code   RO          943    .text.DAC_DMAConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08000ae0   0x08000ae0   0x00000026   Code   RO          892    .text.DAC_DMAErrorCh1  stm32f4xx_hal_dac.o
    0x08000b06   0x08000b06   0x00000002   PAD
    0x08000b08   0x08000b08   0x00000026   Code   RO          947    .text.DAC_DMAErrorCh2  stm32f4xx_hal_dac_ex.o
    0x08000b2e   0x08000b2e   0x00000002   PAD
    0x08000b30   0x08000b30   0x00000016   Code   RO          890    .text.DAC_DMAHalfConvCpltCh1  stm32f4xx_hal_dac.o
    0x08000b46   0x08000b46   0x00000002   PAD
    0x08000b48   0x08000b48   0x00000016   Code   RO          945    .text.DAC_DMAHalfConvCpltCh2  stm32f4xx_hal_dac_ex.o
    0x08000b5e   0x08000b5e   0x00000002   PAD
    0x08000b60   0x08000b60   0x00000010   Code   RO          314    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08000b70   0x08000b70   0x00000010   Code   RO          318    .text.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x08000b80   0x08000b80   0x00000010   Code   RO          320    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000b90   0x08000b90   0x00000010   Code   RO          322    .text.DMA2_Stream7_IRQHandler  stm32f4xx_it.o
    0x08000ba0   0x08000ba0   0x00000056   Code   RO          614    .text.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08000bf6   0x08000bf6   0x00000002   PAD
    0x08000bf8   0x08000bf8   0x000000f0   Code   RO          612    .text.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000ce8   0x08000ce8   0x00000050   Code   RO          620    .text.DMA_SetConfig  stm32f4xx_hal_dma.o
    0x08000d38   0x08000d38   0x00000002   Code   RO          308    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000d3a   0x08000d3a   0x00000002   PAD
    0x08000d3c   0x08000d3c   0x00000088   Code   RO          149    .text.Display_Results  main.o
    0x08000dc4   0x08000dc4   0x00000006   Code   RO          133    .text.Error_Handler  main.o
    0x08000dca   0x08000dca   0x00000002   PAD
    0x08000dcc   0x08000dcc   0x00000064   Code   RO          962    .text.FSMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08000e30   0x08000e30   0x000000ba   Code   RO          956    .text.FSMC_NORSRAM_Init  stm32f4xx_ll_fsmc.o
    0x08000eea   0x08000eea   0x00000002   PAD
    0x08000eec   0x08000eec   0x0000004a   Code   RO          960    .text.FSMC_NORSRAM_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08000f36   0x08000f36   0x00000002   PAD
    0x08000f38   0x08000f38   0x00000150   Code   RO          137    .text.Generate_Flat_Top_Window  main.o
    0x08001088   0x08001088   0x0000006c   Code   RO          139    .text.Generate_Hanning_Window  main.o
    0x080010f4   0x080010f4   0x00000194   Code   RO          145    .text.Get_Signal_Accurate_Amplitude_And_Phase  main.o
    0x08001288   0x08001288   0x00000218   Code   RO          383    .text.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x080014a0   0x080014a0   0x000000ec   Code   RO          157    .text.HAL_ADC_ConvCpltCallback  main.o
    0x0800158c   0x0800158c   0x000000e0   Code   RO          153    .text.HAL_ADC_ConvHalfCpltCallback  main.o
    0x0800166c   0x0800166c   0x00000008   Code   RO          367    .text.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08001674   0x08001674   0x000000a8   Code   RO          339    .text.HAL_ADC_Init  stm32f4xx_hal_adc.o
    0x0800171c   0x0800171c   0x000001bc   Code   RO          209    .text.HAL_ADC_MspInit  adc.o
    0x080018d8   0x080018d8   0x0000024e   Code   RO          369    .text.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08001b26   0x08001b26   0x00000002   PAD
    0x08001b28   0x08001b28   0x000000be   Code   RO          377    .text.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08001be6   0x08001be6   0x00000002   PAD
    0x08001be8   0x08001be8   0x00000008   Code   RO          933    .text.HAL_DACEx_ConvCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x08001bf0   0x08001bf0   0x00000008   Code   RO          935    .text.HAL_DACEx_ConvHalfCpltCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x08001bf8   0x08001bf8   0x00000008   Code   RO          937    .text.HAL_DACEx_ErrorCallbackCh2  stm32f4xx_hal_dac_ex.o
    0x08001c00   0x08001c00   0x000000be   Code   RO          910    .text.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x08001cbe   0x08001cbe   0x00000002   PAD
    0x08001cc0   0x08001cc0   0x00000008   Code   RO          902    .text.HAL_DAC_ConvCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08001cc8   0x08001cc8   0x00000008   Code   RO          904    .text.HAL_DAC_ConvHalfCpltCallbackCh1  stm32f4xx_hal_dac.o
    0x08001cd0   0x08001cd0   0x00000008   Code   RO          906    .text.HAL_DAC_ErrorCallbackCh1  stm32f4xx_hal_dac.o
    0x08001cd8   0x08001cd8   0x0000004a   Code   RO          874    .text.HAL_DAC_Init  stm32f4xx_hal_dac.o
    0x08001d22   0x08001d22   0x00000002   PAD
    0x08001d24   0x08001d24   0x000000e8   Code   RO          226    .text.HAL_DAC_MspInit  dac.o
    0x08001e0c   0x08001e0c   0x000001a2   Code   RO          886    .text.HAL_DAC_Start_DMA  stm32f4xx_hal_dac.o
    0x08001fae   0x08001fae   0x00000002   PAD
    0x08001fb0   0x08001fb0   0x0000008c   Code   RO          894    .text.HAL_DAC_Stop_DMA  stm32f4xx_hal_dac.o
    0x0800203c   0x0800203c   0x000000ec   Code   RO          624    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x08002128   0x08002128   0x00000044   Code   RO          626    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x0800216c   0x0800216c   0x0000031e   Code   RO          630    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x0800248a   0x0800248a   0x00000002   PAD
    0x0800248c   0x0800248c   0x0000016e   Code   RO          610    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x080025fa   0x080025fa   0x00000002   PAD
    0x080025fc   0x080025fc   0x000000c2   Code   RO          622    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080026be   0x080026be   0x00000002   PAD
    0x080026c0   0x080026c0   0x00000042   Code   RO          805    .text.HAL_Delay     stm32f4xx_hal.o
    0x08002702   0x08002702   0x00000002   PAD
    0x08002704   0x08002704   0x00000102   Code   RO          251    .text.HAL_FSMC_MspInit  fsmc.o
    0x08002806   0x08002806   0x00000002   PAD
    0x08002808   0x08002808   0x000003b6   Code   RO          572    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08002bbe   0x08002bbe   0x00000002   PAD
    0x08002bc0   0x08002bc0   0x0000002e   Code   RO          576    .text.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08002bee   0x08002bee   0x00000002   PAD
    0x08002bf0   0x08002bf0   0x0000002e   Code   RO          578    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002c1e   0x08002c1e   0x00000002   PAD
    0x08002c20   0x08002c20   0x0000000c   Code   RO          797    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08002c2c   0x08002c2c   0x0000001a   Code   RO          795    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08002c46   0x08002c46   0x00000002   PAD
    0x08002c48   0x08002c48   0x00000036   Code   RO          785    .text.HAL_Init      stm32f4xx_hal.o
    0x08002c7e   0x08002c7e   0x00000002   PAD
    0x08002c80   0x08002c80   0x00000070   Code   RO          787    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08002cf0   0x08002cf0   0x0000004a   Code   RO          331    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08002d3a   0x08002d3a   0x00000002   PAD
    0x08002d3c   0x08002d3c   0x00000014   Code   RO          720    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002d50   0x08002d50   0x00000032   Code   RO          712    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002d82   0x08002d82   0x00000002   PAD
    0x08002d84   0x08002d84   0x00000010   Code   RO          708    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002d94   0x08002d94   0x00000262   Code   RO          440    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002ff6   0x08002ff6   0x00000002   PAD
    0x08002ff8   0x08002ff8   0x0000000c   Code   RO          450    .text.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08003004   0x08003004   0x00000022   Code   RO          452    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003026   0x08003026   0x00000002   PAD
    0x08003028   0x08003028   0x00000022   Code   RO          454    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800304a   0x0800304a   0x00000002   PAD
    0x0800304c   0x0800304c   0x000000e6   Code   RO          442    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003132   0x08003132   0x00000002   PAD
    0x08003134   0x08003134   0x000006be   Code   RO          438    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080037f2   0x080037f2   0x00000002   PAD
    0x080037f4   0x080037f4   0x00000080   Code   RO         1000    .text.HAL_SRAM_Init  stm32f4xx_hal_sram.o
    0x08003874   0x08003874   0x0000000e   Code   RO          249    .text.HAL_SRAM_MspInit  fsmc.o
    0x08003882   0x08003882   0x00000002   PAD
    0x08003884   0x08003884   0x00000010   Code   RO          732    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003894   0x08003894   0x00000122   Code   RO         1362    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080039b6   0x080039b6   0x00000002   PAD
    0x080039b8   0x080039b8   0x00000130   Code   RO         1205    .text.HAL_TIM_IC_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003ae8   0x08003ae8   0x0000009c   Code   RO         1123    .text.HAL_TIM_IC_Init  stm32f4xx_hal_tim.o
    0x08003b84   0x08003b84   0x0000008a   Code   RO          269    .text.HAL_TIM_IC_MspInit  tim.o
    0x08003c0e   0x08003c0e   0x00000002   PAD
    0x08003c10   0x08003c10   0x000002a6   Code   RO         1135    .text.HAL_TIM_IC_Start_IT  stm32f4xx_hal_tim.o
    0x08003eb6   0x08003eb6   0x00000002   PAD
    0x08003eb8   0x08003eb8   0x0000000c   Code   RO         1477    .text.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08003ec4   0x08003ec4   0x00000008   Code   RO         1475    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003ecc   0x08003ecc   0x0000045c   Code   RO         1469    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004328   0x08004328   0x0000009e   Code   RO         1385    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x080043c6   0x080043c6   0x00000002   PAD
    0x080043c8   0x080043c8   0x000000f8   Code   RO          283    .text.HAL_UART_MspInit  usart.o
    0x080044c0   0x080044c0   0x00000008   Code   RO         1487    .text.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x080044c8   0x080044c8   0x00000008   Code   RO         1483    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080044d0   0x080044d0   0x00000004   Code   RO          298    .text.HardFault_Handler  stm32f4xx_it.o
    0x080044d4   0x080044d4   0x00000040   Code   RO          100    .text.IO_Update     ad9959.o
    0x08004514   0x08004514   0x00000060   Code   RO          106    .text.Init_AD9959   ad9959.o
    0x08004574   0x08004574   0x00000042   Code   RO           98    .text.IntReset      ad9959.o
    0x080045b6   0x080045b6   0x00000002   PAD
    0x080045b8   0x080045b8   0x000000ae   Code   RO          102    .text.Intserve      ad9959.o
    0x08004666   0x08004666   0x00000002   PAD
    0x08004668   0x08004668   0x00000042   Code   RO          135    .text.LCD_ShowString_Simplified  main.o
    0x080046aa   0x080046aa   0x00000002   PAD
    0x080046ac   0x080046ac   0x00000080   Code   RO          205    .text.MX_ADC1_Init  adc.o
    0x0800472c   0x0800472c   0x00000082   Code   RO          207    .text.MX_ADC2_Init  adc.o
    0x080047ae   0x080047ae   0x00000002   PAD
    0x080047b0   0x080047b0   0x0000004e   Code   RO          224    .text.MX_DAC_Init   dac.o
    0x080047fe   0x080047fe   0x00000002   PAD
    0x08004800   0x08004800   0x00000096   Code   RO          239    .text.MX_DMA_Init   dma.o
    0x08004896   0x08004896   0x00000002   PAD
    0x08004898   0x08004898   0x000000ae   Code   RO          247    .text.MX_FSMC_Init  fsmc.o
    0x08004946   0x08004946   0x00000002   PAD
    0x08004948   0x08004948   0x000002a4   Code   RO          197    .text.MX_GPIO_Init  gpio.o
    0x08004bec   0x08004bec   0x0000008a   Code   RO          267    .text.MX_TIM5_Init  tim.o
    0x08004c76   0x08004c76   0x00000002   PAD
    0x08004c78   0x08004c78   0x00000040   Code   RO          281    .text.MX_USART1_UART_Init  usart.o
    0x08004cb8   0x08004cb8   0x00000004   Code   RO          300    .text.MemManage_Handler  stm32f4xx_it.o
    0x08004cbc   0x08004cbc   0x00000004   Code   RO          296    .text.NMI_Handler   stm32f4xx_it.o
    0x08004cc0   0x08004cc0   0x0000006c   Code   RO          718    .text.NVIC_EncodePriority  stm32f4xx_hal_cortex.o
    0x08004d2c   0x08004d2c   0x00000002   Code   RO          310    .text.PendSV_Handler  stm32f4xx_it.o
    0x08004d2e   0x08004d2e   0x00000002   PAD
    0x08004d30   0x08004d30   0x000003c4   Code   RO          155    .text.Process_Buffer_DFT_SIM  main.o
    0x080050f4   0x080050f4   0x00000002   Code   RO          306    .text.SVC_Handler   stm32f4xx_it.o
    0x080050f6   0x080050f6   0x00000002   PAD
    0x080050f8   0x080050f8   0x00000152   Code   RO          143    .text.Start_Simulation_Mode  main.o
    0x0800524a   0x0800524a   0x00000002   PAD
    0x0800524c   0x0800524c   0x0000010c   Code   RO          141    .text.Start_Sweep_And_Analysis  main.o
    0x08005358   0x08005358   0x0000005e   Code   RO          151    .text.Stop_Simulation_Mode  main.o
    0x080053b6   0x080053b6   0x00000002   PAD
    0x080053b8   0x080053b8   0x00000052   Code   RO          734    .text.SysTick_Config  stm32f4xx_hal_cortex.o
    0x0800540a   0x0800540a   0x00000002   PAD
    0x0800540c   0x0800540c   0x00000008   Code   RO          312    .text.SysTick_Handler  stm32f4xx_it.o
    0x08005414   0x08005414   0x000000be   Code   RO          131    .text.SystemClock_Config  main.o
    0x080054d2   0x080054d2   0x00000002   PAD
    0x080054d4   0x080054d4   0x00000012   Code   RO         1516    .text.SystemInit    system_stm32f4xx.o
    0x080054e6   0x080054e6   0x00000002   PAD
    0x080054e8   0x080054e8   0x000001a4   Code   RO         1053    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x0800568c   0x0800568c   0x00000036   Code   RO         1087    .text.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x080056c2   0x080056c2   0x00000002   PAD
    0x080056c4   0x080056c4   0x00000116   Code   RO         1207    .text.TIM_TI1_SetConfig  stm32f4xx_hal_tim.o
    0x080057da   0x080057da   0x00000002   PAD
    0x080057dc   0x080057dc   0x0000006c   Code   RO         1209    .text.TIM_TI2_SetConfig  stm32f4xx_hal_tim.o
    0x08005848   0x08005848   0x0000006a   Code   RO         1211    .text.TIM_TI3_SetConfig  stm32f4xx_hal_tim.o
    0x080058b2   0x080058b2   0x00000002   PAD
    0x080058b4   0x080058b4   0x0000006c   Code   RO         1213    .text.TIM_TI4_SetConfig  stm32f4xx_hal_tim.o
    0x08005920   0x08005920   0x0000001c   Code   RO         1473    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800593c   0x0800593c   0x00000094   Code   RO         1433    .text.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080059d0   0x080059d0   0x00000026   Code   RO         1481    .text.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x080059f6   0x080059f6   0x00000002   PAD
    0x080059f8   0x080059f8   0x00000168   Code   RO         1471    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08005b60   0x08005b60   0x0000013a   Code   RO         1389    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x08005c9a   0x08005c9a   0x00000002   PAD
    0x08005c9c   0x08005c9c   0x00000094   Code   RO         1479    .text.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08005d30   0x08005d30   0x00000010   Code   RO          316    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x08005d40   0x08005d40   0x00000004   Code   RO          304    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08005d44   0x08005d44   0x0000018e   Code   RO          104    .text.WriteData_AD9959  ad9959.o
    0x08005ed2   0x08005ed2   0x00000002   PAD
    0x08005ed4   0x08005ed4   0x000000be   Code   RO          110    .text.Write_Amplitude  ad9959.o
    0x08005f92   0x08005f92   0x00000006   PAD
    0x08005f98   0x08005f98   0x000000e0   Code   RO          108    .text.Write_frequence  ad9959.o
    0x08006078   0x08006078   0x00000030   Code   RO          722    .text.__NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080060a8   0x080060a8   0x00000010   Code   RO          714    .text.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080060b8   0x080060b8   0x00000042   Code   RO          716    .text.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080060fa   0x080060fa   0x00000002   PAD
    0x080060fc   0x080060fc   0x0000003c   Code   RO          710    .text.__NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08006138   0x08006138   0x000000b4   Code   RO         1556    .text.arm_cfft_init_f32  arm_cortexM4lf_math.lib(arm_cfft_init_f32.o)
    0x080061ec   0x080061ec   0x0000002a   Code   RO         1536    .text.arm_rfft_1024_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x08006216   0x08006216   0x00000026   Code   RO         1542    .text.arm_rfft_128_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x0800623c   0x0800623c   0x0000002a   Code   RO         1534    .text.arm_rfft_2048_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x08006266   0x08006266   0x00000028   Code   RO         1540    .text.arm_rfft_256_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x0800628e   0x0800628e   0x00000026   Code   RO         1546    .text.arm_rfft_32_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x080062b4   0x080062b4   0x0000002a   Code   RO         1532    .text.arm_rfft_4096_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x080062de   0x080062de   0x0000002a   Code   RO         1538    .text.arm_rfft_512_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x08006308   0x08006308   0x00000026   Code   RO         1544    .text.arm_rfft_64_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x0800632e   0x0800632e   0x00000090   Code   RO         1530    .text.arm_rfft_fast_init_f32  arm_cortexM4lf_math.lib(arm_rfft_fast_init_f32.o)
    0x080063be   0x080063be   0x00000002   PAD
    0x080063c0   0x080063c0   0x00000056   Code   RO           55    .text.lcd_clear     lcd.o
    0x08006416   0x08006416   0x00000002   PAD
    0x08006418   0x08006418   0x00000212   Code   RO           49    .text.lcd_display_dir  lcd.o
    0x0800662a   0x0800662a   0x00000002   PAD
    0x0800662c   0x0800662c   0x0000002c   Code   RO           45    .text.lcd_draw_point  lcd.o
    0x08006658   0x08006658   0x00000248   Code   RO           17    .text.lcd_ex_ili9341_reginit  lcd.o
    0x080068a0   0x080068a0   0x0000036e   Code   RO           27    .text.lcd_ex_ili9806_reginit  lcd.o
    0x08006c0e   0x08006c0e   0x00000002   PAD
    0x08006c10   0x08006c10   0x00000f92   Code   RO           19    .text.lcd_ex_nt35310_reginit  lcd.o
    0x08007ba2   0x08007ba2   0x00000002   PAD
    0x08007ba4   0x08007ba4   0x00000fb6   Code   RO           23    .text.lcd_ex_nt35510_reginit  lcd.o
    0x08008b5a   0x08008b5a   0x00000002   PAD
    0x08008b5c   0x08008b5c   0x00000182   Code   RO           29    .text.lcd_ex_ssd1963_reginit  lcd.o
    0x08008cde   0x08008cde   0x00000002   PAD
    0x08008ce0   0x08008ce0   0x000001c4   Code   RO           11    .text.lcd_ex_st7789_reginit  lcd.o
    0x08008ea4   0x08008ea4   0x000001e0   Code   RO           21    .text.lcd_ex_st7796_reginit  lcd.o
    0x08009084   0x08009084   0x0000043c   Code   RO           53    .text.lcd_init      lcd.o
    0x080094c0   0x080094c0   0x00000016   Code   RO           81    .text.lcd_opt_delay  lcd.o
    0x080094d6   0x080094d6   0x00000002   PAD
    0x080094d8   0x080094d8   0x0000001e   Code   RO           37    .text.lcd_rd_data   lcd.o
    0x080094f6   0x080094f6   0x00000002   PAD
    0x080094f8   0x080094f8   0x0000035c   Code   RO           43    .text.lcd_scan_dir  lcd.o
    0x08009854   0x08009854   0x00000188   Code   RO           35    .text.lcd_set_cursor  lcd.o
    0x080099dc   0x080099dc   0x000001da   Code   RO           71    .text.lcd_show_char  lcd.o
    0x08009bb6   0x08009bb6   0x00000002   PAD
    0x08009bb8   0x08009bb8   0x000000ea   Code   RO           79    .text.lcd_show_string  lcd.o
    0x08009ca2   0x08009ca2   0x00000006   PAD
    0x08009ca8   0x08009ca8   0x00000068   Code   RO           47    .text.lcd_ssd_backlight_set  lcd.o
    0x08009d10   0x08009d10   0x0000001e   Code   RO           15    .text.lcd_wr_data   lcd.o
    0x08009d2e   0x08009d2e   0x00000002   PAD
    0x08009d30   0x08009d30   0x0000001e   Code   RO           13    .text.lcd_wr_regno  lcd.o
    0x08009d4e   0x08009d4e   0x00000002   PAD
    0x08009d50   0x08009d50   0x00000014   Code   RO           31    .text.lcd_write_ram_prepare  lcd.o
    0x08009d64   0x08009d64   0x00000026   Code   RO           25    .text.lcd_write_reg  lcd.o
    0x08009d8a   0x08009d8a   0x00000002   PAD
    0x08009d8c   0x08009d8c   0x0000058c   Code   RO          129    .text.main          main.o
    0x0800a318   0x0800a318   0x00000026   Code   RO         1814    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0800a33e   0x0800a33e   0x00000002   PAD
    0x0800a340   0x0800a340   0x000002ac   Code   RO         1759    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x0800a5ec   0x0800a5ec   0x00000150   Code   RO         1765    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x0800a73c   0x0800a73c   0x0000009a   Code   RO         1783    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x0800a7d6   0x0800a7d6   0x00000002   PAD
    0x0800a7d8   0x0800a7d8   0x00000190   Code   RO         1771    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x0800a968   0x0800a968   0x0000003a   Code   RO         1777    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0800a9a2   0x0800a9a2   0x00000006   Code   RO         1817    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800a9a8   0x0800a9a8   0x00000006   Code   RO         1818    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x0800a9ae   0x0800a9ae   0x00000002   PAD
    0x0800a9b0   0x0800a9b0   0x00000010   Code   RO         1819    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800a9c0   0x0800a9c0   0x00000010   Code   RO         1822    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x0800a9d0   0x0800a9d0   0x00000154   Code   RO         1830    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x0800ab24   0x0800ab24   0x0000000e   Code   RO         1869    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800ab32   0x0800ab32   0x00000002   Code   RO         1870    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800ab34   0x0800ab34   0x0000000e   Code   RO         1871    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800ab42   0x0800ab42   0x00000002   PAD
    0x0800ab44   0x0800ab44   0x0000000c   Code   RO         1853    i.__set_errno       mc_w.l(errno.o)
    0x0800ab50   0x0800ab50   0x00000020   Data   RO         1831    .constdata          m_wm.l(rredf.o)
    0x0800ab70   0x0800ab70   0x00000010   Data   RO         1521    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x0800ab80   0x0800ab80   0x00000008   Data   RO         1522    .rodata.APBPrescTable  system_stm32f4xx.o
    0x0800ab88   0x0800ab88   0x00000008   Data   RO          640    .rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset  stm32f4xx_hal_dma.o
    0x0800ab90   0x0800ab90   0x00000e10   Data   RO         1617    .rodata.armBitRevIndexTable1024  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800b9a0   0x0800b9a0   0x000001a0   Data   RO         1614    .rodata.armBitRevIndexTable128  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800bb40   0x0800bb40   0x00000028   Data   RO         1611    .rodata.armBitRevIndexTable16  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800bb68   0x0800bb68   0x00001dc0   Data   RO         1618    .rodata.armBitRevIndexTable2048  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800d928   0x0800d928   0x00000370   Data   RO         1615    .rodata.armBitRevIndexTable256  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800dc98   0x0800dc98   0x00000060   Data   RO         1612    .rodata.armBitRevIndexTable32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800dcf8   0x0800dcf8   0x00001f80   Data   RO         1619    .rodata.armBitRevIndexTable4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800fc78   0x0800fc78   0x00000380   Data   RO         1616    .rodata.armBitRevIndexTable512  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800fff8   0x0800fff8   0x00000070   Data   RO         1613    .rodata.armBitRevIndexTable64  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08010068   0x08010068   0x00000010   Data   RO         1700    .rodata.arm_cfft_sR_f32_len1024  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x08010078   0x08010078   0x00000010   Data   RO         1697    .rodata.arm_cfft_sR_f32_len128  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x08010088   0x08010088   0x00000010   Data   RO         1694    .rodata.arm_cfft_sR_f32_len16  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x08010098   0x08010098   0x00000010   Data   RO         1701    .rodata.arm_cfft_sR_f32_len2048  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100a8   0x080100a8   0x00000010   Data   RO         1698    .rodata.arm_cfft_sR_f32_len256  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100b8   0x080100b8   0x00000010   Data   RO         1695    .rodata.arm_cfft_sR_f32_len32  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100c8   0x080100c8   0x00000010   Data   RO         1702    .rodata.arm_cfft_sR_f32_len4096  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100d8   0x080100d8   0x00000010   Data   RO         1699    .rodata.arm_cfft_sR_f32_len512  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100e8   0x080100e8   0x00000010   Data   RO         1696    .rodata.arm_cfft_sR_f32_len64  arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x080100f8   0x080100f8   0x00000474   Data   RO           83    .rodata.asc2_1206   lcd.o
    0x0801056c   0x0801056c   0x000005f0   Data   RO           84    .rodata.asc2_1608   lcd.o
    0x08010b5c   0x08010b5c   0x00000d5c   Data   RO           85    .rodata.asc2_2412   lcd.o
    0x080118b8   0x080118b8   0x000017c0   Data   RO           86    .rodata.asc2_3216   lcd.o
    0x08013078   0x08013078   0x000000e4   Data   RO          176    .rodata.str1.1      main.o
    0x0801315c   0x0801315c   0x00002000   Data   RO         1581    .rodata.twiddleCoef_1024  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801515c   0x0801515c   0x00000400   Data   RO         1578    .rodata.twiddleCoef_128  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801555c   0x0801555c   0x00000080   Data   RO         1575    .rodata.twiddleCoef_16  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080155dc   0x080155dc   0x00004000   Data   RO         1582    .rodata.twiddleCoef_2048  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080195dc   0x080195dc   0x00000800   Data   RO         1579    .rodata.twiddleCoef_256  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08019ddc   0x08019ddc   0x00000100   Data   RO         1576    .rodata.twiddleCoef_32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08019edc   0x08019edc   0x00008000   Data   RO         1583    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08021edc   0x08021edc   0x00001000   Data   RO         1580    .rodata.twiddleCoef_512  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08022edc   0x08022edc   0x00000200   Data   RO         1577    .rodata.twiddleCoef_64  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080230dc   0x080230dc   0x00001000   Data   RO         1642    .rodata.twiddleCoef_rfft_1024  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080240dc   0x080240dc   0x00000200   Data   RO         1639    .rodata.twiddleCoef_rfft_128  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080242dc   0x080242dc   0x00002000   Data   RO         1643    .rodata.twiddleCoef_rfft_2048  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080262dc   0x080262dc   0x00000400   Data   RO         1640    .rodata.twiddleCoef_rfft_256  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080266dc   0x080266dc   0x00000080   Data   RO         1637    .rodata.twiddleCoef_rfft_32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802675c   0x0802675c   0x00004000   Data   RO         1644    .rodata.twiddleCoef_rfft_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802a75c   0x0802a75c   0x00000800   Data   RO         1641    .rodata.twiddleCoef_rfft_512  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802af5c   0x0802af5c   0x00000100   Data   RO         1638    .rodata.twiddleCoef_rfft_64  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802b05c   0x0802b05c   0x00000020   Data   RO         1868    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0802b080, Size: 0x00013168, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0802b080   0x00000004   Data   RW         1854    .data               mc_w.l(errno.o)
    0x20000004   0x0802b084   0x00000001   Data   RW          114    .data.CSR_DATA0     ad9959.o
    0x20000005   0x0802b085   0x00000001   Data   RW          115    .data.CSR_DATA1     ad9959.o
    0x20000006   0x0802b086   0x00000001   Data   RW          116    .data.CSR_DATA2     ad9959.o
    0x20000007   0x0802b087   0x00000001   Data   RW          117    .data.CSR_DATA3     ad9959.o
    0x20000008   0x0802b088   0x00000003   Data   RW          118    .data.FR1_DATA      ad9959.o
    0x2000000b   0x0802b08b   0x00000001   PAD
    0x2000000c   0x0802b08c   0x00000004   Data   RW         1520    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000010   0x0802b090   0x00000004   Data   RW           88    .data.g_back_color  lcd.o
    0x20000014   0x0802b094   0x00000004   Data   RW          174    .data.g_current_freq_sweep  main.o
    0x20000018   0x0802b098   0x00000004   Data   RW          163    .data.g_current_sampling_simulation  main.o
    0x2000001c   0x0802b09c   0x00000004   Data   RW          169    .data.g_current_signal_rate  main.o
    0x20000020   0x0802b0a0   0x00000001   Data   RW          840    .data.uwTickFreq    stm32f4xx_hal.o
    0x20000021   0x0802b0a1   0x00000003   PAD
    0x20000024   0x0802b0a4   0x00000004   Data   RW          839    .data.uwTickPrio    stm32f4xx_hal.o
    0x20000028        -       0x00000003   Zero   RW          120    .bss.ACR_DATA       ad9959.o
    0x2000002b   0x0802b0a8   0x00000001   PAD
    0x2000002c        -       0x00000004   Zero   RW          258    .bss.FSMC_Initialized  fsmc.o
    0x20000030        -       0x00002710   Zero   RW          180    .bss.g_H_magnitude  main.o
    0x20002740        -       0x00002710   Zero   RW          181    .bss.g_H_phase      main.o
    0x20004e50        -       0x00000001   Zero   RW          161    .bss.g_adc1_data_ready  main.o
    0x20004e51   0x0802b0a8   0x00000001   PAD
    0x20004e52        -       0x00002ee0   Zero   RW          182    .bss.g_adc1_dma_buffer  main.o
    0x20007d32        -       0x00001770   Zero   RW          177    .bss.g_adc1_processing_buffer  main.o
    0x200094a2        -       0x00000001   Zero   RW          162    .bss.g_adc2_data_ready  main.o
    0x200094a3   0x0802b0a8   0x00000001   PAD
    0x200094a4        -       0x00002ee0   Zero   RW          183    .bss.g_adc2_dma_buffer  main.o
    0x2000c384        -       0x00001770   Zero   RW          179    .bss.g_adc2_processing_buffer  main.o
    0x2000daf4        -       0x00000001   Zero   RW          171    .bss.g_app_state    main.o
    0x2000daf5   0x0802b0a8   0x00000001   PAD
    0x2000daf6        -       0x00002ee0   Zero   RW          186    .bss.g_dac_output_buffer  main.o
    0x200109d6   0x0802b0a8   0x00000002   PAD
    0x200109d8        -       0x00000018   Zero   RW          175    .bss.g_fft_instance  main.o
    0x200109f0        -       0x00000001   Zero   RW          172    .bss.g_filter_type  main.o
    0x200109f1   0x0802b0a8   0x00000003   PAD
    0x200109f4        -       0x00001000   Zero   RW          184    .bss.g_flat_top_window  main.o
    0x200119f4        -       0x00001000   Zero   RW          185    .bss.g_hanning_window  main.o
    0x200129f4        -       0x00000050   Zero   RW           90    .bss.g_sram_handle  lcd.o
    0x20012a44        -       0x00000004   Zero   RW          173    .bss.g_sweep_current_step  main.o
    0x20012a48        -       0x00000048   Zero   RW          213    .bss.hadc1          adc.o
    0x20012a90        -       0x00000048   Zero   RW          214    .bss.hadc2          adc.o
    0x20012ad8        -       0x00000014   Zero   RW          230    .bss.hdac           dac.o
    0x20012aec        -       0x00000060   Zero   RW          215    .bss.hdma_adc1      adc.o
    0x20012b4c        -       0x00000060   Zero   RW          216    .bss.hdma_adc2      adc.o
    0x20012bac        -       0x00000060   Zero   RW          231    .bss.hdma_dac1      dac.o
    0x20012c0c        -       0x00000060   Zero   RW          288    .bss.hdma_usart1_tx  usart.o
    0x20012c6c        -       0x00000050   Zero   RW          257    .bss.hsram1         fsmc.o
    0x20012cbc        -       0x00000048   Zero   RW          273    .bss.htim5          tim.o
    0x20012d04        -       0x00000048   Zero   RW          287    .bss.huart1         usart.o
    0x20012d4c        -       0x0000000e   Zero   RW           89    .bss.lcddev         lcd.o
    0x20012d5a   0x0802b0a8   0x00000002   PAD
    0x20012d5c        -       0x00000004   Zero   RW          178    .bss.sampling_frequency_sweep  main.o
    0x20012d60        -       0x00000001   Zero   RW          170    .bss.stop_flag      main.o
    0x20012d61   0x0802b0a8   0x00000003   PAD
    0x20012d64        -       0x00000004   Zero   RW          841    .bss.uwTick         stm32f4xx_hal.o
    0x20012d68        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0802b0a8, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1212          8          0          7          3       3005   ad9959.o
       702          0          0          0        336       5396   adc.o
       310          0          0          0        116       4533   dac.o
       150          0          0          0          0       3327   dma.o
       446          0          0          0         84       5339   fsmc.o
       676          0          0          0          0       2389   gpio.o
     14766         46      12160          4         94      16073   lcd.o
      5250        154        228         12      76229      15293   main.o
        36          8        392          0       1024        840   startup_stm32f407xx.o
       270          0          0          5          4       7109   stm32f4xx_hal.o
      2124          0          0          0          0      10057   stm32f4xx_hal_adc.o
       482          0          0          0          0       9683   stm32f4xx_hal_cortex.o
       934          0          0          0          0       6841   stm32f4xx_hal_dac.o
       112          0          0          0          0       5037   stm32f4xx_hal_dac_ex.o
      2068          8          8          0          0       8466   stm32f4xx_hal_dma.o
      1042          0          0          0          0       5085   stm32f4xx_hal_gpio.o
        74          0          0          0          0       1475   stm32f4xx_hal_msp.o
      2646          0          0          0          0       7588   stm32f4xx_hal_rcc.o
       128          0          0          0          0       8348   stm32f4xx_hal_sram.o
      2212         14          0          0          0      35831   stm32f4xx_hal_tim.o
       290          0          0          0          0      15257   stm32f4xx_hal_tim_ex.o
      2346          0          0          0          0      20312   stm32f4xx_hal_uart.o
       114          0          0          0          0       1518   stm32f4xx_it.o
       360          0          0          0          0       6095   stm32f4xx_ll_fsmc.o
        18          0         24          4          0       2505   system_stm32f4xx.o
       276          0          0          0         72       5603   tim.o
       312          0          0          0        168       6819   usart.o

    ----------------------------------------------------------------------
     39520        <USER>      <GROUP>         36      78144     219824   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       164          0          0          4         14          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       180          0          0          0          0       1283   arm_cfft_init_f32.o
         0          0     119768          0          0       6294   arm_common_tables.o
         0          0        144          0          0       4820   arm_const_structs.o
       466          0          0          0          0       3844   arm_rfft_fast_init_f32.o
       684         90          0          0          0        208   atan2f.o
       336         56          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        44         12          0          0          0        464   funder.o
       154          0          0          0          0        140   roundf.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
        58          0          0          0          0        136   sqrtf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o
        54          0          0          0          0         80   dcmpge.o
        54          0          0          0          0         80   dcmple.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      3944        <USER>     <GROUP>          4          0      19405   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       646          0     119912          0          0      16241   arm_cortexM4lf_math.lib
      2054        238         32          0          0       1572   m_wm.l
       330         22          0          4          0        540   mc_w.l
       902          0          0          0          0       1052   mf_w.l

    ----------------------------------------------------------------------
      3944        <USER>     <GROUP>          4          0      19405   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     43464        498     132788         40      78144     237137   Grand Totals
     43464        498     132788         40      78144     237137   ELF Image Totals
     43464        498     132788         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               176252 ( 172.12kB)
    Total RW  Size (RW Data + ZI Data)             78184 (  76.35kB)
    Total ROM Size (Code + RO Data + RW Data)     176292 ( 172.16kB)

==============================================================================

