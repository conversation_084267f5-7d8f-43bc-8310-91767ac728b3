--cpu=Cortex-M4.fp.sp
"template\startup_stm32f407xx.o"
"template\lcd.o"
"template\ad9959.o"
"template\main.o"
"template\gpio.o"
"template\adc.o"
"template\dac.o"
"template\dma.o"
"template\fsmc.o"
"template\tim.o"
"template\usart.o"
"template\stm32f4xx_it.o"
"template\stm32f4xx_hal_msp.o"
"template\stm32f4xx_hal_adc.o"
"template\stm32f4xx_hal_adc_ex.o"
"template\stm32f4xx_ll_adc.o"
"template\stm32f4xx_hal_rcc.o"
"template\stm32f4xx_hal_rcc_ex.o"
"template\stm32f4xx_hal_flash.o"
"template\stm32f4xx_hal_flash_ex.o"
"template\stm32f4xx_hal_flash_ramfunc.o"
"template\stm32f4xx_hal_gpio.o"
"template\stm32f4xx_hal_dma_ex.o"
"template\stm32f4xx_hal_dma.o"
"template\stm32f4xx_hal_pwr.o"
"template\stm32f4xx_hal_pwr_ex.o"
"template\stm32f4xx_hal_cortex.o"
"template\stm32f4xx_hal.o"
"template\stm32f4xx_hal_exti.o"
"template\stm32f4xx_hal_dac.o"
"template\stm32f4xx_hal_dac_ex.o"
"template\stm32f4xx_ll_fsmc.o"
"template\stm32f4xx_hal_nor.o"
"template\stm32f4xx_hal_sram.o"
"template\stm32f4xx_hal_nand.o"
"template\stm32f4xx_hal_pccard.o"
"template\stm32f4xx_hal_tim.o"
"template\stm32f4xx_hal_tim_ex.o"
"template\stm32f4xx_hal_uart.o"
"template\system_stm32f4xx.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4lf_math.lib"
--library_type=microlib --strict --scatter "template\template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "template.map" -o template\template.axf