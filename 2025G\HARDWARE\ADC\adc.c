#include "adc.h"
#include "delay.h"
#include "string.h"
#include "misc.h"

/* ======= 前向声明 ======= */
typedef enum {
    STATE_IDLE,
    STATE_SWEEPING,
    STATE_ANALYZING,
    STATE_DISPLAY_RESULTS,
    STATE_SIMULATING
} AppState;

/* ======= 外部变量声明 ======= */
extern volatile AppState g_app_state;
extern volatile uint16_t g_dac_output_buffer[];
extern volatile float g_current_signal_rate;
extern volatile float g_current_sampling_simulation;

/* ======= 外部函数声明 ======= */
extern void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                                  volatile uint16_t* dac_dest,
                                  float signal_base_freq_hz,
                                  float sampling_freq_hz);

/* ======= 全局变量定义 ======= */
volatile uint16_t g_adc1_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
volatile uint16_t g_adc2_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
uint16_t g_adc1_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
uint16_t g_adc2_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
volatile uint8_t g_adc1_data_ready = 0;
volatile uint8_t g_adc2_data_ready = 0;

/* ======= 模式3 ADC初始化 ======= */
void ADC_Mode3_Init(void)
{
    printf("Initializing ADC for Mode 3...\r\n");

    // 使能DMA2时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);

    // 初始化ADC1和ADC2
    ADC1_Mode3_Init();
    ADC2_Mode3_Init();

    // 配置NVIC中断优先级
    NVIC_InitTypeDef NVIC_InitStructure;

    // ADC1 DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // ADC2 DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream2_IRQn;
    NVIC_Init(&NVIC_InitStructure);

    printf("ADC Mode 3 initialization complete.\r\n");
}

/* ======= ADC1初始化 ======= */
void ADC1_Mode3_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    ADC_InitTypeDef ADC_InitStructure;
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    DMA_InitTypeDef DMA_InitStructure;

    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);

    // 配置GPIO PA1为模拟输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置ADC公共参数
    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled;
    ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;
    ADC_CommonInit(&ADC_CommonInitStructure);

    // 配置ADC1
    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfConversion = 1;
    ADC_Init(ADC1, &ADC_InitStructure);

    // 配置ADC1通道1
    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_3Cycles);

    // 配置DMA2 Stream0
    DMA_InitStructure.DMA_Channel = DMA_Channel_0;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC1->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)g_adc1_dma_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = ADC_TOTAL_BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_Init(DMA2_Stream0, &DMA_InitStructure);

    // 使能DMA中断
    DMA_ITConfig(DMA2_Stream0, DMA_IT_TC | DMA_IT_HT, ENABLE);

    // 使能ADC DMA请求
    ADC_DMARequestAfterLastTransferCmd(ADC1, ENABLE);
    ADC_DMACmd(ADC1, ENABLE);
}

/* ======= ADC2初始化 ======= */
void ADC2_Mode3_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    ADC_InitTypeDef ADC_InitStructure;
    DMA_InitTypeDef DMA_InitStructure;

    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC2, ENABLE);

    // 配置GPIO PA2为模拟输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置ADC2
    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfConversion = 1;
    ADC_Init(ADC2, &ADC_InitStructure);

    // 配置ADC2通道2
    ADC_RegularChannelConfig(ADC2, ADC_Channel_2, 1, ADC_SampleTime_3Cycles);

    // 配置DMA2 Stream2
    DMA_InitStructure.DMA_Channel = DMA_Channel_1;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC2->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)g_adc2_dma_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = ADC_TOTAL_BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_Init(DMA2_Stream2, &DMA_InitStructure);

    // 使能DMA中断
    DMA_ITConfig(DMA2_Stream2, DMA_IT_TC | DMA_IT_HT, ENABLE);

    // 使能ADC DMA请求
    ADC_DMARequestAfterLastTransferCmd(ADC2, ENABLE);
    ADC_DMACmd(ADC2, ENABLE);
}

/* ======= 开始ADC采样 ======= */
void ADC_Start_Sampling(void)
{
    g_adc1_data_ready = 0;
    g_adc2_data_ready = 0;

    // 启动DMA
    DMA_Cmd(DMA2_Stream0, ENABLE);
    DMA_Cmd(DMA2_Stream2, ENABLE);

    // 启动ADC
    ADC_Cmd(ADC1, ENABLE);
    ADC_Cmd(ADC2, ENABLE);

    printf("ADC sampling started.\r\n");
}

/* ======= 停止ADC采样 ======= */
void ADC_Stop_Sampling(void)
{
    // 停止ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);

    // 停止DMA
    DMA_Cmd(DMA2_Stream0, DISABLE);
    DMA_Cmd(DMA2_Stream2, DISABLE);

    printf("ADC sampling stopped.\r\n");
}

/* ======= DMA中断处理函数 ======= */
void DMA2_Stream0_IRQHandler(void)
{
    // ADC1 DMA中断处理
    if (DMA_GetITStatus(DMA2_Stream0, DMA_IT_HTIF0)) {
        DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_HTIF0);
        // 半传输完成 - 处理前半部分数据
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[0],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    }

    if (DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0)) {
        DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
        // 传输完成 - 处理后半部分数据
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    }
}

void DMA2_Stream2_IRQHandler(void)
{
    // ADC2 DMA中断处理
    if (DMA_GetITStatus(DMA2_Stream2, DMA_IT_HTIF2)) {
        DMA_ClearITPendingBit(DMA2_Stream2, DMA_IT_HTIF2);
        // 半传输完成 - 处理前半部分数据
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[0],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        } else if (g_app_state == STATE_SIMULATING) {
            // 在仿真模式下处理ADC数据
            Process_Buffer_DFT_SIM((const uint16_t*)&g_adc2_dma_buffer[0],
                                  &g_dac_output_buffer[0],
                                  g_current_signal_rate,
                                  g_current_sampling_simulation);
        }
    }

    if (DMA_GetITStatus(DMA2_Stream2, DMA_IT_TCIF2)) {
        DMA_ClearITPendingBit(DMA2_Stream2, DMA_IT_TCIF2);
        // 传输完成 - 处理后半部分数据
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        } else if (g_app_state == STATE_SIMULATING) {
            // 在仿真模式下处理ADC数据
            Process_Buffer_DFT_SIM((const uint16_t*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                                  &g_dac_output_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                                  g_current_signal_rate,
                                  g_current_sampling_simulation);
        }
    }
}
