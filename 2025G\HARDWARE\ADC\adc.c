#include "adc.h"
#include "delay.h"
#include "string.h"

/* ======= 前向声明 ======= */
typedef enum {
    STATE_IDLE,
    STATE_SWEEPING,
    STATE_ANALYZING,
    STATE_DISPLAY_RESULTS,
    STATE_SIMULATING
} AppState;

/* ======= 外部变量声明 ======= */
extern volatile AppState g_app_state;
extern volatile uint16_t g_dac_output_buffer[];
extern volatile float g_current_signal_rate;
extern volatile float g_current_sampling_simulation;

/* ======= 外部函数声明 ======= */
extern void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                                  volatile uint16_t* dac_dest,
                                  float signal_base_freq_hz,
                                  float sampling_freq_hz);

/* ======= 全局变量定义 ======= */
volatile uint16_t g_adc1_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
volatile uint16_t g_adc2_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
uint16_t g_adc1_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
uint16_t g_adc2_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
volatile uint8_t g_adc1_data_ready = 0;
volatile uint8_t g_adc2_data_ready = 0;

/* ======= 静态变量 ======= */
static ADC_HandleTypeDef hadc1_mode3;
static ADC_HandleTypeDef hadc2_mode3;
static DMA_HandleTypeDef hdma_adc1_mode3;
static DMA_HandleTypeDef hdma_adc2_mode3;

/* ======= 模式3 ADC初始化 ======= */
void ADC_Mode3_Init(void)
{
    printf("Initializing ADC for Mode 3...\r\n");
    
    // 初始化DMA
    __HAL_RCC_DMA2_CLK_ENABLE();
    
    // 初始化ADC1和ADC2
    ADC1_Init();
    ADC2_Init();
    
    // 配置NVIC中断优先级
    HAL_NVIC_SetPriority(DMA2_Stream0_IRQn, 1, 0);  // ADC1 DMA
    HAL_NVIC_EnableIRQ(DMA2_Stream0_IRQn);
    
    HAL_NVIC_SetPriority(DMA2_Stream2_IRQn, 1, 0);  // ADC2 DMA
    HAL_NVIC_EnableIRQ(DMA2_Stream2_IRQn);
    
    printf("ADC Mode 3 initialization complete.\r\n");
}

/* ======= ADC1初始化 ======= */
void ADC1_Init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能时钟
    __HAL_RCC_ADC1_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // 配置GPIO
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 配置ADC1
    hadc1_mode3.Instance = ADC1;
    hadc1_mode3.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1_mode3.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1_mode3.Init.ScanConvMode = DISABLE;
    hadc1_mode3.Init.ContinuousConvMode = DISABLE;
    hadc1_mode3.Init.DiscontinuousConvMode = DISABLE;
    hadc1_mode3.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_RISING;
    hadc1_mode3.Init.ExternalTrigConv = ADC_EXTERNALTRIGCONV_T3_TRGO;  // 使用TIM3触发
    hadc1_mode3.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1_mode3.Init.NbrOfConversion = 1;
    hadc1_mode3.Init.DMAContinuousRequests = ENABLE;
    hadc1_mode3.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    
    if (HAL_ADC_Init(&hadc1_mode3) != HAL_OK) {
        printf("ADC1 initialization failed!\r\n");
        return;
    }
    
    // 配置通道
    sConfig.Channel = ADC_CHANNEL_1;
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
    if (HAL_ADC_ConfigChannel(&hadc1_mode3, &sConfig) != HAL_OK) {
        printf("ADC1 channel configuration failed!\r\n");
        return;
    }
    
    // 配置DMA
    hdma_adc1_mode3.Instance = DMA2_Stream0;
    hdma_adc1_mode3.Init.Channel = DMA_CHANNEL_0;
    hdma_adc1_mode3.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_adc1_mode3.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_adc1_mode3.Init.MemInc = DMA_MINC_ENABLE;
    hdma_adc1_mode3.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_adc1_mode3.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_adc1_mode3.Init.Mode = DMA_CIRCULAR;
    hdma_adc1_mode3.Init.Priority = DMA_PRIORITY_HIGH;
    hdma_adc1_mode3.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    
    if (HAL_DMA_Init(&hdma_adc1_mode3) != HAL_OK) {
        printf("ADC1 DMA initialization failed!\r\n");
        return;
    }
    
    __HAL_LINKDMA(&hadc1_mode3, DMA_Handle, hdma_adc1_mode3);
}

/* ======= ADC2初始化 ======= */
void ADC2_Init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能时钟
    __HAL_RCC_ADC2_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    // 配置GPIO
    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 配置ADC2
    hadc2_mode3.Instance = ADC2;
    hadc2_mode3.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc2_mode3.Init.Resolution = ADC_RESOLUTION_12B;
    hadc2_mode3.Init.ScanConvMode = DISABLE;
    hadc2_mode3.Init.ContinuousConvMode = DISABLE;
    hadc2_mode3.Init.DiscontinuousConvMode = DISABLE;
    hadc2_mode3.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_RISING;
    hadc2_mode3.Init.ExternalTrigConv = ADC_EXTERNALTRIGCONV_T3_TRGO;  // 使用TIM3触发
    hadc2_mode3.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc2_mode3.Init.NbrOfConversion = 1;
    hadc2_mode3.Init.DMAContinuousRequests = ENABLE;
    hadc2_mode3.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    
    if (HAL_ADC_Init(&hadc2_mode3) != HAL_OK) {
        printf("ADC2 initialization failed!\r\n");
        return;
    }
    
    // 配置通道
    sConfig.Channel = ADC_CHANNEL_2;
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
    if (HAL_ADC_ConfigChannel(&hadc2_mode3, &sConfig) != HAL_OK) {
        printf("ADC2 channel configuration failed!\r\n");
        return;
    }
    
    // 配置DMA
    hdma_adc2_mode3.Instance = DMA2_Stream2;
    hdma_adc2_mode3.Init.Channel = DMA_CHANNEL_1;
    hdma_adc2_mode3.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_adc2_mode3.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_adc2_mode3.Init.MemInc = DMA_MINC_ENABLE;
    hdma_adc2_mode3.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_adc2_mode3.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_adc2_mode3.Init.Mode = DMA_CIRCULAR;
    hdma_adc2_mode3.Init.Priority = DMA_PRIORITY_HIGH;
    hdma_adc2_mode3.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    
    if (HAL_DMA_Init(&hdma_adc2_mode3) != HAL_OK) {
        printf("ADC2 DMA initialization failed!\r\n");
        return;
    }
    
    __HAL_LINKDMA(&hadc2_mode3, DMA_Handle, hdma_adc2_mode3);
}

/* ======= 获取ADC句柄 ======= */
ADC_HandleTypeDef* ADC_Get_Handle1(void)
{
    return &hadc1_mode3;
}

ADC_HandleTypeDef* ADC_Get_Handle2(void)
{
    return &hadc2_mode3;
}

/* ======= 开始ADC采样 ======= */
void ADC_Start_Sampling(void)
{
    g_adc1_data_ready = 0;
    g_adc2_data_ready = 0;

    if (HAL_ADC_Start_DMA(&hadc1_mode3, (uint32_t*)g_adc1_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
        printf("ADC1 DMA start failed!\r\n");
        return;
    }

    if (HAL_ADC_Start_DMA(&hadc2_mode3, (uint32_t*)g_adc2_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
        printf("ADC2 DMA start failed!\r\n");
        return;
    }

    printf("ADC sampling started.\r\n");
}

/* ======= 停止ADC采样 ======= */
void ADC_Stop_Sampling(void)
{
    HAL_ADC_Stop_DMA(&hadc1_mode3);
    HAL_ADC_Stop_DMA(&hadc2_mode3);
    printf("ADC sampling stopped.\r\n");
}

/* ======= DMA中断处理函数 ======= */
void DMA2_Stream0_IRQHandler(void)
{
    HAL_DMA_IRQHandler(&hdma_adc1_mode3);
}

void DMA2_Stream2_IRQHandler(void)
{
    HAL_DMA_IRQHandler(&hdma_adc2_mode3);
}

/* ======= ADC DMA回调函数 ======= */
void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef* hadc)
{
    if (hadc->Instance == ADC1) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[0],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    } else if (hadc->Instance == ADC2) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[0],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        } else if (g_app_state == STATE_SIMULATING) {
            // 在仿真模式下处理ADC数据
            Process_Buffer_DFT_SIM((const uint16_t*)&g_adc2_dma_buffer[0],
                                  &g_dac_output_buffer[0],
                                  g_current_signal_rate,
                                  g_current_sampling_simulation);
        }
    }
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    if (hadc->Instance == ADC1) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc1_data_ready = 1;
        }
    } else if (hadc->Instance == ADC2) {
        if (g_app_state == STATE_SWEEPING) {
            memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                   ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
            g_adc2_data_ready = 1;
        } else if (g_app_state == STATE_SIMULATING) {
            // 在仿真模式下处理ADC数据
            Process_Buffer_DFT_SIM((const uint16_t*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                                  &g_dac_output_buffer[ADC_SAMPLES_PER_HALF_BUFFER],
                                  g_current_signal_rate,
                                  g_current_sampling_simulation);
        }
    }
}
