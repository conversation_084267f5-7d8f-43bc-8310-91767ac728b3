#ifndef __DAC_H
#define __DAC_H

#include "stm32f4xx.h"
#include "stm32f4xx_hal.h"
#include "stdio.h"
#include "adc.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= DAC配置参数 ======= */
#define DAC_RESOLUTION              4096

/* ======= 外部变量声明 ======= */
extern volatile uint16_t g_dac_output_buffer[ADC_TOTAL_BUFFER_SIZE];

/* ======= 函数声明 ======= */
void DAC_Mode3_Init(void);
void DAC_Start_Output(void);
void DAC_Stop_Output(void);

#ifdef __cplusplus
}
#endif

#endif /* __DAC_H */
