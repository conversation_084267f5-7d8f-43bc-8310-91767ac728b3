#include "matrix_kbd.h"
#include <string.h>

static volatile uint8_t kbdBuf[KBD_BUF_SIZE];
static volatile uint8_t head = 0, tail = 0;
static volatile uint32_t currentBaudRate = KBD_UART_BAUD_DEFAULT;

static void pushKey(uint8_t key)
{
    uint8_t nxt = (uint8_t)((tail + 1u) % KBD_BUF_SIZE);
    if (nxt != head) {
        kbdBuf[tail] = key;
        tail = nxt;
    }
}

uint8_t MatrixKBD_Available(void)
{
    return (uint8_t)((tail + KBD_BUF_SIZE - head) % KBD_BUF_SIZE);
}

uint8_t MatrixKBD_GetKey(void)
{
    uint8_t key;
    if (head == tail) return 0;
    key = kbdBuf[head];
    head = (uint8_t)((head + 1u) % KBD_BUF_SIZE);
    return key;
}

uint8_t MatrixKBD_KeyToDigit(uint8_t key)
{
    switch(key) {
        case KBD_NUM_0: return 0;
        case KBD_NUM_1: return 1;
        case KBD_NUM_2: return 2;
        case KBD_NUM_3: return 3;
        case KBD_NUM_4: return 4;
        case KBD_NUM_5: return 5;
        case KBD_NUM_6: return 6;
        case KBD_NUM_7: return 7;
        case KBD_NUM_8: return 8;
        case KBD_NUM_9: return 9;
        default: return 0xFF;  
    }
}

static void USART3_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
    GPIO_PinAFConfig(GPIOB, GPIO_PinSource10, GPIO_AF_USART3);   /* TX */
    GPIO_PinAFConfig(GPIOB, GPIO_PinSource11, GPIO_AF_USART3);   /* RX */

    GPIO_InitStructure.GPIO_Pin   = GPIO_Pin_10 | GPIO_Pin_11;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
}

static void USART3_Config_Core(void)
{
    USART_InitTypeDef USART_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);

    USART_InitStructure.USART_BaudRate            = currentBaudRate;
    USART_InitStructure.USART_WordLength          = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits            = USART_StopBits_1;
    USART_InitStructure.USART_Parity              = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode                = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART3, &USART_InitStructure);

    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
    USART_Cmd(USART3, ENABLE);
}

static void NVIC_Config_USART3(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;  // 降低优先级，让DMA优先
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

void MatrixKBD_Init(void)
{
    currentBaudRate = KBD_UART_BAUD_DEFAULT;  
    USART3_GPIO_Config();
    USART3_Config_Core();
    NVIC_Config_USART3();
}

void MatrixKBD_SetBaudRate(uint32_t baud)
{
    if (baud != KBD_UART_BAUD_DEFAULT && baud != KBD_UART_BAUD_HIGH) {
        return;  
    }

    currentBaudRate = baud;

    USART_Cmd(USART3, DISABLE);
    USART3_Config_Core();
}

static uint8_t  rxState = 0;   
static uint8_t  keyTemp = 0;
static uint8_t  cmdBuf[3] = {0}; 
static uint8_t  cmdIndex = 0;

static void parseByte(uint8_t byte)
{
    if (rxState == 0 && byte == 0xFE) {
        cmdIndex = 0;
        cmdBuf[cmdIndex++] = byte;
        return;
    }

    if (cmdIndex > 0 && cmdIndex < 3) {
        cmdBuf[cmdIndex++] = byte;
        if (cmdIndex == 3) {
            if (cmdBuf[0] == 0xFE && cmdBuf[2] == 0xAA) {
                if (cmdBuf[1] == 0x01) {
                    MatrixKBD_SetBaudRate(KBD_UART_BAUD_DEFAULT);  /* 9600 */
                } else if (cmdBuf[1] == 0x03) {
                    MatrixKBD_SetBaudRate(KBD_UART_BAUD_HIGH);     /* 115200 */
                }
            }
            cmdIndex = 0;  
        }
        return;
    }

    switch (rxState)
    {
        case 0: 
            if (byte == 0xFF) rxState = 1;
            break;

        case 1:  
            keyTemp = byte;
            rxState = 2;
            break;

        case 2:  
            if (byte == 0xEE)
                pushKey(keyTemp);   
            rxState = 0;
            break;

        default: rxState = 0; break;
    }
}

void USART3_IRQHandler(void)
{
    if (USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t data = (uint8_t)USART_ReceiveData(USART3);
        parseByte(data);
    }
}
