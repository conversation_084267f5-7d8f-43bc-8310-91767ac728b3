#ifndef __DFT_H
#define __DFT_H

#include "stm32f4xx.h"
#include "stdio.h"
#include "math.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= DFT配置参数 ======= */
#define FFT_SIZE                    1024
#define NUM_HARMONICS               10
#define NUM_COMPONENTS              (NUM_HARMONICS + 1)
#define ADC_SAMPLES_PER_HALF_BUFFER 3000
#define ADC_VREF_V                  3.3f
#define ADC_RESOLUTION              4096

#ifndef M_PI
#define M_PI                        3.14159265358979323846f
#endif

/* ======= 函数声明 ======= */
void DFT_Init(void);
void Get_Signal_Accurate_Amplitude_And_Phase(const uint16_t* processing_buffer,
                                             float target_frequency_hz,
                                             float current_sampling_freq_hz,
                                             float* p_amplitude_v,
                                             float* p_phase_rad);
void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                           volatile uint16_t* dac_dest,
                           float signal_base_freq_hz,
                           float sampling_freq_hz);

#ifdef __cplusplus
}
#endif

#endif /* __DFT_H */
