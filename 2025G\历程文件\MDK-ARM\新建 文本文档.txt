/**
 * @brief  [MODIFIED] ADC DMA half-transfer complete callback.
 * @param  hadc: ADC handle
 */
void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[0], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
        g_adc1_data_ready = 1;
    }
    else if (hadc->Instance == ADC2) {
        memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[0], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
        g_adc2_data_ready = 1;
    }
    /* --- NEW: Handle ADC3 for simulation mode --- */
    else if (hadc->Instance == ADC3) {
        // The first half of the ADC3 DMA buffer is full.
        // Process this data and fill the first half of the DAC output buffer.
        // The DAC DMA is simultaneously reading from the *second* half of its buffer.
        Process_And_Fill_DAC_Buffer((const uint16_t*)&g_adc3_dma_buffer[0], &g_dac_output_buffer[0], ADC3_DAC_BUFFER_SAMPLES);
    }
}

/**
 * @brief  [MODIFIED] ADC DMA full-transfer complete callback.
 * @param  hadc: ADC handle
 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        memcpy(g_adc1_processing_buffer, (const void*)&g_adc1_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
        g_adc1_data_ready = 1;
    }
    else if (hadc->Instance == ADC2) {
        memcpy(g_adc2_processing_buffer, (const void*)&g_adc2_dma_buffer[ADC_SAMPLES_PER_HALF_BUFFER], ADC_SAMPLES_PER_HALF_BUFFER * sizeof(uint16_t));
        g_adc2_data_ready = 1;
    }
    /* --- NEW: Handle ADC3 for simulation mode --- */
    else if (hadc->Instance == ADC3) {
        // The second half of the ADC3 DMA buffer is full.
        // Process this data and fill the second half of the DAC output buffer.
        // The DAC DMA is simultaneously reading from the *first* half of its buffer.
        Process_And_Fill_DAC_Buffer((const uint16_t*)&g_adc3_dma_buffer[ADC3_DAC_BUFFER_SAMPLES], &g_dac_output_buffer[ADC3_DAC_BUFFER_SAMPLES], ADC3_DAC_BUFFER_SAMPLES);
    }
}