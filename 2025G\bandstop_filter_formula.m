%% 二阶带阻滤波器从s域到z域转换 - 通用公式推导
% 作者：STM32项目组
% 日期：2025-07-30
% 功能：推导带阻滤波器 H(s) = c(s^2 + ω0^2)/(s^2 + as + b) 的通用z域差分方程公式

clear all;
close all;
clc;

fprintf('========================================\n');
fprintf('二阶带阻滤波器 - 通用公式推导\n');
fprintf('========================================\n');

%% 原始传递函数
fprintf('原始传递函数: H(s) = c(s^2 + ω0^2)/(s^2 + as + b)\n');
fprintf('标准形式: H(s) = c(s^2 + b)/(s^2 + as + b)  (设 ω0^2 = b)\n');
fprintf('其中 a, b, c, T 为未知参数\n\n');

%% 步骤1：带阻滤波器分解
fprintf('=== 步骤1：带阻滤波器分解 ===\n');
fprintf('将带阻滤波器重写为:\n');
fprintf('H(s) = c(s^2 + b)/(s^2 + as + b) = c - c×as/(s^2 + as + b)\n');
fprintf('= c + H_bp(s)\n');
fprintf('其中 H_bp(s) = -cas/(s^2 + as + b) 是带通部分\n\n');

fprintf('这样带阻滤波器 = 直流增益 c + 带通滤波器(-ca)\n\n');

%% 步骤2：对带通部分进行分解
fprintf('=== 步骤2：带通部分的部分分式分解 ===\n');
fprintf('对 H_bp(s) = -cas/(s^2 + as + b) 进行分解\n');
fprintf('特征方程: s^2 + as + b = 0\n');
fprintf('判别式: Δ = a^2 - 4b\n\n');

fprintf('情况1: Δ ≥ 0 (实根情况)\n');
fprintf('根: s1 = (-a + √Δ)/2,  s2 = (-a - √Δ)/2\n');
fprintf('极点: p1 = -s1 = (a - √Δ)/2,  p2 = -s2 = (a + √Δ)/2\n\n');

fprintf('带通部分的部分分式分解:\n');
fprintf('H_bp(s) = -cas/((s - s1)(s - s2)) = A/(s - s1) + B/(s - s2)\n');
fprintf('其中: A = -ca×s1/(s1 - s2),  B = -ca×s2/(s2 - s1)\n');
fprintf('转换为标准形式: H_bp(s) = A1/(s + p1) + A2/(s + p2)\n');
fprintf('A1 = -A = ca×s1/(s1 - s2) = -ca×s1/√Δ\n');
fprintf('A2 = -B = ca×s2/(s2 - s1) = ca×s2/√Δ\n\n');

fprintf('情况2: Δ < 0 (复根情况)\n');
fprintf('σ = -a/2,  ω = √(-Δ)/2 = √(4b - a^2)/2\n');
fprintf('复根: s1,2 = σ ± jω\n');
fprintf('此时直接转换为二阶数字滤波器\n\n');

%% 步骤3：双线性变换
fprintf('=== 步骤3：双线性变换 ===\n');
fprintf('变换公式: s = (2/T) × (z-1)/(z+1)\n\n');

fprintf('实根情况的变换:\n');
fprintf('对带通部分的两个一阶滤波器分别变换:\n');
fprintf('b0_1 = b1_1 = A1×T/(2 + p1×T) = -ca×s1×T/((2 + p1×T)×√Δ)\n');
fprintf('a1_1 = (p1×T - 2)/(2 + p1×T)\n\n');
fprintf('b0_2 = b1_2 = A2×T/(2 + p2×T) = ca×s2×T/((2 + p2×T)×√Δ)\n');
fprintf('a1_2 = (p2×T - 2)/(2 + p2×T)\n\n');

fprintf('注意：s1 = (-a + √Δ)/2, s2 = (-a - √Δ)/2\n');
fprintf('所以: ca×s1 = ca×(-a + √Δ)/2, ca×s2 = ca×(-a - √Δ)/2\n\n');

fprintf('复根情况的变换:\n');
fprintf('直接转换为二阶IIR滤波器，需要在陷波频率处产生零点\n\n');

%% 步骤4：最终差分方程
fprintf('=== 步骤4：最终差分方程公式 ===\n\n');

fprintf('实根情况 (Δ ≥ 0):\n');
fprintf('带阻输出 = 直流增益 + 带通部分\n');
fprintf('y_bp1[n] = b0_1×x[n] + b1_1×x[n-1] - a1_1×y_bp1[n-1]\n');
fprintf('y_bp2[n] = b0_2×x[n] + b1_2×x[n-1] - a1_2×y_bp2[n-1]\n');
fprintf('y[n] = c×x[n] + y_bp1[n] + y_bp2[n]\n\n');

fprintf('注意：实际实现中，直流增益c通过差分实现\n');
fprintf('简化为: y[n] = y_bp1[n] + y_bp2[n] + c×x[n]\n\n');

fprintf('其中系数为:\n');
fprintf('Δ = a^2 - 4b\n');
fprintf('s1 = (-a + √Δ)/2,  s2 = (-a - √Δ)/2\n');
fprintf('p1 = (a - √Δ)/2,  p2 = (a + √Δ)/2\n');
fprintf('b0_1 = b1_1 = -ca×(-a + √Δ)×T/(2×(2 + p1×T)×√Δ)\n');
fprintf('b0_2 = b1_2 = ca×(-a - √Δ)×T/(2×(2 + p2×T)×√Δ)\n');
fprintf('a1_1 = (p1×T - 2)/(2 + p1×T)\n');
fprintf('a1_2 = (p2×T - 2)/(2 + p2×T)\n\n');

fprintf('简化后:\n');
fprintf('b0_1 = b1_1 = ca×(a - √Δ)×T/(2×(2 + p1×T)×√Δ)\n');
fprintf('b0_2 = b1_2 = -ca×(a + √Δ)×T/(2×(2 + p2×T)×√Δ)\n\n');

fprintf('复根情况 (Δ < 0):\n');
fprintf('使用单个二阶滤波器:\n');
fprintf('y[n] = b0×x[n] + b1×x[n-1] + b2×x[n-2] - a1×y[n-1] - a2×y[n-2]\n\n');

fprintf('其中系数计算:\n');
fprintf('σ = -a/2,  ω = √(4b - a^2)/2\n');
fprintf('α = 2/T + σ,  β = ω\n');
fprintf('z_real = (α×(α-2σ) + β^2)/((α-2σ)^2 + β^2)\n');
fprintf('z_imag = (β×(-2σ))/((α-2σ)^2 + β^2)\n');
fprintf('r = √(z_real^2 + z_imag^2)\n');
fprintf('θ = atan2(z_imag, z_real)\n');
fprintf('a1 = -2r×cos(θ)\n');
fprintf('a2 = r^2\n\n');

fprintf('带阻滤波器的分子系数 (在陷波频率处产生零点):\n');
fprintf('ω0 = √b  (陷波频率)\n');
fprintf('z_notch = (2/T + jω0)/(2/T - jω0)  (陷波频率对应的z域零点)\n');
fprintf('K = c×(4 + b×T^2)/(4 + 2σ×T + (σ^2 + ω^2)×T^2)\n');
fprintf('b0 = K,  b1 = -2K×(4 - b×T^2)/(4 + b×T^2),  b2 = K\n\n');

%% 步骤5：单片机实现模板
fprintf('=== 步骤5：单片机实现模板 ===\n\n');

fprintf('实根情况的C代码模板:\n');
fprintf('float bandstop_filter_real(float input) {\n');
fprintf('    static float x1_1=0, y1_1=0, x1_2=0, y1_2=0;\n');
fprintf('    \n');
fprintf('    // 计算系数\n');
fprintf('    float delta = a*a - 4*b;\n');
fprintf('    float sqrt_delta = sqrt(delta);\n');
fprintf('    float s1 = (-a + sqrt_delta)/2;\n');
fprintf('    float s2 = (-a - sqrt_delta)/2;\n');
fprintf('    float p1 = (a - sqrt_delta)/2;\n');
fprintf('    float p2 = (a + sqrt_delta)/2;\n');
fprintf('    \n');
fprintf('    float b0_1 = -c*a*s1*T/((2 + p1*T)*sqrt_delta);\n');
fprintf('    float b1_1 = b0_1;\n');
fprintf('    float a1_1 = (p1*T - 2)/(2 + p1*T);\n');
fprintf('    \n');
fprintf('    float b0_2 = c*a*s2*T/((2 + p2*T)*sqrt_delta);\n');
fprintf('    float b1_2 = b0_2;\n');
fprintf('    float a1_2 = (p2*T - 2)/(2 + p2*T);\n');
fprintf('    \n');
fprintf('    // 带通部分1\n');
fprintf('    float y_bp1 = b0_1*input + b1_1*x1_1 - a1_1*y1_1;\n');
fprintf('    x1_1 = input; y1_1 = y_bp1;\n');
fprintf('    \n');
fprintf('    // 带通部分2\n');
fprintf('    float y_bp2 = b0_2*input + b1_2*x1_2 - a1_2*y1_2;\n');
fprintf('    x1_2 = input; y1_2 = y_bp2;\n');
fprintf('    \n');
fprintf('    // 带阻输出 = 直流增益 + 带通部分\n');
fprintf('    return c*input + y_bp1 + y_bp2;\n');
fprintf('}\n\n');

fprintf('复根情况的C代码模板:\n');
fprintf('float bandstop_filter_complex(float input) {\n');
fprintf('    static float x1=0, x2=0, y1=0, y2=0;\n');
fprintf('    \n');
fprintf('    // 计算系数\n');
fprintf('    float sigma = -a/2;\n');
fprintf('    float omega = sqrt(4*b - a*a)/2;\n');
fprintf('    float alpha = 2/T + sigma;\n');
fprintf('    float beta = omega;\n');
fprintf('    \n');
fprintf('    float denom = (alpha-2*sigma)*(alpha-2*sigma) + beta*beta;\n');
fprintf('    float z_real = (alpha*(alpha-2*sigma) + beta*beta)/denom;\n');
fprintf('    float z_imag = (beta*(-2*sigma))/denom;\n');
fprintf('    \n');
fprintf('    float r = sqrt(z_real*z_real + z_imag*z_imag);\n');
fprintf('    float theta = atan2(z_imag, z_real);\n');
fprintf('    \n');
fprintf('    float a1_coeff = -2*r*cos(theta);\n');
fprintf('    float a2_coeff = r*r;\n');
fprintf('    \n');
fprintf('    // 带阻滤波器分子系数\n');
fprintf('    float K = c*(4 + b*T*T)/(4 + 2*sigma*T + (sigma*sigma + omega*omega)*T*T);\n');
fprintf('    float b0 = K;\n');
fprintf('    float b1 = -2*K*(4 - b*T*T)/(4 + b*T*T);\n');
fprintf('    float b2 = K;\n');
fprintf('    \n');
fprintf('    float output = b0*input + b1*x1 + b2*x2 - a1_coeff*y1 - a2_coeff*y2;\n');
fprintf('    \n');
fprintf('    x2 = x1; x1 = input;\n');
fprintf('    y2 = y1; y1 = output;\n');
fprintf('    \n');
fprintf('    return output;\n');
fprintf('}\n\n');

%% 总结
fprintf('=== 总结 ===\n');
fprintf('带阻滤波器转换公式已推导完成\n');
fprintf('1. 带阻滤波器 = 直流增益 + 带通部分\n');
fprintf('2. 根据判别式 Δ = a^2 - 4b 选择实现方式\n');
fprintf('3. Δ ≥ 0: 直流增益 + 两个一阶带通滤波器\n');
fprintf('4. Δ < 0: 单个二阶滤波器，在陷波频率处产生零点\n');
fprintf('5. 陷波频率 ω0 = √b\n');
fprintf('6. 所有系数都用 a, b, c, T 的通用公式表示\n');

fprintf('\n========================================\n');
fprintf('带阻滤波器通用公式推导完成\n');
fprintf('========================================\n');
