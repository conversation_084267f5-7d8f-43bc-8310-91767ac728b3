#include "dft.h"

/* ======= 外部变量声明 ======= */
extern float g_H_magnitude[];
extern float g_H_phase[];

/* ======= 常量定义 ======= */
#define SWEEP_START_FREQ_HZ         200
#define SWEEP_END_FREQ_HZ           500000
#define SWEEP_STEP_FREQ_HZ          200
#define NUM_SWEEP_STEPS             ((SWEEP_END_FREQ_HZ - SWEEP_START_FREQ_HZ) / SWEEP_STEP_FREQ_HZ + 1)

/* ======= DFT模块初始化 ======= */
void DFT_Init(void)
{
    printf("DFT module initialized.\r\n");
}

/**
 * @brief  计算单频信号的精确幅度和相位
 * @param  processing_buffer      ADC采样缓冲区
 * @param  target_frequency_hz    目标频率 (Hz)
 * @param  current_sampling_freq_hz 当前采样频率 (Hz)
 * @param  p_amplitude_v          输出幅度 (V)
 * @param  p_phase_rad            输出相位 (弧度)
 */
void Get_Signal_Accurate_Amplitude_And_Phase(const uint16_t* processing_buffer,
                                             float target_frequency_hz,
                                             float current_sampling_freq_hz,
                                             float* p_amplitude_v,
                                             float* p_phase_rad)
{
    // DFT bin公式: f_k = k * fs / N  =>  k = f_k * N / fs
    const float k = target_frequency_hz * (float)ADC_SAMPLES_PER_HALF_BUFFER / current_sampling_freq_hz;
    
    // 计算角频率 w = 2 * PI * k / N
    const float w = 2.0f * M_PI * k / (float)ADC_SAMPLES_PER_HALF_BUFFER;
    
    // 计算直流偏置
    float real_dc_offset = 0.0f;
    for (int i = 0; i < ADC_SAMPLES_PER_HALF_BUFFER; i++) {
        real_dc_offset += (float)processing_buffer[i];
    }
    real_dc_offset /= (float)ADC_SAMPLES_PER_HALF_BUFFER;
    
    // 初始化DFT实部和虚部
    float sum_real = 0.0f;
    float sum_imag = 0.0f;
    
    // 执行单频DFT
    for (int n = 0; n < ADC_SAMPLES_PER_HALF_BUFFER; n++) {
        float sample_no_dc = (float)processing_buffer[n] - real_dc_offset;
        float angle = w * (float)n;
        sum_real += sample_no_dc * cosf(angle);
        sum_imag += sample_no_dc * sinf(angle);
    }
    
    // 计算DFT结果
    float dft_real = sum_real;
    float dft_imag = -sum_imag;
    
    // 计算幅度
    float amplitude_adc_units = (2.0f / (float)ADC_SAMPLES_PER_HALF_BUFFER) * 
                               sqrtf(dft_real * dft_real + dft_imag * dft_imag);
    *p_amplitude_v = amplitude_adc_units * (ADC_VREF_V / (float)ADC_RESOLUTION);
    
    // 计算相位
    *p_phase_rad = atan2f(dft_imag, dft_real);
}

/**
 * @brief  在仿真模式下使用DFT处理ADC数据并生成DAC输出
 * @param  adc_src             源ADC数据缓冲区
 * @param  dac_dest            目标DAC数据缓冲区
 * @param  signal_base_freq_hz 输入信号基频 (Hz)
 * @param  sampling_freq_hz    当前采样频率 (Hz)
 */
void Process_Buffer_DFT_SIM(const uint16_t* adc_src,
                           volatile uint16_t* dac_dest,
                           float signal_base_freq_hz,
                           float sampling_freq_hz)
{
    const int BUFFER_SIZE = ADC_SAMPLES_PER_HALF_BUFFER;
    
    // 存储每个谐波分量的幅度和相位
    float mag_X_V[NUM_COMPONENTS];      // 输入信号 X(w) 的幅度 (伏特)
    float phase_X_rad[NUM_COMPONENTS];  // 输入信号 X(w) 的相位 (弧度)
    float mag_Y_V[NUM_COMPONENTS];      // 输出信号 Y(w) 的幅度 (伏特)
    float phase_Y_rad[NUM_COMPONENTS];  // 输出信号 Y(w) 的相位 (弧度)
    
    // 计算输入信号的直流偏置
    float dc_offset_adc = 0.0f;
    for (int i = 0; i < BUFFER_SIZE; i++) {
        dc_offset_adc += (float)adc_src[i];
    }
    dc_offset_adc /= (float)BUFFER_SIZE;
    
    // 对每个谐波分量进行前向DFT并应用滤波器H(w)
    for (int h = 0; h < NUM_COMPONENTS; h++) {
        // 当前处理的频率 (h=0是基波, h=1是2次谐波, ...)
        float current_freq_hz = signal_base_freq_hz * (float)(h + 1);
        
        // 对当前频率执行单频DFT
        const float k = current_freq_hz * (float)BUFFER_SIZE / sampling_freq_hz;
        const float w = 2.0f * M_PI * k / (float)BUFFER_SIZE;
        float sum_real = 0.0f;
        float sum_imag = 0.0f;
        
        for (int n = 0; n < BUFFER_SIZE; n++) {
            float sample_no_dc = (float)adc_src[n] - dc_offset_adc;
            float angle = w * (float)n;
            sum_real += sample_no_dc * cosf(angle);
            sum_imag += sample_no_dc * sinf(angle);
        }
        
        float dft_real = sum_real;
        float dft_imag = -sum_imag;
        
        // 计算输入信号X(w)的幅度和相位
        float amplitude_adc_units = (2.0f / (float)BUFFER_SIZE) * 
                                   sqrtf(dft_real * dft_real + dft_imag * dft_imag);
        mag_X_V[h] = amplitude_adc_units * (ADC_VREF_V / (float)ADC_RESOLUTION);
        phase_X_rad[h] = atan2f(dft_imag, dft_real);
        
        // 查找对应的滤波器响应H(w)
        float mag_H = 0.0f;   // 默认增益为0
        float phase_H = 0.0f; // 默认相移为0
        
        // 检查频率是否在扫描范围内
        if (current_freq_hz >= SWEEP_START_FREQ_HZ && current_freq_hz <= SWEEP_END_FREQ_HZ) {
            // 计算索引，找到最接近的频率点
            int h_idx = roundf((current_freq_hz - SWEEP_START_FREQ_HZ) / SWEEP_STEP_FREQ_HZ);
            if (h_idx >= 0 && h_idx < NUM_SWEEP_STEPS) {
                mag_H = g_H_magnitude[h_idx];
                phase_H = g_H_phase[h_idx];
            }
        }
        
        // 计算输出 Y(w) = H(w) * X(w)
        mag_Y_V[h] = mag_X_V[h] * mag_H;
        phase_Y_rad[h] = phase_X_rad[h] + phase_H;
    }
    
    // 反向DFT (信号重建)
    const float dc_out_dac = (float)ADC_RESOLUTION / 2.0f; // DAC输出的直流偏置设为中间值
    
    for (int n = 0; n < BUFFER_SIZE; n++) {
        float sample_value_dac = dc_out_dac; // 从直流偏置开始
        
        // 叠加所有谐波分量来重建信号
        for (int h = 0; h < NUM_COMPONENTS; h++) {
            float current_freq_hz = signal_base_freq_hz * (float)(h + 1);
            // 将输出幅度从伏特转换回DAC单位
            float amp_Y_dac = mag_Y_V[h] * ((float)ADC_RESOLUTION / ADC_VREF_V);
            
            sample_value_dac += amp_Y_dac * cosf(2.0f * M_PI * current_freq_hz * (float)n / sampling_freq_hz + phase_Y_rad[h]);
        }
        
        // 限幅并存入DAC缓冲区
        if (sample_value_dac < 0.0f) {
            sample_value_dac = 0.0f;
        } else if (sample_value_dac > (ADC_RESOLUTION - 1.0f)) {
            sample_value_dac = (ADC_RESOLUTION - 1.0f);
        }
        dac_dest[n] = (uint16_t)sample_value_dac;
    }
}
