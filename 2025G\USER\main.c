#include "stm32f4xx.h"
#include "usart.h"
#include "delay.h"
#include "AD9959.h"
#include "lcd.h"
#include "arm_math.h"
#include "math.h"
#include "stdio.h"
#include "matrix_kbd.h"
#include "key.h"
#include "mode3.h"

// 全局变量声明

// 函数声明
static void SystemInit_Custom(void);
static void SetFrequencyOutput(uint32_t freq);
static void SetFrequencyAmplitudeOutput(uint32_t freq, uint16_t amplitude);
static uint16_t CalculateAD9959Code(uint32_t freq, uint16_t target_amplitude);
static double CalculateFilterResponse(uint32_t freq);
static void Mode3_Learn_Callback(void);
static void Mode3_Simulate_Callback(void);
static void Mode3_Stop_Callback(void);



int main()
{
    SystemInit_Custom();

    // 初始化按键模块
    Key_Init();
    
    // 设置回调函数
    Key_SetFrequencyOutputCallback(SetFrequencyOutput);
    Key_SetFrequencyAmplitudeOutputCallback(SetFrequencyAmplitudeOutput);
    Key_SetMode3Callbacks(Mode3_Learn_Callback, Mode3_Simulate_Callback, Mode3_Stop_Callback);

    // 上电默认AD9959无输出

    while(1)
    {
        /* 处理按键输入 */
        Key_ProcessInput();

        /* 处理模式3状态机 */
        if (Key_GetCurrentMode() == MODE_FILTER_LEARN) {
            Mode3_Process_State_Machine();
        }

        delay_ms(10);
    }
}

// 系统初始化
static void SystemInit_Custom(void)
{
    // 配置NVIC中断优先级分组
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  // 2位抢占优先级，2位子优先级

    delay_init(168);
    uart_init(115200);     // 初始化USART1
    MatrixKBD_Init();      // 初始化串口矩阵键盘（最高优先级中断）
    Key_Init();            // 初始化按键处理模块

    printf("Initializing hardware modules...\r\n");

    // 初始化AD9959
    AD9959GPIO_Init();
    Init_AD9959();

    // 初始化模式3
    Mode3_Init();



    // 初始化后设置一个测试频率确保AD9959工作正常
    Write_frequence(0, 1000);      // 设置通道0为1000Hz
    Write_Phase(0, 0);             // 相位0
    Write_Amplitude(0, 1023);      // 最大幅度

    // 设置CH3默认输出1000Hz，编码635（用于模式1和2）
    Write_frequence(3, 1000);      // 设置通道3为1000Hz
    Write_Phase(3, 0);             // 相位0
    Write_Amplitude(3, 635);       // 编码635

    IO_Update();                   // 更新输出
    
    printf("Mode 1: Frequency Setting (100Hz - 1.6MHz)\r\n");
    printf("S1-S10: Digits 0-9, S12: Confirm, S13: Mode Switch\r\n");
}

// 设置频率输出（模式1）
static void SetFrequencyOutput(uint32_t freq)
{
    printf("Setting AD9959 CH0 frequency to %lu Hz...\r\n", freq);

    // 设置AD9959通道0输出
    Write_frequence(0, freq);      // 设置频率
    Write_Phase(0, 0);             // 相位设为0
    Write_Amplitude(0, 1023);      // 幅度设为最大值1023
    IO_Update();                   // 更新输出

    printf("AD9959 CH0 configured: Freq=%lu Hz, Phase=0, Amplitude=1023\r\n", freq);
}

// 设置频率和幅度输出（模式2）
static void SetFrequencyAmplitudeOutput(uint32_t freq, uint16_t amplitude)
{
    uint16_t ad9959_code;
    
    printf("Calculating AD9959 code for %lu Hz, %.1fV...\r\n", freq, amplitude / 10.0);
    
    // 计算AD9959编码值
    ad9959_code = CalculateAD9959Code(freq, amplitude);
    
    printf("AD9959 code calculated: %d\r\n", ad9959_code);
    
    // 设置AD9959输出
    Write_frequence(0, freq);
    Write_Phase(0, 0);
    Write_Amplitude(0, ad9959_code);
    IO_Update();
    
    printf("AD9959 CH0 output: %lu Hz, Code=%d (Target: %.1fV)\r\n",
           freq, ad9959_code, amplitude / 10.0);
}

// 计算二阶低通滤波器的频率响应幅度
static double CalculateFilterResponse(uint32_t freq)
{
    double w, w2;
    double num, den;
    double magnitude;
    
    // 角频率 w = 2*pi*f
    w = 2.0 * 3.14159265359 * freq;
    w2 = w * w;
    
    // 传递函数 H(s) = 4.08 / (8.33e-09 s^2 + 2.42e-04 s + 0.83)
    // 频率响应 H(jw) = 4.08 / (0.83 - 8.33e-09*w^2 + j*2.42e-04*w)
    
    num = 4.08;
    den = (0.83 - 8.33e-09 * w2) * (0.83 - 8.33e-09 * w2) + 
          (2.42e-04 * w) * (2.42e-04 * w);
    
    magnitude = num / sqrt(den);
    
    return magnitude;
}

// 计算AD9959编码值
static uint16_t CalculateAD9959Code(uint32_t freq, uint16_t target_amplitude)
{
    double filter_gain;
    double target_voltage_mv;
    double required_filter_input_mv;
    double required_ad9959_output_mv;
    uint16_t ad9959_code;
    
    // 目标电压（mV）
    target_voltage_mv = (target_amplitude / 10.0) * 1000.0;  // 转换为mV
    
    // 计算滤波器在该频率下的增益
    filter_gain = CalculateFilterResponse(freq);
    
    printf("Filter gain at %lu Hz: %.3f\r\n", freq, filter_gain);
    
    // 计算滤波器输入需要的电压（mV）
    required_filter_input_mv = target_voltage_mv / filter_gain;
    
    // 考虑6倍放大器，计算AD9959需要输出的电压（mV）
    required_ad9959_output_mv = required_filter_input_mv / 6.0;
    
    // 根据关系 y = 0.525 * n 计算编码值
    ad9959_code = (uint16_t)(required_ad9959_output_mv / 0.525);
    
    // 限制编码值范围
    if (ad9959_code > 1023) {
        ad9959_code = 1023;
        printf("Warning: AD9959 code limited to 1023\r\n");
    }
    if (ad9959_code < 1) {
        ad9959_code = 1;
        printf("Warning: AD9959 code limited to 1\r\n");
    }
    
    printf("Calculation: Target=%.1fV, Filter_in=%.1fmV, AD9959_out=%.1fmV, Code=%d\r\n",
           target_voltage_mv/1000.0, required_filter_input_mv, required_ad9959_output_mv, ad9959_code);
    
    return ad9959_code;
}

// 模式3学习回调函数
static void Mode3_Learn_Callback(void)
{
    printf("Mode 3: Starting filter learning...\r\n");
    Mode3_Start_Learn();
}

// 模式3仿真回调函数
static void Mode3_Simulate_Callback(void)
{
    printf("Mode 3: Starting filter simulation...\r\n");
    Mode3_Start_Simulation();
}

// 模式3停止回调函数
static void Mode3_Stop_Callback(void)
{
    printf("Mode 3: Stopping current operation...\r\n");
    Mode3_Stop_Simulation();
}




