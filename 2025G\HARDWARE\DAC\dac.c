#include "dac.h"
#include "delay.h"

/* ======= 全局变量定义 ======= */
volatile uint16_t g_dac_output_buffer[ADC_TOTAL_BUFFER_SIZE];

/* ======= 静态变量 ======= */
static DAC_HandleTypeDef hdac_mode3;
static DMA_HandleTypeDef hdma_dac1_mode3;

/* ======= 模式3 DAC初始化 ======= */
void DAC_Mode3_Init(void)
{
    DAC_ChannelConfTypeDef sConfig = {0};
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    printf("Initializing DAC for Mode 3...\r\n");
    
    // 使能时钟
    __HAL_RCC_DAC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();
    
    // 配置GPIO (PA4 -> DAC_OUT1)
    GPIO_InitStruct.Pin = GPIO_PIN_4;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 配置DAC
    hdac_mode3.Instance = DAC;
    if (HAL_DAC_Init(&hdac_mode3) != HAL_OK) {
        printf("DAC initialization failed!\r\n");
        return;
    }
    
    // 配置DAC通道1
    sConfig.DAC_Trigger = DAC_TRIGGER_T3_TRGO;  // 使用TIM3触发
    sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;
    if (HAL_DAC_ConfigChannel(&hdac_mode3, &sConfig, DAC_CHANNEL_1) != HAL_OK) {
        printf("DAC channel configuration failed!\r\n");
        return;
    }
    
    // 配置DMA
    hdma_dac1_mode3.Instance = DMA1_Stream5;
    hdma_dac1_mode3.Init.Channel = DMA_CHANNEL_7;
    hdma_dac1_mode3.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_dac1_mode3.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_dac1_mode3.Init.MemInc = DMA_MINC_ENABLE;
    hdma_dac1_mode3.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
    hdma_dac1_mode3.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
    hdma_dac1_mode3.Init.Mode = DMA_CIRCULAR;
    hdma_dac1_mode3.Init.Priority = DMA_PRIORITY_LOW;
    hdma_dac1_mode3.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    
    if (HAL_DMA_Init(&hdma_dac1_mode3) != HAL_OK) {
        printf("DAC DMA initialization failed!\r\n");
        return;
    }
    
    __HAL_LINKDMA(&hdac_mode3, DMA_Handle1, hdma_dac1_mode3);
    
    // 配置NVIC中断优先级
    HAL_NVIC_SetPriority(DMA1_Stream5_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream5_IRQn);
    
    printf("DAC Mode 3 initialization complete.\r\n");
}

/* ======= 开始DAC输出 ======= */
void DAC_Start_Output(void)
{
    if (HAL_DAC_Start_DMA(&hdac_mode3, DAC_CHANNEL_1, (uint32_t*)g_dac_output_buffer, 
                         ADC_TOTAL_BUFFER_SIZE, DAC_ALIGN_12B_R) != HAL_OK) {
        printf("DAC DMA start failed!\r\n");
        return;
    }
    
    printf("DAC output started.\r\n");
}

/* ======= 停止DAC输出 ======= */
void DAC_Stop_Output(void)
{
    HAL_DAC_Stop_DMA(&hdac_mode3, DAC_CHANNEL_1);
    printf("DAC output stopped.\r\n");
}

/* ======= DMA中断处理函数 ======= */
void DMA1_Stream5_IRQHandler(void)
{
    HAL_DMA_IRQHandler(&hdma_dac1_mode3);
}
