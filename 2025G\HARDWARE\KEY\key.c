#include "key.h"

/* ======= 内部变量 ======= */
static KeyState_t keyState;
static uint8_t isOutputEnabled = 0;

/* ======= 回调函数指针 ======= */
static FrequencyOutputCallback_t frequencyOutputCallback = 0;
static FrequencyAmplitudeOutputCallback_t frequencyAmplitudeOutputCallback = 0;
static Mode3LearnCallback_t mode3LearnCallback = 0;
static Mode3SimulateCallback_t mode3SimulateCallback = 0;
static Mode3StopCallback_t mode3StopCallback = 0;

/* ======= 初始化函数 ======= */
void Key_Init(void)
{
    // 初始化按键状态
    keyState.currentMode = MODE_FREQUENCY;
    keyState.inputIndex = 0;
    keyState.mode2_step = MODE2_STEP_FREQUENCY;
    keyState.mode2_freq = 0;
    keyState.mode2_amplitude = 10;  // 默认1.0V
    keyState.mode3_step = MODE3_STEP_IDLE;
    
    // 清空输入缓冲区
    Key_ClearInputBuffer();
    
    // 初始化输出状态
    isOutputEnabled = 0;
    
    printf("Key module initialized - Mode 1: Frequency Setting\r\n");
}

/* ======= 设置回调函数 ======= */
void Key_SetFrequencyOutputCallback(FrequencyOutputCallback_t callback)
{
    frequencyOutputCallback = callback;
}

void Key_SetFrequencyAmplitudeOutputCallback(FrequencyAmplitudeOutputCallback_t callback)
{
    frequencyAmplitudeOutputCallback = callback;
}

void Key_SetMode3Callbacks(Mode3LearnCallback_t learn_cb, Mode3SimulateCallback_t sim_cb, Mode3StopCallback_t stop_cb)
{
    mode3LearnCallback = learn_cb;
    mode3SimulateCallback = sim_cb;
    mode3StopCallback = stop_cb;
}

/* ======= 主处理函数 ======= */
void Key_ProcessInput(void)
{
    if (MatrixKBD_Available() > 0) {
        uint8_t key = MatrixKBD_GetKey();
        if (key != 0) {
            Key_ProcessKeyInput(key);
        }
    }
}

/* ======= 获取当前状态 ======= */
KeyMode_t Key_GetCurrentMode(void)
{
    return keyState.currentMode;
}

uint8_t Key_IsOutputEnabled(void)
{
    return isOutputEnabled;
}

/* ======= 按键输入处理 ======= */
void Key_ProcessKeyInput(uint8_t key)
{
    // S13模式切换键
    if (key == KBD_MODE) {
        Key_SwitchMode();
        return;
    }

    // 根据当前模式处理按键
    switch (keyState.currentMode) {
        case MODE_FREQUENCY:
            Key_HandleFrequencyMode(key);
            break;
        case MODE_FREQ_AMPLITUDE:
            Key_HandleFrequencyAmplitudeMode(key);
            break;
        case MODE_FILTER_LEARN:
            Key_HandleFilterLearnMode(key);
            break;
        case MODE_RESERVED_4:
            // 预留模式，暂不实现
            printf("Mode %d: Reserved\r\n", keyState.currentMode);
            break;
        default:
            break;
    }
}

/* ======= 频率设置模式处理 ======= */
void Key_HandleFrequencyMode(uint8_t key)
{
    uint8_t digit;

    // S12确认键
    if (key == KBD_CONFIRM) {
        uint32_t inputFreq = Key_BufferToFrequency();
        if (Key_ValidateFrequency(inputFreq)) {
            if (frequencyOutputCallback != 0) {
                frequencyOutputCallback(inputFreq);
                isOutputEnabled = 1;
            }
            printf("Frequency set to: %lu Hz\r\n", inputFreq);
        } else {
            printf("Invalid frequency range (100Hz - 1600000Hz)\r\n");
        }
        Key_ClearInputBuffer();
        return;
    }

    // 数字键S1-S10
    digit = MatrixKBD_KeyToDigit(key);
    if (digit != 0xFF) {
        if (keyState.inputIndex < sizeof(keyState.inputBuffer) - 1) {
            keyState.inputBuffer[keyState.inputIndex++] = digit;
            printf("Input: ");
            {
                uint8_t i;
                for (i = 0; i < keyState.inputIndex; i++) {
                    printf("%d", keyState.inputBuffer[i]);
                }
            }
            printf("\r\n");
        } else {
            printf("Input buffer full\r\n");
        }
    }
}

/* ======= 频率幅度设置模式处理（模式2） ======= */
void Key_HandleFrequencyAmplitudeMode(uint8_t key)
{
    uint8_t digit;

    if (keyState.mode2_step == MODE2_STEP_FREQUENCY) {
        // 第一步：设置频率
        if (key == KBD_CONFIRM) {
            keyState.mode2_freq = Key_BufferToFrequency();
            if (Key_ValidateFrequency(keyState.mode2_freq)) {
                keyState.mode2_step = MODE2_STEP_AMPLITUDE;
                Key_ClearInputBuffer();
                printf("Frequency set to: %lu Hz\r\n", keyState.mode2_freq);
                printf("Step 2: Set amplitude (1.0-2.0V, S11=decimal point)\r\n");
                printf("Current amplitude: %.1fV\r\n", keyState.mode2_amplitude / 10.0);
            } else {
                printf("Invalid frequency range (100Hz - 1600000Hz)\r\n");
                Key_ClearInputBuffer();
            }
            return;
        }

        // 数字键输入频率
        digit = MatrixKBD_KeyToDigit(key);
        if (digit != 0xFF) {
            if (keyState.inputIndex < sizeof(keyState.inputBuffer) - 1) {
                keyState.inputBuffer[keyState.inputIndex++] = digit;
                printf("Frequency input: ");
                {
                    uint8_t i;
                    for (i = 0; i < keyState.inputIndex; i++) {
                        printf("%d", keyState.inputBuffer[i]);
                    }
                }
                printf("\r\n");
            } else {
                printf("Input buffer full\r\n");
            }
        }
    } else if (keyState.mode2_step == MODE2_STEP_AMPLITUDE) {
        // 第二步：设置幅度
        if (key == KBD_CONFIRM) {
            keyState.mode2_amplitude = Key_BufferToAmplitude();
            if (keyState.mode2_amplitude >= 10 && keyState.mode2_amplitude <= 20) {
                if (frequencyAmplitudeOutputCallback != 0) {
                    frequencyAmplitudeOutputCallback(keyState.mode2_freq, keyState.mode2_amplitude);
                    isOutputEnabled = 1;
                }
                printf("Output enabled: %lu Hz, %.1fV\r\n", keyState.mode2_freq, keyState.mode2_amplitude / 10.0);
                // 重置到第一步，准备下次设置
                keyState.mode2_step = MODE2_STEP_FREQUENCY;
                Key_ClearInputBuffer();
            } else {
                printf("Invalid amplitude range (1.0-2.0V)\r\n");
                Key_ClearInputBuffer();
            }
            return;
        }

        // 处理幅度输入（支持小数点）
        if (key == KBD_S11) {
            // S11作为小数点
            if (keyState.inputIndex < sizeof(keyState.inputBuffer) - 1) {
                keyState.inputBuffer[keyState.inputIndex++] = 0xFF;  // 用0xFF表示小数点
                printf("Amplitude input: ");
                {
                    uint8_t i;
                    for (i = 0; i < keyState.inputIndex; i++) {
                        if (keyState.inputBuffer[i] == 0xFF) {
                            printf(".");
                        } else {
                            printf("%d", keyState.inputBuffer[i]);
                        }
                    }
                }
                printf("\r\n");
            }
        } else {
            digit = MatrixKBD_KeyToDigit(key);
            if (digit != 0xFF) {
                if (keyState.inputIndex < sizeof(keyState.inputBuffer) - 1) {
                    keyState.inputBuffer[keyState.inputIndex++] = digit;
                    printf("Amplitude input: ");
                    {
                        uint8_t i;
                        for (i = 0; i < keyState.inputIndex; i++) {
                            if (keyState.inputBuffer[i] == 0xFF) {
                                printf(".");
                            } else {
                                printf("%d", keyState.inputBuffer[i]);
                            }
                        }
                    }
                    printf("\r\n");
                } else {
                    printf("Input buffer full\r\n");
                }
            }
        }
    }
}

/* ======= 滤波器学习模式处理（模式3） ======= */
void Key_HandleFilterLearnMode(uint8_t key)
{
    switch (keyState.mode3_step) {
        case MODE3_STEP_IDLE:
            // 空闲状态，等待按键
            if (key == KBD_S1) {
                // S1键开始学习
                if (mode3LearnCallback != 0) {
                    mode3LearnCallback();
                    keyState.mode3_step = MODE3_STEP_LEARNING;
                    printf("Starting filter learning...\r\n");
                }
            } else if (key == KBD_S2) {
                // S2键开始仿真
                if (mode3SimulateCallback != 0) {
                    mode3SimulateCallback();
                    keyState.mode3_step = MODE3_STEP_SIMULATING;
                    printf("Starting filter simulation...\r\n");
                }
            }
            break;

        case MODE3_STEP_LEARNING:
            // 学习状态，等待学习完成或停止
            if (key == KBD_S3) {
                // S3键停止学习
                if (mode3StopCallback != 0) {
                    mode3StopCallback();
                    keyState.mode3_step = MODE3_STEP_RESULTS;
                    printf("Learning stopped.\r\n");
                }
            }
            break;

        case MODE3_STEP_SIMULATING:
            // 仿真状态，等待停止
            if (key == KBD_S3) {
                // S3键停止仿真
                if (mode3StopCallback != 0) {
                    mode3StopCallback();
                    keyState.mode3_step = MODE3_STEP_IDLE;
                    printf("Simulation stopped.\r\n");
                }
            }
            break;

        case MODE3_STEP_RESULTS:
            // 结果显示状态，任意键返回空闲
            keyState.mode3_step = MODE3_STEP_IDLE;
            printf("Returning to idle state.\r\n");
            break;

        default:
            keyState.mode3_step = MODE3_STEP_IDLE;
            break;
    }
}

/* ======= 模式切换 ======= */
void Key_SwitchMode(void)
{
    keyState.currentMode++;
    if (keyState.currentMode > MODE_RESERVED_4) {
        keyState.currentMode = MODE_FREQUENCY;
    }

    Key_ClearInputBuffer();

    // 重置模式2的状态
    if (keyState.currentMode == MODE_FREQ_AMPLITUDE) {
        keyState.mode2_step = MODE2_STEP_FREQUENCY;
        keyState.mode2_freq = 0;
        keyState.mode2_amplitude = 10;  // 默认1.0V
    }

    // 重置模式3的状态
    if (keyState.currentMode == MODE_FILTER_LEARN) {
        keyState.mode3_step = MODE3_STEP_IDLE;
    }



    printf("Switched to Mode %d\r\n", keyState.currentMode);

    if (keyState.currentMode == MODE_FREQUENCY) {
        printf("Mode 1: Frequency Setting\r\n");
    } else if (keyState.currentMode == MODE_FREQ_AMPLITUDE) {
        printf("Mode 2: Frequency & Amplitude Setting\r\n");
        printf("Step 1: Set frequency (S1-S10: 0-9, S12: Confirm)\r\n");
    } else if (keyState.currentMode == MODE_FILTER_LEARN) {
        printf("Mode 3: Filter Learning & Simulation\r\n");
        printf("S1: Learn Filter, S2: Simulate, S3: Stop\r\n");
    } else {
        printf("Mode %d: Reserved (To be implemented)\r\n", keyState.currentMode);
    }
}

/* ======= 清空输入缓冲区 ======= */
void Key_ClearInputBuffer(void)
{
    uint8_t i;
    keyState.inputIndex = 0;
    for (i = 0; i < sizeof(keyState.inputBuffer); i++) {
        keyState.inputBuffer[i] = 0;
    }
}

/* ======= 将缓冲区数字转换为频率值 ======= */
uint32_t Key_BufferToFrequency(void)
{
    uint32_t freq = 0;
    uint8_t i;

    for (i = 0; i < keyState.inputIndex; i++) {
        freq = freq * 10 + keyState.inputBuffer[i];
    }

    return freq;
}

/* ======= 验证频率范围 ======= */
uint8_t Key_ValidateFrequency(uint32_t freq)
{
    return (freq >= 100 && freq <= 1600000) ? 1 : 0;
}

/* ======= 将缓冲区转换为幅度值（单位：0.1V） ======= */
uint16_t Key_BufferToAmplitude(void)
{
    uint16_t amplitude = 0;
    uint8_t i;
    uint8_t decimal_found = 0;
    uint8_t decimal_digits = 0;

    for (i = 0; i < keyState.inputIndex; i++) {
        if (keyState.inputBuffer[i] == 0xFF) {
            // 遇到小数点
            decimal_found = 1;
        } else if (!decimal_found) {
            // 小数点前的数字
            amplitude = amplitude * 10 + keyState.inputBuffer[i];
        } else {
            // 小数点后的数字
            if (decimal_digits == 0) {
                amplitude = amplitude * 10 + keyState.inputBuffer[i];
                decimal_digits++;
            }
            // 只处理小数点后第一位
        }
    }

    // 如果没有小数点，默认为整数（如输入2表示2.0V）
    if (!decimal_found) {
        amplitude = amplitude * 10;
    }

    return amplitude;
}


