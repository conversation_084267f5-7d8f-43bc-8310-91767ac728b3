/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
typedef uint32_t  u32;
 
typedef uint16_t u16;
 
typedef uint8_t  u8;
 
typedef __IO uint32_t  vu32;
 
typedef __IO uint16_t vu16;
 
typedef __IO uint8_t  vu8;
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LEARN_KEY_Pin GPIO_PIN_4
#define LEARN_KEY_GPIO_Port GPIOE
#define SIM_KEY_Pin GPIO_PIN_0
#define SIM_KEY_GPIO_Port GPIOA
#define RELAY_Pin GPIO_PIN_7
#define RELAY_GPIO_Port GPIOA
#define IDLE_KEY_Pin GPIO_PIN_4
#define IDLE_KEY_GPIO_Port GPIOC
#define LCD_BL_Pin GPIO_PIN_15
#define LCD_BL_GPIO_Port GPIOB
#define SDIO3_Pin GPIO_PIN_12
#define SDIO3_GPIO_Port GPIOD
#define PS1_Pin GPIO_PIN_8
#define PS1_GPIO_Port GPIOC
#define PS2_Pin GPIO_PIN_9
#define PS2_GPIO_Port GPIOC
#define PWR1_Pin GPIO_PIN_10
#define PWR1_GPIO_Port GPIOG
#define Reset_Pin GPIO_PIN_11
#define Reset_GPIO_Port GPIOG
#define SDIO2_Pin GPIO_PIN_13
#define SDIO2_GPIO_Port GPIOG
#define SDIO1_Pin GPIO_PIN_14
#define SDIO1_GPIO_Port GPIOG
#define PS3_Pin GPIO_PIN_15
#define PS3_GPIO_Port GPIOG
#define CS_Pin GPIO_PIN_3
#define CS_GPIO_Port GPIOB
#define SCLK_Pin GPIO_PIN_4
#define SCLK_GPIO_Port GPIOB
#define SDIO0_Pin GPIO_PIN_5
#define SDIO0_GPIO_Port GPIOB
#define UPDATE_Pin GPIO_PIN_6
#define UPDATE_GPIO_Port GPIOB
#define PS0_Pin GPIO_PIN_7
#define PS0_GPIO_Port GPIOB

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
