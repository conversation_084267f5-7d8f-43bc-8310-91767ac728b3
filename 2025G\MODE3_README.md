# 模式3 - 滤波器学习和仿真系统（简化版）

## 功能概述

模式3实现了一个基础的滤波器学习和仿真演示系统，能够：

1. **频率扫描演示**：通过AD9959输出不同频率信号
2. **滤波器类型模拟**：演示滤波器类型识别过程
3. **仿真模式演示**：展示仿真功能界面

## 硬件配置

### AD9959配置
- **通道0**: 学习模式激励信号输出（1kHz-100kHz扫描）
- **通道3**: 仿真模式测试信号输出（50kHz）

## 操作说明

### 进入模式3
1. 按S13键切换到模式3
2. LCD显示："Mode 3: Filter Learning & Simulation"
3. 提示："S1: Learn Filter, S2: Simulate, S3: Stop"

### 学习演示
1. 按S1键开始学习演示
2. 系统自动扫描1kHz-100kHz频率范围
3. AD9959通道0输出扫描频率
4. LCD显示学习进度
5. 学习完成后显示模拟的滤波器类型（低通滤波器）

### 仿真演示
1. 确保已完成学习演示
2. 按S2键开始仿真演示
3. AD9959通道3输出50kHz测试信号
4. LCD显示仿真状态
5. 按S3键停止仿真

## 技术特性

### 频率扫描演示
- **起始频率**: 1kHz
- **结束频率**: 100kHz
- **步进**: 1kHz
- **总测试点**: 100个频率点

### 演示功能
- **频率输出**: AD9959多频率输出演示
- **状态显示**: LCD实时状态显示
- **按键控制**: 简单的三键操作

### 滤波器类型演示
- **低通滤波器**: 演示识别结果
- **学习过程**: 模拟学习过程显示
- **仿真状态**: 模拟仿真工作状态

## 文件结构

```
HARDWARE/
├── MODE3/
│   ├── mode3.h        # 模式3主控制接口
│   └── mode3.c        # 模式3演示逻辑
└── KEY/
    ├── key.h          # 按键处理（已更新支持模式3）
    └── key.c          # 按键处理（已更新支持模式3）
```

## 编译和测试

### 编译状态
- ✅ 所有语法错误已修复
- ✅ 使用STM32F4标准库
- ✅ 兼容现有项目架构
- ✅ 可以正常编译

### 测试步骤
1. 编译项目确保无错误
2. 烧录到开发板
3. 按S13键切换到模式3
4. 按S1键测试学习功能
5. 按S2键测试仿真功能
6. 按S3键停止操作

## 调试信息

系统通过串口输出调试信息：
- 模式3初始化状态
- 学习演示进度
- 频率扫描过程
- 仿真状态切换

## 注意事项

1. **演示功能**: 这是一个简化的演示版本，主要展示界面和流程
2. **频率范围**: 演示频率范围为1kHz-100kHz
3. **学习时间**: 演示学习过程约需要10秒
4. **扩展性**: 可以在此基础上添加真实的ADC/DAC功能

## 扩展功能

可以在此基础上扩展：
- 添加真实的ADC采样功能
- 实现DAC输出功能
- 增加复杂的信号处理算法
- 添加更多滤波器类型识别
