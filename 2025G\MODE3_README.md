# 模式3 - 滤波器学习和仿真系统

## 功能概述

模式3实现了一个完整的滤波器学习和仿真系统，能够：

1. **学习滤波器特性**：通过频率扫描测量滤波器的频率响应
2. **分析滤波器类型**：自动识别低通、高通、带通、带阻滤波器
3. **实时仿真**：基于学习到的特性实时仿真滤波器效果

## 硬件配置

### ADC配置
- **ADC1**: PA1 - 滤波器输入信号采样
- **ADC2**: PA2 - 滤波器输出信号采样
- **采样率**: 动态调整（信号频率的2-20倍）
- **分辨率**: 12位
- **DMA**: 循环模式，双缓冲

### DAC配置
- **DAC1**: PA4 - 仿真模式输出
- **分辨率**: 12位
- **DMA**: 循环模式

### 定时器配置
- **TIM3**: ADC/DAC采样触发源
- **TIM5**: 频率测量（PA3输入捕获）

### AD9959配置
- **通道0**: 学习模式激励信号输出
- **通道3**: 采样时钟输出

## 操作说明

### 进入模式3
1. 按S13键切换到模式3
2. LCD显示："Mode 3: Filter Learning & Simulation"
3. 提示："S1: Learn Filter, S2: Simulate, S3: Stop"

### 学习滤波器
1. 连接待测滤波器：
   - 输入端连接AD9959通道0输出
   - 输出端连接ADC2输入（PA2）
   - ADC1输入（PA1）连接滤波器输入端作为参考
2. 按S1键开始学习
3. 系统自动扫描200Hz-500kHz频率范围
4. LCD显示学习进度
5. 学习完成后显示滤波器类型

### 仿真模式
1. 确保已完成滤波器学习
2. 按S2键开始仿真
3. 连接信号源到ADC2输入（PA2）
4. DAC输出（PA4）提供仿真的滤波器输出
5. 按S3键停止仿真

## 技术特性

### 频率扫描
- **起始频率**: 200Hz
- **结束频率**: 500kHz
- **步进**: 200Hz
- **总测试点**: 2500个频率点

### 信号处理
- **算法**: 单频DFT（离散傅里叶变换）
- **谐波处理**: 支持基波+10次谐波
- **相位补偿**: 自动相位对齐
- **幅度校准**: 基于ADC参考电压

### 滤波器识别
- **低通滤波器**: 高频衰减明显
- **高通滤波器**: 低频衰减明显
- **带通滤波器**: 两端衰减，中间通过
- **带阻滤波器**: 中间衰减，两端通过

## 文件结构

```
HARDWARE/
├── ADC/
│   ├── adc.h          # ADC配置和接口
│   └── adc.c          # ADC实现和DMA回调
├── DAC/
│   ├── dac.h          # DAC配置和接口
│   └── dac.c          # DAC实现
├── TIM/
│   ├── tim.h          # 定时器配置和接口
│   └── tim.c          # 定时器实现和频率测量
├── DFT/
│   ├── dft.h          # DFT算法接口
│   └── dft.c          # DFT算法实现
├── MODE3/
│   ├── mode3.h        # 模式3主控制接口
│   └── mode3.c        # 模式3状态机和控制逻辑
└── KEY/
    ├── key.h          # 按键处理（已更新）
    └── key.c          # 按键处理（已更新）
```

## 调试信息

系统通过串口输出详细的调试信息：
- 初始化状态
- 学习进度
- 频率响应数据
- 滤波器类型识别结果
- 仿真状态

## 注意事项

1. **信号幅度**: 输入信号幅度应在ADC量程内（0-3.3V）
2. **频率范围**: 测试频率应在200Hz-500kHz范围内
3. **连接稳定**: 确保信号连接稳定，避免干扰
4. **学习时间**: 完整学习过程约需要1-2分钟
5. **仿真精度**: 仿真精度取决于学习时的信号质量

## 扩展功能

可以通过修改配置参数实现：
- 调整频率扫描范围
- 修改采样率策略
- 增加滤波器类型识别算法
- 优化DFT算法性能
