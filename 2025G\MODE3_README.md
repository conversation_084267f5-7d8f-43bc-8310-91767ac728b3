# 模式3 - 滤波器学习和仿真系统（简化版）

## 功能概述

模式3实现了一个基础的滤波器学习和仿真演示系统，能够：

1. **频率扫描演示**：通过AD9959输出不同频率信号
2. **滤波器类型模拟**：演示滤波器类型识别过程
3. **仿真模式演示**：展示仿真功能界面

## 硬件配置

### AD9959配置
- **通道0**: 学习模式激励信号输出（1kHz-100kHz扫描）
- **通道3**: 仿真模式测试信号输出（50kHz）

## 操作说明

### 进入模式3
1. 按S13键切换到模式3
2. LCD显示："Mode 3: Filter Learning & Simulation"
3. 提示："S1: Learn Filter, S2: Simulate, S3: Stop"

### 学习演示
1. 按S1键开始学习演示
2. 系统自动扫描1kHz-100kHz频率范围
3. AD9959通道0输出扫描频率
4. LCD显示学习进度
5. 学习完成后显示模拟的滤波器类型（低通滤波器）

### 仿真演示
1. 确保已完成学习演示
2. 按S2键开始仿真演示
3. AD9959通道3输出50kHz测试信号
4. LCD显示仿真状态
5. 按S3键停止仿真

## 技术特性

### 频率扫描演示
- **起始频率**: 1kHz
- **结束频率**: 100kHz
- **步进**: 1kHz
- **总测试点**: 100个频率点

### 演示功能
- **频率输出**: AD9959多频率输出演示
- **状态显示**: LCD实时状态显示
- **按键控制**: 简单的三键操作

### 滤波器类型演示
- **低通滤波器**: 演示识别结果
- **学习过程**: 模拟学习过程显示
- **仿真状态**: 模拟仿真工作状态

## 文件结构

```
HARDWARE/
├── MODE3/
│   ├── mode3.h        # 模式3主控制接口
│   └── mode3.c        # 模式3演示逻辑
└── KEY/
    ├── key.h          # 按键处理（已更新支持模式3）
    └── key.c          # 按键处理（已更新支持模式3）
```

## 调试信息

系统通过串口输出详细的调试信息：
- 初始化状态
- 学习进度
- 频率响应数据
- 滤波器类型识别结果
- 仿真状态

## 注意事项

1. **信号幅度**: 输入信号幅度应在ADC量程内（0-3.3V）
2. **频率范围**: 测试频率应在200Hz-500kHz范围内
3. **连接稳定**: 确保信号连接稳定，避免干扰
4. **学习时间**: 完整学习过程约需要1-2分钟
5. **仿真精度**: 仿真精度取决于学习时的信号质量

## 扩展功能

可以通过修改配置参数实现：
- 调整频率扫描范围
- 修改采样率策略
- 增加滤波器类型识别算法
- 优化DFT算法性能
