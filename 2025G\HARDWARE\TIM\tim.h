#ifndef __TIM_H
#define __TIM_H

#include "stm32f4xx.h"
#include "stm32f4xx_hal.h"
#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= 定时器配置参数 ======= */
#define TIMER5_TICK_FREQ_HZ         84000000.0f
#define NUM_PERIODS_TO_AVERAGE      50

/* ======= 外部变量声明 ======= */
extern volatile uint32_t g_tim5_capture_val1;
extern volatile uint32_t g_tim5_capture_val2;
extern volatile uint8_t g_tim5_capture_state;
extern volatile uint32_t g_capture_count;
extern volatile float g_measured_freq;
extern volatile float g_current_signal_rate;
extern volatile float g_current_sampling_simulation;

/* ======= 函数声明 ======= */
void TIM_Mode3_Init(void);
void TIM3_Init(uint32_t sampling_freq);
void TIM5_Init(void);
void TIM_Start_Sampling_Timer(uint32_t sampling_freq);
void TIM_Stop_Sampling_Timer(void);
void TIM_Start_Frequency_Measurement(void);
void TIM_Stop_Frequency_Measurement(void);

#ifdef __cplusplus
}
#endif

#endif /* __TIM_H */
