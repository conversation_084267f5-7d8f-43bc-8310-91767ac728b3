#include "sys.h"
#include "ad9959.h"

/* AD9959寄存器地址定义 */
#define CSR_ADD     0x00    // CSR 通道选择寄存器，包括通道选择，串行3线通信模式，数据传输首先高低位设置
                            // default Value = 0xF0 详细请参见AD9958 datasheet Table 27

/* 通道选择数据 */
uchar CSR_DATA0[1] = {0x10};    // 开启 CH0
uchar CSR_DATA1[1] = {0x20};    // 开启 CH1
uchar CSR_DATA2[1] = {0x40};    // 开启 CH2
uchar CSR_DATA3[1] = {0x80};    // 开启 CH3

#define FR1_ADD     0x01    // FR1 功能寄存器1，详细请参见AD9958 datasheet Table 27
uchar FR1_DATA[3] = {0xD0,0x00,0x00};   // default Value = 0x000000; 20倍频; Charge pump control = 75uA
                                        // FR1<23> -- VCO gain control =0时 system clock below 160 MHz
                                        //             =1时, the high range (system clock above 255 MHz)

#define FR2_ADD     0x02    // FR2 功能寄存器2 详细请参见AD9958 datasheet Table 27
uchar FR2_DATA[2] = {0x00,0x00};       // default Value = 0x0000

#define CFR_ADD     0x03    // CFR 通道功能寄存器，详细请参见AD9958 datasheet Table 28
uchar CFR_DATA[3] = {0x00,0x03,0x02};  // default Value = 0x000302

#define CFTW0_ADD   0x04    // CTW0 通道频率转换字寄存器，详细请参见AD9958 datasheet Table 28
uchar CFTW0_DATA0[4] = {0x33,0x33,0x33,0x33};  // OUT0 100MHZ 主频500M
uchar CFTW0_DATA1[4] = {0x28,0xF5,0xC2,0x8F};  // OUT1 80MHZ
uchar CFTW0_DATA2[4] = {0x05,0x1E,0xB8,0x52};  // OUT2 10MHZ
uchar CFTW0_DATA3[4] = {0x00,0x83,0x12,0x6F};  // OUT3 1MHZ

#define CPOW0_ADD   0x05    // CPW0 通道相位转换字寄存器，详细请参见AD9958 datasheet Table 28
uchar CPOW0_DATA[2] = {0x00,0x00};             // default Value = 0x0000 @ = POW/2^14*360

#define ACR_ADD     0x06    // ACR 幅度控制寄存器，详细请参见AD9958 datasheet Table 28
uchar ACR_DATA[3] = {0x00,0x00,0x00};          // default Value = 0x--0000 Rest = 18.91/Iout

#define LSRR_ADD    0x07    // LSR 通道线性扫描寄存器，详细请参见AD9958 datasheet Table 28
uchar LSRR_DATA[2] = {0x00,0x00};              // default Value = 0x----

#define RDW_ADD     0x08    // RDW 通道线性向上扫描寄存器，详细请参见AD9958 datasheet Table 28
uchar RDW_DATA[4] = {0x00,0x00,0x00,0x00};     // default Value = 0x--------

#define FDW_ADD     0x09    // FDW 通道线性向下扫描寄存器，详细请参见AD9958 datasheet Table 28
uchar FDW_DATA[4] = {0x00,0x00,0x00,0x00};     // default Value = 0x--------

/* GPIO引脚定义 */
#define CS      PBout(0)    // 片选信号
#define SCLK    PBout(1)    // 时钟信号
#define SDIO0   PBout(6)    // 数据输入输出0
#define UPDATE  PBout(7)    // 更新信号
#define PS0     PGout(6)    // P0
#define PS1     PBout(13)   // P1
#define PS2     PBout(12)   // P2

#define PS3     PGout(15)   // P3
#define SDIO1   PGout(14)   // 数据输入输出1
#define SDIO2   PGout(13)   // 数据输入输出2
#define SDIO3   PGout(12)   // 数据输入输出3
#define Reset   PGout(11)   // 复位信号
#define PWR1    PGout(8)    // PDC


/**
 * @brief  AD9959 GPIO初始化
 * @param  None
 * @retval None
 */
void AD9959GPIO_Init(void)
{
    GPIO_InitTypeDef  GPIO_InitStructure;

    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA|RCC_AHB1Periph_GPIOB|RCC_AHB1Periph_GPIOG|RCC_AHB1Periph_GPIOE, ENABLE);

    // GPIOB引脚初始化设置
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_6 | GPIO_Pin_7 | GPIO_Pin_13 | GPIO_Pin_12;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;       // 普通输出模式
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;      // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;  // 100MHz
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;        // 上拉
    GPIO_Init(GPIOB, &GPIO_InitStructure);              // 初始化GPIOB

    // GPIOG引脚初始化设置
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_6 | GPIO_Pin_11 | GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15;
    GPIO_Init(GPIOG, &GPIO_InitStructure);              // 初始化GPIOG
	
		//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;       //WK_UP对应引脚PA0
		//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    //普通输入模式
		//  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN ; //下拉
		//  GPIO_Init(GPIOA, &GPIO_InitStructure);
		//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;       //WK_UP对应引脚PA0
		//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    //普通输入模式
		//  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN ; //下拉
		//  GPIO_Init(GPIOE, &GPIO_InitStructure);
		//	
		//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4|GPIO_Pin_3;//WK_UP对应引脚PA0
		//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;    //普通输入模式
		//  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP ;   //上拉
		//  GPIO_Init(GPIOE, &GPIO_InitStructure);
}

/**
 * @brief  延时函数
 * @param  length: 延时长度
 * @retval None
 */
void delay1(unsigned long length)
{
    length = length * 12;
    while(length--);
}

/**
 * @brief  AD9959复位
 * @param  None
 * @retval None
 */
void IntReset(void)
{
    Reset = 0;
    delay1(1);
    Reset = 1;
    delay1(30);
    Reset = 0;
}

/**
 * @brief  AD9959更新数据
 * @param  None
 * @retval None
 */
void IO_Update(void)
{
    UPDATE = 0;
    delay1(2);
    UPDATE = 1;
    delay1(4);
    UPDATE = 0;
}

/**
 * @brief  IO口初始化
 * @param  None
 * @retval None
 */
void Intserve(void)
{
    PWR1 = 0;
    CS = 1;
    SCLK = 0;
    UPDATE = 0;
    PS0 = 0;
    PS1 = 0;
    PS2 = 0;
    PS3 = 0;

    SDIO0 = 0;
    SDIO1 = 0;
    SDIO2 = 0;
    SDIO3 = 0;
}

/**
 * @brief  控制器通过SPI向AD9959写数据
 * @param  RegisterAddress: 寄存器地址
 * @param  NumberofRegisters: 所含字节数
 * @param  RegisterData: 数据起始地址
 * @param  temp: 是否更新IO寄存器
 * @retval None
 */
void WriteData_AD9959(uchar RegisterAddress, uchar NumberofRegisters, uchar *RegisterData, uchar temp)
{
    uchar ControlValue = 0;
    uchar ValueToWrite = 0;
    uchar RegisterIndex = 0;
    uchar i = 0;

    // 创建8位头部
    ControlValue = RegisterAddress;

    SCLK = 0;
    CS = 0;     // 拉低CS

    // 写入控制字
    for(i = 0; i < 8; i++)
    {
        SCLK = 0;
        if(0x80 == (ControlValue & 0x80))
        {
            SDIO0 = 1;  // 发送1到SDIO0引脚
        }
        else
        {
            SDIO0 = 0;  // 发送0到SDIO0引脚
        }
        SCLK = 1;
        ControlValue <<= 1; // 旋转数据
    }
    SCLK = 0;

    // 然后写入数据
    for (RegisterIndex = 0; RegisterIndex < NumberofRegisters; RegisterIndex++)
    {
        ValueToWrite = RegisterData[RegisterIndex];
        for (i = 0; i < 8; i++)
        {
            SCLK = 0;
            if(0x80 == (ValueToWrite & 0x80))
            {
                SDIO0 = 1;  // 发送1到SDIO0引脚
            }
            else
            {
                SDIO0 = 0;  // 发送0到SDIO0引脚
            }
            SCLK = 1;
            ValueToWrite <<= 1; // 旋转数据
        }
        SCLK = 0;
    }

    CS = 1; // 拉高CS
}
/**
 * @brief  AD9959初始化
 * @param  None
 * @retval None
 */
void Init_AD9959(void)
{
    Intserve(); // 初始化 IO 口状态
    IntReset(); // 复位芯片

    WriteData_AD9959(FR1_ADD, 3, FR1_DATA, 1); // 配置功能寄存器1 (20倍频等)
    IO_Update(); // 更新生效

    // 依次设置四个通道的默认频率 - 参考历程文件
    Write_frequence(0, 1000);      // CH0: 1kHz
    Write_frequence(1, 1000);      // CH1: 1kHz
    Write_frequence(2, 1000);      // CH2: 1kHz
    Write_frequence(3, 1000);      // CH3: 1kHz
}
/**
 * @brief  计算频偏字、频率字和发送程序
 * @param  Channel: 通道选择 (0-3)
 * @param  Freq: 频率值
 * @retval None
 */
void Write_frequence(uchar Channel, ulong Freq)
{
    uchar CFTW0_DATA[4] = {0x00, 0x00, 0x00, 0x00};    // 中间变量
    ulong Temp;

    Temp = (ulong)Freq * 8.589934592;  // 将输入频率因子分为四个字节 4.294967296=(2^32)/500000000
    CFTW0_DATA[3] = (uchar)Temp;
    CFTW0_DATA[2] = (uchar)(Temp >> 8);
    CFTW0_DATA[1] = (uchar)(Temp >> 16);
    CFTW0_DATA[0] = (uchar)(Temp >> 24);

    // 根据通道选择对应的 CSR 值 - 参考历程文件
    if(Channel == 0) WriteData_AD9959(CSR_ADD, 1, CSR_DATA0, 1);
    if(Channel == 1) WriteData_AD9959(CSR_ADD, 1, CSR_DATA1, 1);
    if(Channel == 2) WriteData_AD9959(CSR_ADD, 1, CSR_DATA2, 1);
    if(Channel == 3) WriteData_AD9959(CSR_ADD, 1, CSR_DATA3, 1);

    // 写入频率调谐字
    WriteData_AD9959(CFTW0_ADD, 4, CFTW0_DATA, 1);
    IO_Update(); // 每次设置后都更新
}

/**
 * @brief  更新幅度
 * @param  Channel: 通道选择 (0-3)
 * @param  Ampli: 幅度值
 * @retval None
 */
void Write_Amplitude(uchar Channel, unsigned int Ampli)
{
    uint A_temp = Ampli & 0x03FF; // 确保幅度值在 10-bit 范围内
    A_temp |= 0x1000; // 使能幅度缩放因子
    ACR_DATA[2] = (uchar)A_temp;
    ACR_DATA[1] = (uchar)(A_temp >> 8);
    // ACR_DATA[0] 保持默认，可以根据需要修改

    if(Channel == 0) WriteData_AD9959(CSR_ADD, 1, CSR_DATA0, 1);
    if(Channel == 1) WriteData_AD9959(CSR_ADD, 1, CSR_DATA1, 1);
    if(Channel == 2) WriteData_AD9959(CSR_ADD, 1, CSR_DATA2, 1);
    if(Channel == 3) WriteData_AD9959(CSR_ADD, 1, CSR_DATA3, 1);

    WriteData_AD9959(ACR_ADD, 3, ACR_DATA, 1);
    IO_Update(); // 每次设置后都更新
}
/**
 * @brief  写入相位
 * @param  Channel: 通道选择 (0-3)
 * @param  Phase: 相位值
 * @retval None
 */
void Write_Phase(uchar Channel, unsigned int Phase)
{
    uint P_temp = 0;
    P_temp = (uint)Phase * 45.511111;  // 将输入相位差写入，精度1度，45.511111=2^14/360
    CPOW0_DATA[1] = (uchar)P_temp;
    CPOW0_DATA[0] = (uchar)(P_temp >> 8);

    if(Channel == 0)
    {
        WriteData_AD9959(CSR_ADD, 1, CSR_DATA0, 0);     // 控制寄存器写入CH0通道
        WriteData_AD9959(CPOW0_ADD, 2, CPOW0_DATA, 0);
    }
    if(Channel == 1)
    {
        WriteData_AD9959(CSR_ADD, 1, CSR_DATA1, 0);     // 控制寄存器写入CH1通道
        WriteData_AD9959(CPOW0_ADD, 2, CPOW0_DATA, 0);
    }
    if(Channel == 2)
    {
        WriteData_AD9959(CSR_ADD, 1, CSR_DATA2, 0);     // 控制寄存器写入CH2通道
        WriteData_AD9959(CPOW0_ADD, 2, CPOW0_DATA, 0);
    }
    if(Channel == 3)
    {
        WriteData_AD9959(CSR_ADD, 1, CSR_DATA3, 0);     // 控制寄存器写入CH3通道
        WriteData_AD9959(CPOW0_ADD, 2, CPOW0_DATA, 0);
    }
}
