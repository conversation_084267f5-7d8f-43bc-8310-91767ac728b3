Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to mode3.o(.text) for Mode3_Stop_Simulation
    main.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    main.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    main.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    main.o(.text) refers to ad9959.o(.text) for Write_frequence
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to matrix_kbd.o(.text) for MatrixKBD_Init
    main.o(.text) refers to key.o(.text) for Key_Init
    main.o(.text) refers to main.o(.conststring) for .conststring
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to matrix_kbd.o(.text) for USART3_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    ad9959.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9959.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9959.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(.text) refers to ad9959.o(.data) for CSR_DATA0
    lcd.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(.text) refers to delay.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    lcd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    lcd.o(.text) refers to stm32f4xx_fsmc.o(.text) for FSMC_NORSRAMInit
    lcd.o(.text) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    matrix_kbd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    matrix_kbd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    matrix_kbd.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    matrix_kbd.o(.text) refers to misc.o(.text) for NVIC_Init
    matrix_kbd.o(.text) refers to matrix_kbd.o(.data) for tail
    matrix_kbd.o(.text) refers to matrix_kbd.o(.bss) for kbdBuf
    key.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    key.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    key.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key.o(.text) refers to noretval__2printf.o(.text) for __2printf
    key.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    key.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    key.o(.text) refers to matrix_kbd.o(.text) for MatrixKBD_KeyToDigit
    key.o(.text) refers to key.o(.bss) for keyState
    key.o(.text) refers to key.o(.data) for isOutputEnabled
    mode3.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    mode3.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    mode3.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    mode3.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    mode3.o(.text) refers to _printf_str.o(.text) for _printf_str
    mode3.o(.text) refers to noretval__2printf.o(.text) for __2printf
    mode3.o(.text) refers to ad9959.o(.text) for Write_frequence
    mode3.o(.text) refers to lcd.o(.text) for LCD_ShowString
    mode3.o(.text) refers to delay.o(.text) for delay_ms
    mode3.o(.text) refers to mode3.o(.data) for g_app_state
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_ss_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (3234 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing matrix_kbd.o(.rev16_text), (4 bytes).
    Removing matrix_kbd.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing mode3.o(.rev16_text), (4 bytes).
    Removing mode3.o(.revsh_text), (4 bytes).

123 unused section(s) (total 29616 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9959\ad9959.c              0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\MODE3\mode3.c                0x00000000   Number         0  mode3.o ABSOLUTE
    ..\HARDWARE\matrix_kbd\matrix_kbd.c      0x00000000   Number         0  matrix_kbd.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HARDWARE\\AD9959\\ad9959.c           0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\MODE3\\mode3.c             0x00000000   Number         0  mode3.o ABSOLUTE
    ..\\HARDWARE\\matrix_kbd\\matrix_kbd.c   0x00000000   Number         0  matrix_kbd.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000208   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800020e   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x08000214   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800021a   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800021e   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000220   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000224   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800022a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000234   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000234   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000236   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000238   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000238   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800023a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800023a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800023a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000240   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000240   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000244   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000244   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800024c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800024e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800024e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000252   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000258   Section        0  main.o(.text)
    Mode3_Stop_Callback                      0x08000259   Thumb Code    14  main.o(.text)
    Mode3_Simulate_Callback                  0x08000267   Thumb Code    14  main.o(.text)
    Mode3_Learn_Callback                     0x08000275   Thumb Code    14  main.o(.text)
    CalculateFilterResponse                  0x08000283   Thumb Code   256  main.o(.text)
    CalculateAD9959Code                      0x08000383   Thumb Code   220  main.o(.text)
    SetFrequencyAmplitudeOutput              0x0800045f   Thumb Code   144  main.o(.text)
    SetFrequencyOutput                       0x080004ef   Thumb Code    52  main.o(.text)
    SystemInit_Custom                        0x08000523   Thumb Code   124  main.o(.text)
    .text                                    0x0800089c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080008b8   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x080008b9   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000ac8   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000ac8   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000b08   Section        0  misc.o(.text)
    .text                                    0x08000be8   Section        0  stm32f4xx_fsmc.o(.text)
    .text                                    0x080011b0   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08001444   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08001aa0   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08001ef4   Section        0  delay.o(.text)
    .text                                    0x08001ff8   Section        0  usart.o(.text)
    .text                                    0x08002140   Section        0  ad9959.o(.text)
    .text                                    0x08002554   Section        0  lcd.o(.text)
    .text                                    0x080072f4   Section        0  matrix_kbd.o(.text)
    pushKey                                  0x080072f5   Thumb Code    32  matrix_kbd.o(.text)
    USART3_GPIO_Config                       0x0800739d   Thumb Code    70  matrix_kbd.o(.text)
    USART3_Config_Core                       0x080073e3   Thumb Code    70  matrix_kbd.o(.text)
    NVIC_Config_USART3                       0x08007429   Thumb Code    34  matrix_kbd.o(.text)
    parseByte                                0x08007489   Thumb Code   210  matrix_kbd.o(.text)
    .text                                    0x080075a4   Section        0  key.o(.text)
    .text                                    0x08007e44   Section        0  mode3.o(.text)
    .text                                    0x08008378   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800837c   Section        0  noretval__2printf.o(.text)
    .text                                    0x08008394   Section        0  _printf_str.o(.text)
    .text                                    0x080083e8   Section        0  _printf_dec.o(.text)
    .text                                    0x08008460   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080084b8   Section        0  __printf_ss_wp.o(.text)
    .text                                    0x08008618   Section        0  heapauxi.o(.text)
    .text                                    0x0800861e   Section        2  use_no_semi.o(.text)
    .text                                    0x08008620   Section        0  _rserrno.o(.text)
    .text                                    0x08008636   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080086e8   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080086eb   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08008b06   Section        0  _printf_char.o(.text)
    .text                                    0x08008b34   Section        0  _printf_char_file.o(.text)
    .text                                    0x08008b58   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08008b60   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08008b68   Section      138  lludiv10.o(.text)
    .text                                    0x08008bf4   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08008bf5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08008c24   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08008ca4   Section        0  bigflt0.o(.text)
    .text                                    0x08008d88   Section        0  ferror.o(.text)
    .text                                    0x08008d90   Section        8  libspace.o(.text)
    .text                                    0x08008d98   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08008de2   Section        0  exit.o(.text)
    .text                                    0x08008df4   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08008e74   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08008eb2   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08008ef8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08008f58   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08009290   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800936c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08009396   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080093c0   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08009604   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sqrt                          0x08009634   Section        0  sqrt.o(i.__hardfp_sqrt)
    i._is_digit                              0x080096ae   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x080096bc   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dadd                               0x080096e8   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080096e8   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080096f9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$ddiv                               0x08009838   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08009838   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800983f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08009ae8   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08009ae8   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x08009b42   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08009b42   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08009b68   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009b68   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08009cbc   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08009cbc   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009d58   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009d58   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08009d64   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08009d64   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08009d7c   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08009d7c   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08009f14   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08009f14   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08009f25   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fpinit                             0x0800a0e8   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800a0e8   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x0800a0f2   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800a0f2   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$usenofp                            0x0800a0f6   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800a0f8   Section       28  stm32f4xx_fsmc.o(.constdata)
    .constdata                               0x0800a114   Section    53248  lcd.o(.constdata)
    .constdata                               0x08017114   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x08017114   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08017128   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0801713c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0801713c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08017178   Data          64  bigflt0.o(.constdata)
    .conststring                             0x080171d0   Section       74  main.o(.conststring)
    locale$$data                             0x0801723c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08017240   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08017248   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08017254   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08017256   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08017257   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08017258   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000024   Section        4  delay.o(.data)
    fac_us                                   0x20000024   Data           1  delay.o(.data)
    fac_ms                                   0x20000026   Data           2  delay.o(.data)
    .data                                    0x20000028   Section        6  usart.o(.data)
    .data                                    0x2000002e   Section       43  ad9959.o(.data)
    .data                                    0x2000005a   Section        4  lcd.o(.data)
    .data                                    0x20000060   Section       14  matrix_kbd.o(.data)
    head                                     0x20000060   Data           1  matrix_kbd.o(.data)
    tail                                     0x20000061   Data           1  matrix_kbd.o(.data)
    currentBaudRate                          0x20000064   Data           4  matrix_kbd.o(.data)
    rxState                                  0x20000068   Data           1  matrix_kbd.o(.data)
    keyTemp                                  0x20000069   Data           1  matrix_kbd.o(.data)
    cmdBuf                                   0x2000006a   Data           3  matrix_kbd.o(.data)
    cmdIndex                                 0x2000006d   Data           1  matrix_kbd.o(.data)
    .data                                    0x20000070   Section       24  key.o(.data)
    isOutputEnabled                          0x20000070   Data           1  key.o(.data)
    frequencyOutputCallback                  0x20000074   Data           4  key.o(.data)
    frequencyAmplitudeOutputCallback         0x20000078   Data           4  key.o(.data)
    mode3LearnCallback                       0x2000007c   Data           4  key.o(.data)
    mode3SimulateCallback                    0x20000080   Data           4  key.o(.data)
    mode3StopCallback                        0x20000084   Data           4  key.o(.data)
    .data                                    0x20000088   Section       13  mode3.o(.data)
    .bss                                     0x20000098   Section      200  usart.o(.bss)
    .bss                                     0x20000160   Section       14  lcd.o(.bss)
    .bss                                     0x2000016e   Section       16  matrix_kbd.o(.bss)
    kbdBuf                                   0x2000016e   Data          16  matrix_kbd.o(.bss)
    .bss                                     0x20000180   Section       24  key.o(.bss)
    keyState                                 0x20000180   Data          24  key.o(.bss)
    .bss                                     0x20000198   Section       96  libspace.o(.bss)
    HEAP                                     0x200001f8   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200001f8   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200003f8   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200003f8   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200007f8   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000209   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x0800020f   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x08000215   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800021b   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800021f   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000221   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000235   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000237   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000239   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800023b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800023b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000241   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000241   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000245   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000245   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800024d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800024f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800024f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000253   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x0800059f   Thumb Code    56  main.o(.text)
    NMI_Handler                              0x0800089d   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800089f   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080008a3   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080008a7   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080008ab   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080008af   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080008b1   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080008b3   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080008b5   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000995   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x080009ed   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x08000ac9   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000ae3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000ae5   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000b09   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08000b13   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08000b7d   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08000b8b   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08000bad   Thumb Code    40  misc.o(.text)
    FSMC_NORSRAMDeInit                       0x08000be9   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMInit                         0x08000c1f   Thumb Code   230  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMStructInit                   0x08000d05   Thumb Code    48  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMCmd                          0x08000d35   Thumb Code    46  stm32f4xx_fsmc.o(.text)
    FSMC_NANDDeInit                          0x08000d63   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_NANDInit                            0x08000da1   Thumb Code   132  stm32f4xx_fsmc.o(.text)
    FSMC_NANDStructInit                      0x08000e25   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NANDCmd                             0x08000e5b   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_NANDECCCmd                          0x08000eb1   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_GetECC                              0x08000f07   Thumb Code    24  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDDeInit                        0x08000f1f   Thumb Code    36  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDInit                          0x08000f43   Thumb Code   130  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDStructInit                    0x08000fc5   Thumb Code    60  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDCmd                           0x08001001   Thumb Code    44  stm32f4xx_fsmc.o(.text)
    FSMC_ITConfig                            0x0800102d   Thumb Code   128  stm32f4xx_fsmc.o(.text)
    FSMC_GetFlagStatus                       0x080010ad   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_ClearFlag                           0x080010e3   Thumb Code    74  stm32f4xx_fsmc.o(.text)
    FSMC_GetITStatus                         0x0800112d   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_ClearITPendingBit                   0x0800116b   Thumb Code    66  stm32f4xx_fsmc.o(.text)
    GPIO_DeInit                              0x080011b1   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x080012bd   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800134d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800135f   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08001381   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08001393   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800139b   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x080013ad   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x080013b5   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x080013b9   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x080013bd   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x080013c7   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x080013cb   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x080013d3   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08001445   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08001497   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x080014a5   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080014e1   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08001519   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x0800152d   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08001533   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08001561   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08001567   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08001587   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800158d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800159b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x080015a1   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x080015b5   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x080015bb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x080015c1   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x080015dd   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080015f9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x0800160d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08001619   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x0800162d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08001641   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08001657   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08001735   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800176b   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001773   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800177b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08001781   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800179b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x080017b7   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x080017cb   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080017df   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080017f3   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080017f9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x0800181b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08001869   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800188b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080018ad   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x080018cf   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x080018f1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08001913   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001935   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08001957   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08001979   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x0800199b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x080019bd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x080019df   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08001a01   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08001a23   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08001a4b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08001a6d   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08001a7f   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001a95   Thumb Code     8  stm32f4xx_rcc.o(.text)
    USART_DeInit                             0x08001aa1   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08001b6f   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08001c3b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08001c53   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08001c73   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08001c7f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08001c97   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001ca7   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001cbd   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001cd5   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001cdd   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08001ce7   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001cf9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001d11   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001d23   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001d35   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08001d4d   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001d57   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08001d6f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08001d7f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001d97   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08001daf   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08001dc1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08001dd9   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08001deb   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08001e35   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08001e4f   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08001e61   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08001ed7   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x08001ef5   Thumb Code    52  delay.o(.text)
    delay_us                                 0x08001f29   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08001f71   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08001fb9   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x08001ff9   Thumb Code     4  usart.o(.text)
    fputc                                    0x08001ffd   Thumb Code    22  usart.o(.text)
    uart_init                                0x08002013   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x080020b7   Thumb Code   122  usart.o(.text)
    AD9959GPIO_Init                          0x08002141   Thumb Code    64  ad9959.o(.text)
    delay1                                   0x08002181   Thumb Code    18  ad9959.o(.text)
    IntReset                                 0x08002193   Thumb Code    36  ad9959.o(.text)
    IO_Update                                0x080021b7   Thumb Code    36  ad9959.o(.text)
    Intserve                                 0x080021db   Thumb Code    74  ad9959.o(.text)
    WriteData_AD9959                         0x08002225   Thumb Code   252  ad9959.o(.text)
    Write_frequence                          0x08002321   Thumb Code   158  ad9959.o(.text)
    Init_AD9959                              0x080023bf   Thumb Code    68  ad9959.o(.text)
    Write_Amplitude                          0x08002403   Thumb Code   102  ad9959.o(.text)
    Write_Phase                              0x08002469   Thumb Code   168  ad9959.o(.text)
    LCD_WR_REG                               0x08002555   Thumb Code    18  lcd.o(.text)
    LCD_WR_DATA                              0x08002567   Thumb Code    20  lcd.o(.text)
    LCD_RD_DATA                              0x0800257b   Thumb Code    16  lcd.o(.text)
    LCD_WriteReg                             0x0800258b   Thumb Code    14  lcd.o(.text)
    LCD_ReadReg                              0x08002599   Thumb Code    22  lcd.o(.text)
    LCD_WriteRAM_Prepare                     0x080025af   Thumb Code    10  lcd.o(.text)
    LCD_WriteRAM                             0x080025b9   Thumb Code     8  lcd.o(.text)
    LCD_BGR2RGB                              0x080025c1   Thumb Code    26  lcd.o(.text)
    opt_delay                                0x080025db   Thumb Code    14  lcd.o(.text)
    LCD_SetCursor                            0x080025e9   Thumb Code   418  lcd.o(.text)
    LCD_ReadPoint                            0x0800278b   Thumb Code   360  lcd.o(.text)
    LCD_DisplayOn                            0x080028f3   Thumb Code   102  lcd.o(.text)
    LCD_DisplayOff                           0x08002959   Thumb Code    90  lcd.o(.text)
    LCD_Scan_Dir                             0x080029b3   Thumb Code   744  lcd.o(.text)
    LCD_DrawPoint                            0x08002c9b   Thumb Code    28  lcd.o(.text)
    LCD_Fast_DrawPoint                       0x08002cb7   Thumb Code   388  lcd.o(.text)
    LCD_SSD_BackLightSet                     0x08002e3b   Thumb Code    88  lcd.o(.text)
    LCD_Display_Dir                          0x08002e93   Thumb Code   444  lcd.o(.text)
    LCD_Set_Window                           0x0800304f   Thumb Code   546  lcd.o(.text)
    LCD_Clear                                0x08003271   Thumb Code   100  lcd.o(.text)
    LCD_Init                                 0x080032d5   Thumb Code 14696  lcd.o(.text)
    LCD_Fill                                 0x08006c3d   Thumb Code   180  lcd.o(.text)
    LCD_Color_Fill                           0x08006cf1   Thumb Code   100  lcd.o(.text)
    LCD_DrawLine                             0x08006d55   Thumb Code   176  lcd.o(.text)
    LCD_DrawRectangle                        0x08006e05   Thumb Code    60  lcd.o(.text)
    LCD_Draw_Circle                          0x08006e41   Thumb Code   152  lcd.o(.text)
    LCD_ShowChar                             0x08006ed9   Thumb Code   272  lcd.o(.text)
    LCD_Pow                                  0x08006fe9   Thumb Code    22  lcd.o(.text)
    LCD_ShowNum                              0x08006fff   Thumb Code   148  lcd.o(.text)
    LCD_ShowxNum                             0x08007093   Thumb Code   220  lcd.o(.text)
    LCD_ShowString                           0x0800716f   Thumb Code   102  lcd.o(.text)
    lcd_showPicture                          0x080071d5   Thumb Code    66  lcd.o(.text)
    lcd_show_chinese                         0x08007217   Thumb Code   204  lcd.o(.text)
    MatrixKBD_Available                      0x08007315   Thumb Code    28  matrix_kbd.o(.text)
    MatrixKBD_GetKey                         0x08007331   Thumb Code    42  matrix_kbd.o(.text)
    MatrixKBD_KeyToDigit                     0x0800735b   Thumb Code    66  matrix_kbd.o(.text)
    MatrixKBD_Init                           0x0800744b   Thumb Code    24  matrix_kbd.o(.text)
    MatrixKBD_SetBaudRate                    0x08007463   Thumb Code    38  matrix_kbd.o(.text)
    USART3_IRQHandler                        0x0800755b   Thumb Code    32  matrix_kbd.o(.text)
    Key_ClearInputBuffer                     0x080075a5   Thumb Code    28  key.o(.text)
    Key_Init                                 0x080075c1   Thumb Code    42  key.o(.text)
    Key_SetFrequencyOutputCallback           0x080075eb   Thumb Code     6  key.o(.text)
    Key_SetFrequencyAmplitudeOutputCallback  0x080075f1   Thumb Code     6  key.o(.text)
    Key_SetMode3Callbacks                    0x080075f7   Thumb Code    14  key.o(.text)
    Key_HandleFilterLearnMode                0x08007605   Thumb Code   170  key.o(.text)
    Key_BufferToAmplitude                    0x080076af   Thumb Code    96  key.o(.text)
    Key_ValidateFrequency                    0x0800770f   Thumb Code    20  key.o(.text)
    Key_BufferToFrequency                    0x08007723   Thumb Code    34  key.o(.text)
    Key_HandleFrequencyAmplitudeMode         0x08007745   Thumb Code   528  key.o(.text)
    Key_HandleFrequencyMode                  0x08007955   Thumb Code   696  key.o(.text)
    Key_SwitchMode                           0x08007c0d   Thumb Code   144  key.o(.text)
    Key_ProcessKeyInput                      0x08007c9d   Thumb Code    80  key.o(.text)
    Key_ProcessInput                         0x08007ced   Thumb Code    28  key.o(.text)
    Key_GetCurrentMode                       0x08007d09   Thumb Code     6  key.o(.text)
    Key_IsOutputEnabled                      0x08007d0f   Thumb Code     6  key.o(.text)
    Mode3_Init                               0x08007e45   Thumb Code   104  mode3.o(.text)
    Mode3_LCD_ShowString_Simplified          0x08007ead   Thumb Code    34  mode3.o(.text)
    Mode3_Start_Learn                        0x08007ecf   Thumb Code   124  mode3.o(.text)
    Mode3_Start_Simulation                   0x08007f4b   Thumb Code   174  mode3.o(.text)
    Mode3_Stop_Simulation                    0x08007ff9   Thumb Code    66  mode3.o(.text)
    Mode3_Display_Results                    0x0800803b   Thumb Code   100  mode3.o(.text)
    Mode3_Process_State_Machine              0x0800809f   Thumb Code    52  mode3.o(.text)
    __use_no_semihosting                     0x08008379   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800837d   Thumb Code    20  noretval__2printf.o(.text)
    _printf_str                              0x08008395   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080083e9   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x08008461   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08008461   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080084b9   Thumb Code   352  __printf_ss_wp.o(.text)
    __use_two_region_memory                  0x08008619   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800861b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800861d   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800861f   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800861f   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08008621   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800862b   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x08008637   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x080086e9   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800889b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_cs_common                        0x08008b07   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08008b1b   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08008b2b   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08008b35   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08008b59   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08008b61   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08008b61   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08008b61   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08008b69   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08008bff   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x08008c25   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08008ca5   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08008d89   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08008d91   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08008d91   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08008d91   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08008d99   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08008de3   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08008df5   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08008e75   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08008eb3   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08008ef9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08008f59   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08009291   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800936d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08009397   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080093c1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08009605   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sqrt                            0x08009635   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    _is_digit                                0x080096af   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x080096bd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dadd                             0x080096e9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080096e9   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __aeabi_ddiv                             0x08009839   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08009839   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08009ae9   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08009ae9   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x08009b43   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08009b43   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08009b69   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009b69   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08009cbd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009d59   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08009d65   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08009d65   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08009d7d   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08009f15   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08009f15   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    _fp_init                                 0x0800a0e9   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800a0f1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800a0f1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x0800a0f3   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x0800a0f6   Number         0  usenofp.o(x$fpl$usenofp)
    FSMC_DefaultTimingStruct                 0x0800a0f8   Data          28  stm32f4xx_fsmc.o(.constdata)
    asc2_1206                                0x0800a114   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800a588   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800ab78   Data        3420  lcd.o(.constdata)
    chinese_1616                             0x0800b8d4   Data        1600  lcd.o(.constdata)
    chinese_2424                             0x0800bf14   Data       12800  lcd.o(.constdata)
    gImage_biaozhi                           0x0800f114   Data       32768  lcd.o(.constdata)
    Region$$Table$$Base                      0x0801721c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0801723c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    __stdout                                 0x20000028   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000002c   Data           2  usart.o(.data)
    CSR_DATA0                                0x2000002e   Data           1  ad9959.o(.data)
    CSR_DATA1                                0x2000002f   Data           1  ad9959.o(.data)
    CSR_DATA2                                0x20000030   Data           1  ad9959.o(.data)
    CSR_DATA3                                0x20000031   Data           1  ad9959.o(.data)
    FR1_DATA                                 0x20000032   Data           3  ad9959.o(.data)
    FR2_DATA                                 0x20000035   Data           2  ad9959.o(.data)
    CFR_DATA                                 0x20000037   Data           3  ad9959.o(.data)
    CFTW0_DATA0                              0x2000003a   Data           4  ad9959.o(.data)
    CFTW0_DATA1                              0x2000003e   Data           4  ad9959.o(.data)
    CFTW0_DATA2                              0x20000042   Data           4  ad9959.o(.data)
    CFTW0_DATA3                              0x20000046   Data           4  ad9959.o(.data)
    CPOW0_DATA                               0x2000004a   Data           2  ad9959.o(.data)
    ACR_DATA                                 0x2000004c   Data           3  ad9959.o(.data)
    LSRR_DATA                                0x2000004f   Data           2  ad9959.o(.data)
    RDW_DATA                                 0x20000051   Data           4  ad9959.o(.data)
    FDW_DATA                                 0x20000055   Data           4  ad9959.o(.data)
    POINT_COLOR                              0x2000005a   Data           2  lcd.o(.data)
    BACK_COLOR                               0x2000005c   Data           2  lcd.o(.data)
    g_app_state                              0x20000088   Data           1  mode3.o(.data)
    g_filter_type                            0x20000089   Data           1  mode3.o(.data)
    g_sweep_current_step                     0x2000008c   Data           4  mode3.o(.data)
    g_current_freq_sweep                     0x20000090   Data           4  mode3.o(.data)
    stop_flag                                0x20000094   Data           1  mode3.o(.data)
    USART_RX_BUF                             0x20000098   Data         200  usart.o(.bss)
    lcddev                                   0x20000160   Data          14  lcd.o(.bss)
    __libspace_start                         0x20000198   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001f8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000172f0, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00017258, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          224    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1253  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1524    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1526    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1528    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1250    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1249    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1247    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1248    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         1246    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000214   0x08000214   0x00000006   Code   RO         1245    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800021a   0x0800021a   0x00000004   Code   RO         1304    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800021e   0x0800021e   0x00000002   Code   RO         1399    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000220   0x08000220   0x00000004   Code   RO         1400    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1403    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1406    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1408    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1410    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000006   Code   RO         1411    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         1413    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         1415    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         1417    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x0000000a   Code   RO         1418    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1419    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1421    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1423    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1425    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1427    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1429    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1431    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1433    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1437    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1439    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1441    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1443    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000234   0x08000234   0x00000002   Code   RO         1444    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000002   Code   RO         1472    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1481    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1483    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1485    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1488    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1491    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1493    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1496    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000238   0x08000238   0x00000002   Code   RO         1497    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1293    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1315    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800023a   0x0800023a   0x00000006   Code   RO         1327    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1317    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000004   Code   RO         1318    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000244   0x08000244   0x00000000   Code   RO         1320    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000244   0x08000244   0x00000008   Code   RO         1321    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO         1445    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO         1452    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800024e   0x0800024e   0x00000004   Code   RO         1453    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000006   Code   RO         1454    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000258   0x08000258   0x00000644   Code   RO            3    .text               main.o
    0x0800089c   0x0800089c   0x0000001a   Code   RO          175    .text               stm32f4xx_it.o
    0x080008b6   0x080008b6   0x00000002   PAD
    0x080008b8   0x080008b8   0x00000210   Code   RO          198    .text               system_stm32f4xx.o
    0x08000ac8   0x08000ac8   0x00000040   Code   RO          225    .text               startup_stm32f40_41xxx.o
    0x08000b08   0x08000b08   0x000000e0   Code   RO          231    .text               misc.o
    0x08000be8   0x08000be8   0x000005c8   Code   RO          583    .text               stm32f4xx_fsmc.o
    0x080011b0   0x080011b0   0x00000294   Code   RO          606    .text               stm32f4xx_gpio.o
    0x08001444   0x08001444   0x0000065c   Code   RO          769    .text               stm32f4xx_rcc.o
    0x08001aa0   0x08001aa0   0x00000454   Code   RO          934    .text               stm32f4xx_usart.o
    0x08001ef4   0x08001ef4   0x00000104   Code   RO          974    .text               delay.o
    0x08001ff8   0x08001ff8   0x00000148   Code   RO         1014    .text               usart.o
    0x08002140   0x08002140   0x00000414   Code   RO         1045    .text               ad9959.o
    0x08002554   0x08002554   0x00004da0   Code   RO         1071    .text               lcd.o
    0x080072f4   0x080072f4   0x000002b0   Code   RO         1113    .text               matrix_kbd.o
    0x080075a4   0x080075a4   0x000008a0   Code   RO         1139    .text               key.o
    0x08007e44   0x08007e44   0x00000534   Code   RO         1162    .text               mode3.o
    0x08008378   0x08008378   0x00000002   Code   RO         1192    .text               c_w.l(use_no_semi_2.o)
    0x0800837a   0x0800837a   0x00000002   PAD
    0x0800837c   0x0800837c   0x00000018   Code   RO         1196    .text               c_w.l(noretval__2printf.o)
    0x08008394   0x08008394   0x00000052   Code   RO         1200    .text               c_w.l(_printf_str.o)
    0x080083e6   0x080083e6   0x00000002   PAD
    0x080083e8   0x080083e8   0x00000078   Code   RO         1202    .text               c_w.l(_printf_dec.o)
    0x08008460   0x08008460   0x00000058   Code   RO         1207    .text               c_w.l(_printf_hex_int.o)
    0x080084b8   0x080084b8   0x00000160   Code   RO         1240    .text               c_w.l(__printf_ss_wp.o)
    0x08008618   0x08008618   0x00000006   Code   RO         1251    .text               c_w.l(heapauxi.o)
    0x0800861e   0x0800861e   0x00000002   Code   RO         1291    .text               c_w.l(use_no_semi.o)
    0x08008620   0x08008620   0x00000016   Code   RO         1294    .text               c_w.l(_rserrno.o)
    0x08008636   0x08008636   0x000000b2   Code   RO         1296    .text               c_w.l(_printf_intcommon.o)
    0x080086e8   0x080086e8   0x0000041e   Code   RO         1298    .text               c_w.l(_printf_fp_dec.o)
    0x08008b06   0x08008b06   0x0000002c   Code   RO         1300    .text               c_w.l(_printf_char.o)
    0x08008b32   0x08008b32   0x00000002   PAD
    0x08008b34   0x08008b34   0x00000024   Code   RO         1302    .text               c_w.l(_printf_char_file.o)
    0x08008b58   0x08008b58   0x00000008   Code   RO         1332    .text               c_w.l(rt_locale_intlibspace.o)
    0x08008b60   0x08008b60   0x00000008   Code   RO         1337    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08008b68   0x08008b68   0x0000008a   Code   RO         1339    .text               c_w.l(lludiv10.o)
    0x08008bf2   0x08008bf2   0x00000002   PAD
    0x08008bf4   0x08008bf4   0x00000030   Code   RO         1341    .text               c_w.l(_printf_char_common.o)
    0x08008c24   0x08008c24   0x00000080   Code   RO         1343    .text               c_w.l(_printf_fp_infnan.o)
    0x08008ca4   0x08008ca4   0x000000e4   Code   RO         1345    .text               c_w.l(bigflt0.o)
    0x08008d88   0x08008d88   0x00000008   Code   RO         1370    .text               c_w.l(ferror.o)
    0x08008d90   0x08008d90   0x00000008   Code   RO         1383    .text               c_w.l(libspace.o)
    0x08008d98   0x08008d98   0x0000004a   Code   RO         1386    .text               c_w.l(sys_stackheap_outer.o)
    0x08008de2   0x08008de2   0x00000012   Code   RO         1390    .text               c_w.l(exit.o)
    0x08008df4   0x08008df4   0x00000080   Code   RO         1392    .text               c_w.l(strcmpv7m.o)
    0x08008e74   0x08008e74   0x0000003e   Code   RO         1348    CL$$btod_d2e        c_w.l(btod.o)
    0x08008eb2   0x08008eb2   0x00000046   Code   RO         1350    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08008ef8   0x08008ef8   0x00000060   Code   RO         1349    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08008f58   0x08008f58   0x00000338   Code   RO         1358    CL$$btod_div_common  c_w.l(btod.o)
    0x08009290   0x08009290   0x000000dc   Code   RO         1355    CL$$btod_e2e        c_w.l(btod.o)
    0x0800936c   0x0800936c   0x0000002a   Code   RO         1352    CL$$btod_ediv       c_w.l(btod.o)
    0x08009396   0x08009396   0x0000002a   Code   RO         1351    CL$$btod_emul       c_w.l(btod.o)
    0x080093c0   0x080093c0   0x00000244   Code   RO         1357    CL$$btod_mult_common  c_w.l(btod.o)
    0x08009604   0x08009604   0x00000030   Code   RO         1381    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08009634   0x08009634   0x0000007a   Code   RO         1279    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x080096ae   0x080096ae   0x0000000e   Code   RO         1235    i._is_digit         c_w.l(__printf_wp.o)
    0x080096bc   0x080096bc   0x0000002c   Code   RO         1375    locale$$code        c_w.l(lc_numeric_c.o)
    0x080096e8   0x080096e8   0x00000150   Code   RO         1255    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08009838   0x08009838   0x000002b0   Code   RO         1262    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08009ae8   0x08009ae8   0x0000005a   Code   RO         1265    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08009b42   0x08009b42   0x00000026   Code   RO         1269    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08009b68   0x08009b68   0x00000154   Code   RO         1275    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08009cbc   0x08009cbc   0x0000009c   Code   RO         1307    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08009d58   0x08009d58   0x0000000c   Code   RO         1309    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08009d64   0x08009d64   0x00000016   Code   RO         1256    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08009d7a   0x08009d7a   0x00000002   PAD
    0x08009d7c   0x08009d7c   0x00000198   Code   RO         1311    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08009f14   0x08009f14   0x000001d4   Code   RO         1257    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800a0e8   0x0800a0e8   0x0000000a   Code   RO         1449    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800a0f2   0x0800a0f2   0x00000004   Code   RO         1277    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800a0f6   0x0800a0f6   0x00000000   Code   RO         1313    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800a0f6   0x0800a0f6   0x00000002   PAD
    0x0800a0f8   0x0800a0f8   0x0000001c   Data   RO          584    .constdata          stm32f4xx_fsmc.o
    0x0800a114   0x0800a114   0x0000d000   Data   RO         1073    .constdata          lcd.o
    0x08017114   0x08017114   0x00000028   Data   RO         1208    .constdata          c_w.l(_printf_hex_int.o)
    0x0801713c   0x0801713c   0x00000094   Data   RO         1346    .constdata          c_w.l(bigflt0.o)
    0x080171d0   0x080171d0   0x0000004a   Data   RO            4    .conststring        main.o
    0x0801721a   0x0801721a   0x00000002   PAD
    0x0801721c   0x0801721c   0x00000020   Data   RO         1522    Region$$Table       anon$$obj.o
    0x0801723c   0x0801723c   0x0000001c   Data   RO         1374    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08017258, Size: 0x000007f8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08017258   0x00000014   Data   RW          199    .data               system_stm32f4xx.o
    0x20000014   0x0801726c   0x00000010   Data   RW          770    .data               stm32f4xx_rcc.o
    0x20000024   0x0801727c   0x00000004   Data   RW          975    .data               delay.o
    0x20000028   0x08017280   0x00000006   Data   RW         1016    .data               usart.o
    0x2000002e   0x08017286   0x0000002b   Data   RW         1046    .data               ad9959.o
    0x20000059   0x080172b1   0x00000001   PAD
    0x2000005a   0x080172b2   0x00000004   Data   RW         1074    .data               lcd.o
    0x2000005e   0x080172b6   0x00000002   PAD
    0x20000060   0x080172b8   0x0000000e   Data   RW         1115    .data               matrix_kbd.o
    0x2000006e   0x080172c6   0x00000002   PAD
    0x20000070   0x080172c8   0x00000018   Data   RW         1141    .data               key.o
    0x20000088   0x080172e0   0x0000000d   Data   RW         1163    .data               mode3.o
    0x20000095   0x080172ed   0x00000003   PAD
    0x20000098        -       0x000000c8   Zero   RW         1015    .bss                usart.o
    0x20000160        -       0x0000000e   Zero   RW         1072    .bss                lcd.o
    0x2000016e        -       0x00000010   Zero   RW         1114    .bss                matrix_kbd.o
    0x2000017e   0x080172ed   0x00000002   PAD
    0x20000180        -       0x00000018   Zero   RW         1140    .bss                key.o
    0x20000198        -       0x00000060   Zero   RW         1384    .bss                c_w.l(libspace.o)
    0x200001f8        -       0x00000200   Zero   RW          223    HEAP                startup_stm32f40_41xxx.o
    0x200003f8        -       0x00000400   Zero   RW          222    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1044         68          0         43          0       4871   ad9959.o
       260          8          0          4          0       1485   delay.o
      2208        836          0         24         24       5201   key.o
     19872        192      53248          4         14      23884   lcd.o
      1604        710         74          0          0      44179   main.o
       688         54          0         14         16       3379   matrix_kbd.o
       224         20          0          0          0     230977   misc.o
      1332        684          0         13          0       3534   mode3.o
        64         26        392          0       1536        832   startup_stm32f40_41xxx.o
      1480         18         28          0          0       5654   stm32f4xx_fsmc.o
       660         44          0          0          0       4161   stm32f4xx_gpio.o
        26          0          0          0          0       1222   stm32f4xx_it.o
      1628         52          0         16          0      13040   stm32f4xx_rcc.o
      1108         34          0          0          0       7884   stm32f4xx_usart.o
       528         46          0         20          0       1783   system_stm32f4xx.o
       328         16          0          6        200       3334   usart.o

    ----------------------------------------------------------------------
     33056       <USER>      <GROUP>        152       1792     355420   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          2          8          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       352          0          0          0          0         88   __printf_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        492   daddsub_clz.o
       688        140          0          0          0        256   ddiv.o
        90          4          0          0          0        140   dfixu.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
      7760        <USER>        <GROUP>          0         96       5136   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5004        212        216          0         96       3052   c_w.l
      2572        232          0          0          0       1812   fz_wm.l
       170          0          0          0          0        272   m_wm.l

    ----------------------------------------------------------------------
      7760        <USER>        <GROUP>          0         96       5136   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     40816       3252      53992        152       1888     353892   Grand Totals
     40816       3252      53992        152       1888     353892   ELF Image Totals
     40816       3252      53992        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                94808 (  92.59kB)
    Total RW  Size (RW Data + ZI Data)              2040 (   1.99kB)
    Total ROM Size (Code + RO Data + RW Data)      94960 (  92.73kB)

==============================================================================

