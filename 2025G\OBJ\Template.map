Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    main.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    main.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    main.o(.text) refers to ad9959.o(.text) for Write_frequence
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to matrix_kbd.o(.text) for MatrixKBD_Init
    main.o(.text) refers to key.o(.text) for Key_Init
    main.o(.text) refers to main.o(.conststring) for .conststring
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to matrix_kbd.o(.text) for USART3_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    ad9959.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9959.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9959.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(.text) refers to ad9959.o(.data) for CSR_DATA0
    lcd.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(.text) refers to delay.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    lcd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    lcd.o(.text) refers to stm32f4xx_fsmc.o(.text) for FSMC_NORSRAMInit
    lcd.o(.text) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    matrix_kbd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    matrix_kbd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    matrix_kbd.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    matrix_kbd.o(.text) refers to misc.o(.text) for NVIC_Init
    matrix_kbd.o(.text) refers to matrix_kbd.o(.data) for tail
    matrix_kbd.o(.text) refers to matrix_kbd.o(.bss) for kbdBuf
    key.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    key.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    key.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key.o(.text) refers to noretval__2printf.o(.text) for __2printf
    key.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    key.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    key.o(.text) refers to matrix_kbd.o(.text) for MatrixKBD_KeyToDigit
    key.o(.text) refers to key.o(.bss) for keyState
    key.o(.text) refers to key.o(.data) for isOutputEnabled
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_ss_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (3234 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.text), (19872 bytes).
    Removing lcd.o(.bss), (14 bytes).
    Removing lcd.o(.constdata), (53248 bytes).
    Removing lcd.o(.data), (4 bytes).
    Removing matrix_kbd.o(.rev16_text), (4 bytes).
    Removing matrix_kbd.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).

127 unused section(s) (total 104254 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9959\ad9959.c              0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\matrix_kbd\matrix_kbd.c      0x00000000   Number         0  matrix_kbd.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HARDWARE\\AD9959\\ad9959.c           0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\matrix_kbd\\matrix_kbd.c   0x00000000   Number         0  matrix_kbd.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000208   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000212   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000214   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000218   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800021e   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000228   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800022a   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800022c   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800022e   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800022e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800022e   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000234   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000238   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000238   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000240   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000242   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000242   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000246   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800024c   Section        0  main.o(.text)
    CalculateFilterResponse                  0x0800024d   Thumb Code   256  main.o(.text)
    CalculateAD9959Code                      0x0800034d   Thumb Code   220  main.o(.text)
    SetFrequencyAmplitudeOutput              0x08000429   Thumb Code   144  main.o(.text)
    SetFrequencyOutput                       0x080004b9   Thumb Code    52  main.o(.text)
    SystemInit_Custom                        0x080004ed   Thumb Code   120  main.o(.text)
    .text                                    0x080007c8   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080007e4   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x080007e5   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x080009f4   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080009f4   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000a34   Section        0  misc.o(.text)
    .text                                    0x08000b14   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08000da8   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08001404   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08001858   Section        0  delay.o(.text)
    .text                                    0x0800195c   Section        0  usart.o(.text)
    .text                                    0x08001aa4   Section        0  ad9959.o(.text)
    .text                                    0x08001eb8   Section        0  matrix_kbd.o(.text)
    pushKey                                  0x08001eb9   Thumb Code    32  matrix_kbd.o(.text)
    USART3_GPIO_Config                       0x08001f61   Thumb Code    70  matrix_kbd.o(.text)
    USART3_Config_Core                       0x08001fa7   Thumb Code    70  matrix_kbd.o(.text)
    NVIC_Config_USART3                       0x08001fed   Thumb Code    34  matrix_kbd.o(.text)
    parseByte                                0x0800204d   Thumb Code   210  matrix_kbd.o(.text)
    .text                                    0x08002168   Section        0  key.o(.text)
    .text                                    0x08002834   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08002838   Section        0  noretval__2printf.o(.text)
    .text                                    0x08002850   Section        0  _printf_dec.o(.text)
    .text                                    0x080028c8   Section        0  __printf_ss_wp.o(.text)
    .text                                    0x08002a28   Section        0  heapauxi.o(.text)
    .text                                    0x08002a2e   Section        2  use_no_semi.o(.text)
    .text                                    0x08002a30   Section        0  _rserrno.o(.text)
    .text                                    0x08002a46   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08002af8   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08002afb   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08002f18   Section        0  _printf_char_file.o(.text)
    .text                                    0x08002f3c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08002f44   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08002f4c   Section      138  lludiv10.o(.text)
    .text                                    0x08002fd8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08002fd9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08003008   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08003088   Section        0  bigflt0.o(.text)
    .text                                    0x0800316c   Section        0  ferror.o(.text)
    .text                                    0x08003174   Section        8  libspace.o(.text)
    .text                                    0x0800317c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080031c6   Section        0  exit.o(.text)
    .text                                    0x080031d8   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08003258   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08003296   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080032dc   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800333c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08003674   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08003750   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800377a   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080037a4   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x080039e8   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sqrt                          0x08003a18   Section        0  sqrt.o(i.__hardfp_sqrt)
    i._is_digit                              0x08003a92   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08003aa0   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dadd                               0x08003acc   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08003acc   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08003add   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$ddiv                               0x08003c1c   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08003c1c   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08003c23   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08003ecc   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08003ecc   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x08003f26   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08003f26   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08003f4c   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08003f4c   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080040a0   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080040a0   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800413c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800413c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08004148   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08004148   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08004160   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08004160   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x080042f8   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x080042f8   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08004309   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fpinit                             0x080044cc   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080044cc   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080044d6   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080044d6   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$usenofp                            0x080044da   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080044dc   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080044dc   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08004518   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08004570   Section       74  main.o(.conststring)
    locale$$data                             0x080045dc   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080045e0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080045e8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080045f4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080045f6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080045f7   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080045f8   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000024   Section        4  delay.o(.data)
    fac_us                                   0x20000024   Data           1  delay.o(.data)
    fac_ms                                   0x20000026   Data           2  delay.o(.data)
    .data                                    0x20000028   Section        6  usart.o(.data)
    .data                                    0x2000002e   Section       43  ad9959.o(.data)
    .data                                    0x2000005c   Section       14  matrix_kbd.o(.data)
    head                                     0x2000005c   Data           1  matrix_kbd.o(.data)
    tail                                     0x2000005d   Data           1  matrix_kbd.o(.data)
    currentBaudRate                          0x20000060   Data           4  matrix_kbd.o(.data)
    rxState                                  0x20000064   Data           1  matrix_kbd.o(.data)
    keyTemp                                  0x20000065   Data           1  matrix_kbd.o(.data)
    cmdBuf                                   0x20000066   Data           3  matrix_kbd.o(.data)
    cmdIndex                                 0x20000069   Data           1  matrix_kbd.o(.data)
    .data                                    0x2000006c   Section       12  key.o(.data)
    isOutputEnabled                          0x2000006c   Data           1  key.o(.data)
    frequencyOutputCallback                  0x20000070   Data           4  key.o(.data)
    frequencyAmplitudeOutputCallback         0x20000074   Data           4  key.o(.data)
    .bss                                     0x20000078   Section      200  usart.o(.bss)
    .bss                                     0x20000140   Section       16  matrix_kbd.o(.bss)
    kbdBuf                                   0x20000140   Data          16  matrix_kbd.o(.bss)
    .bss                                     0x20000150   Section       24  key.o(.bss)
    keyState                                 0x20000150   Data          24  key.o(.bss)
    .bss                                     0x20000168   Section       96  libspace.o(.bss)
    HEAP                                     0x200001c8   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200001c8   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200003c8   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200003c8   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200007c8   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000209   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_percent_end                      0x0800020f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000213   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800022b   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800022f   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800022f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800022f   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000241   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000247   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000565   Thumb Code    34  main.o(.text)
    NMI_Handler                              0x080007c9   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080007cb   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080007cf   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080007d3   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080007d7   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080007db   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080007dd   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080007df   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080007e1   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x080008c1   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000919   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x080009f5   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000a0f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000a11   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000a35   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08000a3f   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08000aa9   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08000ab7   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08000ad9   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08000b15   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x08000c21   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08000cb1   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000cc3   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000ce5   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08000cf7   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000cff   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000d11   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08000d19   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08000d1d   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x08000d21   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08000d2b   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x08000d2f   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08000d37   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08000da9   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08000dfb   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000e09   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000e45   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000e7d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x08000e91   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08000e97   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08000ec5   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08000ecb   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08000eeb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08000ef1   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x08000eff   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08000f05   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08000f19   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000f1f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08000f25   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08000f41   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000f5d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000f71   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08000f7d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08000f91   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08000fa5   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000fbb   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08001099   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x080010cf   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x080010d7   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x080010df   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x080010e5   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x080010ff   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x0800111b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800112f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x08001143   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08001157   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x0800115d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x0800117f   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x080011cd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080011ef   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001211   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08001233   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08001255   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08001277   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001299   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080012bb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x080012dd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x080012ff   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08001321   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08001343   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08001365   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08001387   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x080013af   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x080013d1   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x080013e3   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x080013f9   Thumb Code     8  stm32f4xx_rcc.o(.text)
    USART_DeInit                             0x08001405   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x080014d3   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x0800159f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x080015b7   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x080015d7   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x080015e3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x080015fb   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x0800160b   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001621   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001639   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001641   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x0800164b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x0800165d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001675   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001687   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001699   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x080016b1   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x080016bb   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x080016d3   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x080016e3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x080016fb   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08001713   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08001725   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x0800173d   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x0800174f   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08001799   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x080017b3   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x080017c5   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x0800183b   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x08001859   Thumb Code    52  delay.o(.text)
    delay_us                                 0x0800188d   Thumb Code    72  delay.o(.text)
    delay_xms                                0x080018d5   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x0800191d   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x0800195d   Thumb Code     4  usart.o(.text)
    fputc                                    0x08001961   Thumb Code    22  usart.o(.text)
    uart_init                                0x08001977   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x08001a1b   Thumb Code   122  usart.o(.text)
    AD9959GPIO_Init                          0x08001aa5   Thumb Code    64  ad9959.o(.text)
    delay1                                   0x08001ae5   Thumb Code    18  ad9959.o(.text)
    IntReset                                 0x08001af7   Thumb Code    36  ad9959.o(.text)
    IO_Update                                0x08001b1b   Thumb Code    36  ad9959.o(.text)
    Intserve                                 0x08001b3f   Thumb Code    74  ad9959.o(.text)
    WriteData_AD9959                         0x08001b89   Thumb Code   252  ad9959.o(.text)
    Write_frequence                          0x08001c85   Thumb Code   158  ad9959.o(.text)
    Init_AD9959                              0x08001d23   Thumb Code    68  ad9959.o(.text)
    Write_Amplitude                          0x08001d67   Thumb Code   102  ad9959.o(.text)
    Write_Phase                              0x08001dcd   Thumb Code   168  ad9959.o(.text)
    MatrixKBD_Available                      0x08001ed9   Thumb Code    28  matrix_kbd.o(.text)
    MatrixKBD_GetKey                         0x08001ef5   Thumb Code    42  matrix_kbd.o(.text)
    MatrixKBD_KeyToDigit                     0x08001f1f   Thumb Code    66  matrix_kbd.o(.text)
    MatrixKBD_Init                           0x0800200f   Thumb Code    24  matrix_kbd.o(.text)
    MatrixKBD_SetBaudRate                    0x08002027   Thumb Code    38  matrix_kbd.o(.text)
    USART3_IRQHandler                        0x0800211f   Thumb Code    32  matrix_kbd.o(.text)
    Key_ClearInputBuffer                     0x08002169   Thumb Code    28  key.o(.text)
    Key_Init                                 0x08002185   Thumb Code    38  key.o(.text)
    Key_SetFrequencyOutputCallback           0x080021ab   Thumb Code     6  key.o(.text)
    Key_SetFrequencyAmplitudeOutputCallback  0x080021b1   Thumb Code     6  key.o(.text)
    Key_BufferToAmplitude                    0x080021b7   Thumb Code    96  key.o(.text)
    Key_ValidateFrequency                    0x08002217   Thumb Code    20  key.o(.text)
    Key_BufferToFrequency                    0x0800222b   Thumb Code    34  key.o(.text)
    Key_HandleFrequencyAmplitudeMode         0x0800224d   Thumb Code   528  key.o(.text)
    Key_HandleFrequencyMode                  0x0800245d   Thumb Code   152  key.o(.text)
    Key_SwitchMode                           0x080024f5   Thumb Code   530  key.o(.text)
    Key_ProcessKeyInput                      0x08002707   Thumb Code    74  key.o(.text)
    Key_ProcessInput                         0x08002751   Thumb Code    28  key.o(.text)
    Key_GetCurrentMode                       0x0800276d   Thumb Code     6  key.o(.text)
    Key_IsOutputEnabled                      0x08002773   Thumb Code     6  key.o(.text)
    __use_no_semihosting                     0x08002835   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08002839   Thumb Code    20  noretval__2printf.o(.text)
    _printf_int_dec                          0x08002851   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080028c9   Thumb Code   352  __printf_ss_wp.o(.text)
    __use_two_region_memory                  0x08002a29   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08002a2b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08002a2d   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08002a2f   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08002a2f   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08002a31   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08002a3b   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x08002a47   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08002af9   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08002cab   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_file                        0x08002f19   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08002f3d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08002f45   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08002f45   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08002f45   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08002f4d   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08002fe3   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x08003009   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08003089   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x0800316d   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08003175   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08003175   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08003175   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800317d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080031c7   Thumb Code    18  exit.o(.text)
    strcmp                                   0x080031d9   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08003259   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08003297   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080032dd   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800333d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08003675   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08003751   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800377b   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080037a5   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x080039e9   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sqrt                            0x08003a19   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    _is_digit                                0x08003a93   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x08003aa1   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dadd                             0x08003acd   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08003acd   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __aeabi_ddiv                             0x08003c1d   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08003c1d   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08003ecd   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08003ecd   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x08003f27   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08003f27   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08003f4d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08003f4d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080040a1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800413d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08004149   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08004149   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08004161   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x080042f9   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080042f9   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    _fp_init                                 0x080044cd   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080044d5   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080044d5   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080044d7   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x080044da   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080045bc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080045dc   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    __stdout                                 0x20000028   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000002c   Data           2  usart.o(.data)
    CSR_DATA0                                0x2000002e   Data           1  ad9959.o(.data)
    CSR_DATA1                                0x2000002f   Data           1  ad9959.o(.data)
    CSR_DATA2                                0x20000030   Data           1  ad9959.o(.data)
    CSR_DATA3                                0x20000031   Data           1  ad9959.o(.data)
    FR1_DATA                                 0x20000032   Data           3  ad9959.o(.data)
    FR2_DATA                                 0x20000035   Data           2  ad9959.o(.data)
    CFR_DATA                                 0x20000037   Data           3  ad9959.o(.data)
    CFTW0_DATA0                              0x2000003a   Data           4  ad9959.o(.data)
    CFTW0_DATA1                              0x2000003e   Data           4  ad9959.o(.data)
    CFTW0_DATA2                              0x20000042   Data           4  ad9959.o(.data)
    CFTW0_DATA3                              0x20000046   Data           4  ad9959.o(.data)
    CPOW0_DATA                               0x2000004a   Data           2  ad9959.o(.data)
    ACR_DATA                                 0x2000004c   Data           3  ad9959.o(.data)
    LSRR_DATA                                0x2000004f   Data           2  ad9959.o(.data)
    RDW_DATA                                 0x20000051   Data           4  ad9959.o(.data)
    FDW_DATA                                 0x20000055   Data           4  ad9959.o(.data)
    USART_RX_BUF                             0x20000078   Data         200  usart.o(.bss)
    __libspace_start                         0x20000168   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001c8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004670, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000045f8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          220    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1216  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1485    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1487    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1489    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1213    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1212    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1210    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1211    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1265    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000212   0x08000212   0x00000002   Code   RO         1360    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000214   0x08000214   0x00000004   Code   RO         1361    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1364    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1367    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1369    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1371    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000006   Code   RO         1372    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1374    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1376    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1378    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x0000000a   Code   RO         1379    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1380    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1382    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1384    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1386    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1388    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1390    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1392    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1394    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1398    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1400    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1402    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1404    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000002   Code   RO         1405    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000002   Code   RO         1433    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1442    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1444    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1446    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1449    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1452    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1454    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         1457    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000002   Code   RO         1458    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1256    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1276    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000006   Code   RO         1288    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1278    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000004   Code   RO         1279    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         1281    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000008   Code   RO         1282    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000002   Code   RO         1406    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1413    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000242   0x08000242   0x00000004   Code   RO         1414    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000246   0x08000246   0x00000006   Code   RO         1415    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x0000057c   Code   RO            3    .text               main.o
    0x080007c8   0x080007c8   0x0000001a   Code   RO          171    .text               stm32f4xx_it.o
    0x080007e2   0x080007e2   0x00000002   PAD
    0x080007e4   0x080007e4   0x00000210   Code   RO          194    .text               system_stm32f4xx.o
    0x080009f4   0x080009f4   0x00000040   Code   RO          221    .text               startup_stm32f40_41xxx.o
    0x08000a34   0x08000a34   0x000000e0   Code   RO          227    .text               misc.o
    0x08000b14   0x08000b14   0x00000294   Code   RO          602    .text               stm32f4xx_gpio.o
    0x08000da8   0x08000da8   0x0000065c   Code   RO          765    .text               stm32f4xx_rcc.o
    0x08001404   0x08001404   0x00000454   Code   RO          930    .text               stm32f4xx_usart.o
    0x08001858   0x08001858   0x00000104   Code   RO          970    .text               delay.o
    0x0800195c   0x0800195c   0x00000148   Code   RO         1010    .text               usart.o
    0x08001aa4   0x08001aa4   0x00000414   Code   RO         1041    .text               ad9959.o
    0x08001eb8   0x08001eb8   0x000002b0   Code   RO         1109    .text               matrix_kbd.o
    0x08002168   0x08002168   0x000006cc   Code   RO         1135    .text               key.o
    0x08002834   0x08002834   0x00000002   Code   RO         1158    .text               c_w.l(use_no_semi_2.o)
    0x08002836   0x08002836   0x00000002   PAD
    0x08002838   0x08002838   0x00000018   Code   RO         1162    .text               c_w.l(noretval__2printf.o)
    0x08002850   0x08002850   0x00000078   Code   RO         1166    .text               c_w.l(_printf_dec.o)
    0x080028c8   0x080028c8   0x00000160   Code   RO         1204    .text               c_w.l(__printf_ss_wp.o)
    0x08002a28   0x08002a28   0x00000006   Code   RO         1214    .text               c_w.l(heapauxi.o)
    0x08002a2e   0x08002a2e   0x00000002   Code   RO         1254    .text               c_w.l(use_no_semi.o)
    0x08002a30   0x08002a30   0x00000016   Code   RO         1257    .text               c_w.l(_rserrno.o)
    0x08002a46   0x08002a46   0x000000b2   Code   RO         1259    .text               c_w.l(_printf_intcommon.o)
    0x08002af8   0x08002af8   0x0000041e   Code   RO         1261    .text               c_w.l(_printf_fp_dec.o)
    0x08002f16   0x08002f16   0x00000002   PAD
    0x08002f18   0x08002f18   0x00000024   Code   RO         1263    .text               c_w.l(_printf_char_file.o)
    0x08002f3c   0x08002f3c   0x00000008   Code   RO         1293    .text               c_w.l(rt_locale_intlibspace.o)
    0x08002f44   0x08002f44   0x00000008   Code   RO         1298    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08002f4c   0x08002f4c   0x0000008a   Code   RO         1300    .text               c_w.l(lludiv10.o)
    0x08002fd6   0x08002fd6   0x00000002   PAD
    0x08002fd8   0x08002fd8   0x00000030   Code   RO         1302    .text               c_w.l(_printf_char_common.o)
    0x08003008   0x08003008   0x00000080   Code   RO         1304    .text               c_w.l(_printf_fp_infnan.o)
    0x08003088   0x08003088   0x000000e4   Code   RO         1306    .text               c_w.l(bigflt0.o)
    0x0800316c   0x0800316c   0x00000008   Code   RO         1331    .text               c_w.l(ferror.o)
    0x08003174   0x08003174   0x00000008   Code   RO         1344    .text               c_w.l(libspace.o)
    0x0800317c   0x0800317c   0x0000004a   Code   RO         1347    .text               c_w.l(sys_stackheap_outer.o)
    0x080031c6   0x080031c6   0x00000012   Code   RO         1351    .text               c_w.l(exit.o)
    0x080031d8   0x080031d8   0x00000080   Code   RO         1353    .text               c_w.l(strcmpv7m.o)
    0x08003258   0x08003258   0x0000003e   Code   RO         1309    CL$$btod_d2e        c_w.l(btod.o)
    0x08003296   0x08003296   0x00000046   Code   RO         1311    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080032dc   0x080032dc   0x00000060   Code   RO         1310    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800333c   0x0800333c   0x00000338   Code   RO         1319    CL$$btod_div_common  c_w.l(btod.o)
    0x08003674   0x08003674   0x000000dc   Code   RO         1316    CL$$btod_e2e        c_w.l(btod.o)
    0x08003750   0x08003750   0x0000002a   Code   RO         1313    CL$$btod_ediv       c_w.l(btod.o)
    0x0800377a   0x0800377a   0x0000002a   Code   RO         1312    CL$$btod_emul       c_w.l(btod.o)
    0x080037a4   0x080037a4   0x00000244   Code   RO         1318    CL$$btod_mult_common  c_w.l(btod.o)
    0x080039e8   0x080039e8   0x00000030   Code   RO         1342    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08003a18   0x08003a18   0x0000007a   Code   RO         1242    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08003a92   0x08003a92   0x0000000e   Code   RO         1199    i._is_digit         c_w.l(__printf_wp.o)
    0x08003aa0   0x08003aa0   0x0000002c   Code   RO         1336    locale$$code        c_w.l(lc_numeric_c.o)
    0x08003acc   0x08003acc   0x00000150   Code   RO         1218    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08003c1c   0x08003c1c   0x000002b0   Code   RO         1225    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08003ecc   0x08003ecc   0x0000005a   Code   RO         1228    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08003f26   0x08003f26   0x00000026   Code   RO         1232    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08003f4c   0x08003f4c   0x00000154   Code   RO         1238    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080040a0   0x080040a0   0x0000009c   Code   RO         1268    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800413c   0x0800413c   0x0000000c   Code   RO         1270    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08004148   0x08004148   0x00000016   Code   RO         1219    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800415e   0x0800415e   0x00000002   PAD
    0x08004160   0x08004160   0x00000198   Code   RO         1272    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x080042f8   0x080042f8   0x000001d4   Code   RO         1220    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x080044cc   0x080044cc   0x0000000a   Code   RO         1410    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080044d6   0x080044d6   0x00000004   Code   RO         1240    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080044da   0x080044da   0x00000000   Code   RO         1274    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080044da   0x080044da   0x00000002   PAD
    0x080044dc   0x080044dc   0x00000094   Data   RO         1307    .constdata          c_w.l(bigflt0.o)
    0x08004570   0x08004570   0x0000004a   Data   RO            4    .conststring        main.o
    0x080045ba   0x080045ba   0x00000002   PAD
    0x080045bc   0x080045bc   0x00000020   Data   RO         1483    Region$$Table       anon$$obj.o
    0x080045dc   0x080045dc   0x0000001c   Data   RO         1335    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080045f8, Size: 0x000007c8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080045f8   0x00000014   Data   RW          195    .data               system_stm32f4xx.o
    0x20000014   0x0800460c   0x00000010   Data   RW          766    .data               stm32f4xx_rcc.o
    0x20000024   0x0800461c   0x00000004   Data   RW          971    .data               delay.o
    0x20000028   0x08004620   0x00000006   Data   RW         1012    .data               usart.o
    0x2000002e   0x08004626   0x0000002b   Data   RW         1042    .data               ad9959.o
    0x20000059   0x08004651   0x00000003   PAD
    0x2000005c   0x08004654   0x0000000e   Data   RW         1111    .data               matrix_kbd.o
    0x2000006a   0x08004662   0x00000002   PAD
    0x2000006c   0x08004664   0x0000000c   Data   RW         1137    .data               key.o
    0x20000078        -       0x000000c8   Zero   RW         1011    .bss                usart.o
    0x20000140        -       0x00000010   Zero   RW         1110    .bss                matrix_kbd.o
    0x20000150        -       0x00000018   Zero   RW         1136    .bss                key.o
    0x20000168        -       0x00000060   Zero   RW         1345    .bss                c_w.l(libspace.o)
    0x200001c8        -       0x00000200   Zero   RW          219    HEAP                startup_stm32f40_41xxx.o
    0x200003c8        -       0x00000400   Zero   RW          218    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1044         68          0         43          0       4871   ad9959.o
       260          8          0          4          0       1485   delay.o
      1740        606          0         12         24       4521   key.o
      1404        578         74          0          0      35747   main.o
       688         54          0         14         16       3407   matrix_kbd.o
       224         20          0          0          0     231005   misc.o
        64         26        392          0       1536        840   startup_stm32f40_41xxx.o
       660         44          0          0          0       4161   stm32f4xx_gpio.o
        26          0          0          0          0       1226   stm32f4xx_it.o
      1628         52          0         16          0      13040   stm32f4xx_rcc.o
      1108         34          0          0          0       7888   stm32f4xx_usart.o
       528         46          0         20          0       1783   system_stm32f4xx.o
       328         16          0          6        200       3382   usart.o

    ----------------------------------------------------------------------
      9704       <USER>        <GROUP>        120       1776     313356   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          2          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       352          0          0          0          0         88   __printf_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_u.o
        22          0          0          0          0        100   _rserrno.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        492   daddsub_clz.o
       688        140          0          0          0        256   ddiv.o
        90          4          0          0          0        140   dfixu.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
      7532        <USER>        <GROUP>          0         96       4860   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4778        208        176          0         96       2776   c_w.l
      2572        232          0          0          0       1812   fz_wm.l
       170          0          0          0          0        272   m_wm.l

    ----------------------------------------------------------------------
      7532        <USER>        <GROUP>          0         96       4860   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17236       1992        676        120       1872     312508   Grand Totals
     17236       1992        676        120       1872     312508   ELF Image Totals
     17236       1992        676        120          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17912 (  17.49kB)
    Total RW  Size (RW Data + ZI Data)              1992 (   1.95kB)
    Total ROM Size (Code + RO Data + RW Data)      18032 (  17.61kB)

==============================================================================

