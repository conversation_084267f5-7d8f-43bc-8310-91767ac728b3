#ifndef __AD9959_H
#define __AD9959_H

// 包含 main.h，它会自动引入所有必要的 HAL 库文件和 CubeMX 生成的引脚定义
#include "main.h"

// 为了与您的原始代码兼容，保留了这些类型定义
// 建议在未来的代码中使用 <stdint.h> 中的标准类型，如 uint8_t, uint16_t, uint32_t
#define uchar uint8_t
#define uint  uint16_t
#define ulong uint32_t

/* 函数原型 */
void IO_Update(void);
void WriteData_AD9959(uchar RegisterAddress, uchar NumberofRegisters, uchar *RegisterData);
void Write_frequence(uchar Channel, ulong Freq);
void Write_Amplitude(uchar Channel, uint Ampli);
void Write_Phase(uchar Channel, uint Phase);
void Init_AD9959(void);
void IntReset(void);


#endif
