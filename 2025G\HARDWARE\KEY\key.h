#ifndef __KEY_H
#define __KEY_H

#include "stm32f4xx.h"
#include "matrix_kbd.h"
#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= 按键控制模块配置 ======= */
#define KEY_INPUT_BUFFER_SIZE    10    /* 输入缓冲区大小 */

/* ======= 模式定义 ======= */
typedef enum {
    MODE_FREQUENCY = 1,         /* 模式1：频率设置 */
    MODE_FREQ_AMPLITUDE = 2,    /* 模式2：频率幅度设置 */
    MODE_RESERVED_3 = 3,        /* 模式3：待定 */
    MODE_RESERVED_4 = 4         /* 模式4：待定 */
} KeyMode_t;

/* ======= 模式2步骤定义 ======= */
typedef enum {
    MODE2_STEP_FREQUENCY = 0,   /* 模式2步骤：设置频率 */
    MODE2_STEP_AMPLITUDE = 1    /* 模式2步骤：设置幅度 */
} Mode2Step_t;

/* ======= 按键处理状态结构体 ======= */
typedef struct {
    KeyMode_t currentMode;              /* 当前模式 */
    uint8_t inputBuffer[KEY_INPUT_BUFFER_SIZE];  /* 输入缓冲区 */
    uint8_t inputIndex;                 /* 输入索引 */
    
    /* 模式2专用变量 */
    Mode2Step_t mode2_step;             /* 模式2当前步骤 */
    uint32_t mode2_freq;                /* 模式2保存的频率值 */
    uint16_t mode2_amplitude;           /* 模式2幅度值（单位：0.1V） */
} KeyState_t;

/* ======= 回调函数类型定义 ======= */
typedef void (*FrequencyOutputCallback_t)(uint32_t freq);
typedef void (*FrequencyAmplitudeOutputCallback_t)(uint32_t freq, uint16_t amplitude);

/* ======= API函数声明 ======= */
void Key_Init(void);
void Key_SetFrequencyOutputCallback(FrequencyOutputCallback_t callback);
void Key_SetFrequencyAmplitudeOutputCallback(FrequencyAmplitudeOutputCallback_t callback);
void Key_ProcessInput(void);
KeyMode_t Key_GetCurrentMode(void);
uint8_t Key_IsOutputEnabled(void);

/* ======= 内部函数声明（供测试使用） ======= */
void Key_ProcessKeyInput(uint8_t key);
void Key_HandleFrequencyMode(uint8_t key);
void Key_HandleFrequencyAmplitudeMode(uint8_t key);
void Key_SwitchMode(void);
void Key_ClearInputBuffer(void);
uint32_t Key_BufferToFrequency(void);
uint16_t Key_BufferToAmplitude(void);
uint8_t Key_ValidateFrequency(uint32_t freq);

#ifdef __cplusplus
}
#endif

#endif /* __KEY_H */
