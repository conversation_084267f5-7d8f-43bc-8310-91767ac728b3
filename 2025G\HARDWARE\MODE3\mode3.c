#include "mode3.h"

/* ======= 全局变量定义 ======= */
volatile AppState g_app_state = STATE_IDLE;
FilterType g_filter_type = FILTER_TYPE_UNKNOWN;
uint32_t g_sweep_current_step = 0;
uint32_t g_current_freq_sweep = SWEEP_START_FREQ_HZ;
volatile uint8_t stop_flag = 0;

/* ======= 模式3初始化 ======= */
void Mode3_Init(void)
{
    printf("Initializing Mode 3 - Filter Learning and Simulation...\r\n");

    // 初始化状态
    g_app_state = STATE_IDLE;
    g_filter_type = FILTER_TYPE_UNKNOWN;
    g_sweep_current_step = 0;
    g_current_freq_sweep = SWEEP_START_FREQ_HZ;
    stop_flag = 0;

    // 初始化AD9959
    Write_frequence(0, 1000);      // 设置通道0为1000Hz
    Write_Phase(0, 0);             // 相位0
    Write_Amplitude(0, 1023);      // 最大幅度

    // 设置CH3默认输出
    Write_frequence(3, 100000);    // 设置通道3为100kHz
    Write_Phase(3, 0);             // 相位0
    Write_Amplitude(3, 0);         // 初始幅度为0

    IO_Update();                   // 更新输出

    printf("Mode 3 initialization complete.\r\n");
    printf("Ready for filter learning and simulation.\r\n");
}

/* ======= 开始学习模式 ======= */
void Mode3_Start_Learn(void)
{
    printf("Starting filter learning process...\r\n");

    // 重置学习参数
    g_sweep_current_step = 0;
    g_current_freq_sweep = SWEEP_START_FREQ_HZ;
    g_filter_type = FILTER_TYPE_UNKNOWN;
    stop_flag = 0;

    // 显示学习状态
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Learning Filter...", BLACK);

    // 简单的频率扫描演示
    for (uint32_t freq = SWEEP_START_FREQ_HZ; freq <= SWEEP_END_FREQ_HZ; freq += SWEEP_STEP_FREQ_HZ) {
        printf("Testing frequency: %lu Hz\r\n", freq);
        Write_frequence(0, freq);
        IO_Update();
        delay_ms(SETTLE_TIME_MS);
        g_sweep_current_step++;
    }

    // 模拟滤波器类型识别
    g_filter_type = FILTER_TYPE_LOW_PASS;  // 演示：假设识别为低通滤波器

    g_app_state = STATE_DISPLAY_RESULTS;
    printf("Filter learning completed.\r\n");
}

/* ======= 开始仿真模式 ======= */
void Mode3_Start_Simulation(void)
{
    // 检查是否已学习滤波器
    if (g_filter_type == FILTER_TYPE_UNKNOWN) {
        lcd_clear(RED);
        Mode3_LCD_ShowString_Simplified(10, 10, "Error!", WHITE);
        Mode3_LCD_ShowString_Simplified(10, 30, "Learn filter first.", WHITE);
        delay_ms(2000);
        g_app_state = STATE_IDLE;
        lcd_clear(WHITE);
        Mode3_LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
        Mode3_LCD_ShowString_Simplified(10, 30, "Press Learn Key.", BLACK);
        return;
    }

    printf("Starting filter simulation...\r\n");

    // 显示仿真状态
    lcd_clear(BLUE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Simulation Active", WHITE);
    Mode3_LCD_ShowString_Simplified(10, 30, "Press Stop Key", WHITE);

    // 设置AD9959输出一个测试频率
    Write_frequence(3, 50000);  // 50kHz测试频率
    Write_Amplitude(3, 500);    // 中等幅度
    IO_Update();

    g_app_state = STATE_SIMULATING;
    printf("Filter simulation started.\r\n");
}

/* ======= 停止仿真模式 ======= */
void Mode3_Stop_Simulation(void)
{
    printf("Stopping filter simulation...\r\n");

    // 停止频率测量
    TIM_Stop_Frequency_Measurement();

    // 停止DAC和ADC DMA
    DAC_Stop_Output();
    ADC_Stop_Sampling();

    // 更新状态和显示
    g_app_state = STATE_IDLE;
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
    Mode3_LCD_ShowString_Simplified(10, 30, "Press Learn/Sim Key", BLACK);

    printf("Filter simulation stopped.\r\n");
}

/* ======= 状态机处理 ======= */
void Mode3_Process_State_Machine(void)
{
    switch (g_app_state) {
        case STATE_IDLE:
            // 空闲状态，等待按键输入
            break;

        case STATE_SWEEPING:
            // 频率扫描状态 - 在Mode3_Start_Learn中已完成
            break;

        case STATE_ANALYZING:
            // 分析状态 - 简化处理
            g_app_state = STATE_DISPLAY_RESULTS;
            break;

        case STATE_DISPLAY_RESULTS:
            Mode3_Display_Results();
            // 等待用户按键返回空闲状态
            break;

        case STATE_SIMULATING:
            // 仿真状态，简单演示
            break;

        default:
            g_app_state = STATE_IDLE;
            break;
    }
}



/* ======= 显示结果 ======= */
void Mode3_Display_Results(void)
{
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Analysis Complete!", BLACK);

    char* type_str = "Type: Unknown";
    switch (g_filter_type) {
        case FILTER_TYPE_LOW_PASS:  type_str = "Type: Low-Pass";  break;
        case FILTER_TYPE_HIGH_PASS: type_str = "Type: High-Pass"; break;
        case FILTER_TYPE_BAND_PASS: type_str = "Type: Band-Pass"; break;
        case FILTER_TYPE_BAND_STOP: type_str = "Type: Band-Stop"; break;
        default: break;
    }
    Mode3_LCD_ShowString_Simplified(10, 30, type_str, BLACK);
    Mode3_LCD_ShowString_Simplified(10, 50, "Press key to return", BLACK);

    printf("Results displayed: %s\r\n", type_str);
}

/* ======= LCD显示简化函数 ======= */
void Mode3_LCD_ShowString_Simplified(uint16_t x, uint16_t y, const char *p, uint16_t color)
{
    // 使用16号字体，最大宽度240
    uint8_t font_size = 16;
    lcd_show_string(x, y, 240, font_size, font_size, (char*)p, color);
}
