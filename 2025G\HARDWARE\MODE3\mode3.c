#include "mode3.h"

/* ======= 全局变量定义 ======= */
volatile AppState g_app_state = STATE_IDLE;
FilterType g_filter_type = FILTER_TYPE_UNKNOWN;
uint32_t g_sweep_current_step = 0;
uint32_t g_current_freq_sweep = SWEEP_START_FREQ_HZ;
volatile uint8_t stop_flag = 0;

/* ======= 模式3初始化 ======= */
void Mode3_Init(void)
{
    printf("Initializing Mode 3 - Filter Learning and Simulation...\r\n");
    
    // 初始化硬件模块
    ADC_Mode3_Init();
    DAC_Mode3_Init();
    TIM_Mode3_Init();
    DFT_Init();
    
    // 初始化状态
    g_app_state = STATE_IDLE;
    g_filter_type = FILTER_TYPE_UNKNOWN;
    g_sweep_current_step = 0;
    g_current_freq_sweep = SWEEP_START_FREQ_HZ;
    stop_flag = 0;
    
    // 清空滤波器响应数组
    memset(g_H_magnitude, 0, sizeof(g_H_magnitude));
    memset(g_H_phase, 0, sizeof(g_H_phase));
    
    // 初始化AD9959
    Write_frequence(0, 1000);      // 设置通道0为1000Hz
    Write_Phase(0, 0);             // 相位0
    Write_Amplitude(0, 1023);      // 最大幅度
    
    // 设置CH3默认输出
    Write_frequence(3, 100000);    // 设置通道3为100kHz
    Write_Phase(3, 0);             // 相位0
    Write_Amplitude(3, 0);         // 初始幅度为0
    
    IO_Update();                   // 更新输出
    
    printf("Mode 3 initialization complete.\r\n");
    printf("Ready for filter learning and simulation.\r\n");
}

/* ======= 开始学习模式 ======= */
void Mode3_Start_Learn(void)
{
    printf("Starting filter learning process...\r\n");
    
    // 重置学习参数
    g_sweep_current_step = 0;
    g_current_freq_sweep = SWEEP_START_FREQ_HZ;
    g_filter_type = FILTER_TYPE_UNKNOWN;
    stop_flag = 0;
    
    // 设置继电器到学习状态（如果有的话）
    // HAL_GPIO_WritePin(RELAY_GPIO_Port, RELAY_Pin, RELAY_STATE_LEARN);
    
    // 开始ADC采样
    ADC_Start_Sampling();
    
    // 设置初始采样频率
    sampling_frequency_sweep = g_current_freq_sweep * 20;
    TIM_Start_Sampling_Timer((uint32_t)sampling_frequency_sweep);
    
    // 设置AD9959输出
    Write_frequence(0, g_current_freq_sweep);
    Write_frequence(3, (uint32_t)sampling_frequency_sweep);
    Write_Amplitude(3, 1000);
    IO_Update();
    
    delay_ms(SETTLE_TIME_MS);
    
    // 显示学习状态
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Learning Filter...", BLACK);
    
    g_adc1_data_ready = 0;
    g_adc2_data_ready = 0;
    
    g_app_state = STATE_SWEEPING;
    printf("Filter learning started.\r\n");
}

/* ======= 开始仿真模式 ======= */
void Mode3_Start_Simulation(void)
{
    // 检查是否已学习滤波器
    if (g_filter_type == FILTER_TYPE_UNKNOWN) {
        lcd_clear(RED);
        Mode3_LCD_ShowString_Simplified(10, 10, "Error!", WHITE);
        Mode3_LCD_ShowString_Simplified(10, 30, "Learn filter first.", WHITE);
        delay_ms(2000);
        g_app_state = STATE_IDLE;
        lcd_clear(WHITE);
        Mode3_LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
        Mode3_LCD_ShowString_Simplified(10, 30, "Press Learn Key.", BLACK);
        return;
    }

    printf("Starting filter simulation...\r\n");

    // 设置继电器到仿真状态（如果有的话）
    // HAL_GPIO_WritePin(RELAY_GPIO_Port, RELAY_Pin, RELAY_STATE_SIMULATE);
    delay_ms(20);

    // 显示仿真状态
    lcd_clear(BLUE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Simulation Active", WHITE);
    Mode3_LCD_ShowString_Simplified(10, 30, "Press Stop Key", WHITE);

    // 开始频率测量
    TIM_Start_Frequency_Measurement();

    // 启动DAC DMA
    DAC_Start_Output();

    // 启动ADC2采样（需要修正变量名）
    // if (HAL_ADC_Start_DMA(&hadc2_mode3, (uint32_t*)g_adc2_dma_buffer, ADC_TOTAL_BUFFER_SIZE) != HAL_OK) {
    //     printf("ADC2 start failed in simulation mode!\r\n");
    //     return;
    // }

    // 设置AD9959采样频率输出
    Write_frequence(3, (uint32_t)g_current_sampling_simulation);
    IO_Update();

    g_app_state = STATE_SIMULATING;
    printf("Filter simulation started.\r\n");
}

/* ======= 停止仿真模式 ======= */
void Mode3_Stop_Simulation(void)
{
    printf("Stopping filter simulation...\r\n");

    // 停止频率测量
    TIM_Stop_Frequency_Measurement();

    // 停止DAC和ADC DMA
    DAC_Stop_Output();
    ADC_Stop_Sampling();

    // 更新状态和显示
    g_app_state = STATE_IDLE;
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "System Ready.", BLACK);
    Mode3_LCD_ShowString_Simplified(10, 30, "Press Learn/Sim Key", BLACK);

    printf("Filter simulation stopped.\r\n");
}

/* ======= 状态机处理 ======= */
void Mode3_Process_State_Machine(void)
{
    switch (g_app_state) {
        case STATE_IDLE:
            // 空闲状态，等待按键输入
            break;

        case STATE_SWEEPING:
            // 频率扫描状态
            if (g_adc1_data_ready && g_adc2_data_ready) {
                g_adc1_data_ready = 0;
                g_adc2_data_ready = 0;

                float input_amp = 0.0f, input_phase = 0.0f;
                float output_amp = 0.0f, output_phase = 0.0f;

                // 计算输入和输出信号的幅度和相位
                Get_Signal_Accurate_Amplitude_And_Phase(g_adc1_processing_buffer,
                                                        g_current_freq_sweep,
                                                        sampling_frequency_sweep,
                                                        &input_amp, &input_phase);
                Get_Signal_Accurate_Amplitude_And_Phase(g_adc2_processing_buffer,
                                                        g_current_freq_sweep,
                                                        sampling_frequency_sweep,
                                                        &output_amp, &output_phase);

                // 计算并存储 H(w)
                if (output_amp / input_amp < 10.0f && output_amp / input_amp > 0.0f) {
                    g_H_magnitude[g_sweep_current_step] = output_amp / input_amp;
                } else {
                    g_H_magnitude[g_sweep_current_step] = 0.0f;
                }

                if (input_amp == 0.0f) {
                    stop_flag = 1;
                }

                // 计算相位差并归一化到 [-PI, PI] 区间
                float phase_diff = output_phase - input_phase;
                while (phase_diff > M_PI) {
                    phase_diff -= 2.0f * M_PI;
                }
                while (phase_diff <= -M_PI) {
                    phase_diff += 2.0f * M_PI;
                }
                g_H_phase[g_sweep_current_step] = phase_diff;

                g_sweep_current_step++;

                if (g_sweep_current_step < NUM_SWEEP_STEPS && !stop_flag) {
                    // 继续下一个频率点
                    g_current_freq_sweep += SWEEP_STEP_FREQ_HZ;
                    Write_Amplitude(3, 0);
                    ADC_Stop_Sampling();

                    Write_frequence(0, g_current_freq_sweep);

                    // 根据频率调整采样频率
                    if (g_current_freq_sweep < 50000) {
                        sampling_frequency_sweep = g_current_freq_sweep * 20;
                    } else if (g_current_freq_sweep < 200000) {
                        sampling_frequency_sweep = g_current_freq_sweep * 5;
                    } else if (g_current_freq_sweep < 466666) {
                        sampling_frequency_sweep = g_current_freq_sweep * 3;
                    } else {
                        sampling_frequency_sweep = g_current_freq_sweep * 2;
                    }

                    TIM_Start_Sampling_Timer((uint32_t)sampling_frequency_sweep);
                    ADC_Start_Sampling();

                    Write_frequence(3, (uint32_t)sampling_frequency_sweep);
                    Write_Amplitude(3, 1000);
                    IO_Update();

                    delay_ms(SETTLE_TIME_MS);
                } else {
                    // 扫描完成
                    Write_Amplitude(3, 0);
                    IO_Update();
                    ADC_Stop_Sampling();
                    TIM_Stop_Sampling_Timer();
                    g_app_state = STATE_ANALYZING;
                }
            }
            break;

        case STATE_ANALYZING:
            Mode3_Analyze_Filter_Response();
            g_app_state = STATE_DISPLAY_RESULTS;
            break;

        case STATE_DISPLAY_RESULTS:
            Mode3_Display_Results();
            // 等待用户按键返回空闲状态
            break;

        case STATE_SIMULATING:
            // 仿真状态，由中断处理
            break;

        default:
            g_app_state = STATE_IDLE;
            break;
    }
}

/* ======= 分析滤波器响应 ======= */
void Mode3_Analyze_Filter_Response(void)
{
    float max_gain = 0.0f;
    int max_gain_index = 0;

    // 找到最大增益
    for (int i = 0; i < NUM_SWEEP_STEPS; i++) {
        if (g_H_magnitude[i] > max_gain) {
            max_gain = g_H_magnitude[i];
            max_gain_index = i;
        }
    }

    if (max_gain < 0.01) {
        g_filter_type = FILTER_TYPE_UNKNOWN;
        return;
    }

    float cutoff_gain = max_gain * 0.707f;
    float start_gain = g_H_magnitude[0];
    float end_gain = g_H_magnitude[NUM_SWEEP_STEPS - 1];

    // 判断滤波器类型
    if (max_gain_index < NUM_SWEEP_STEPS * 0.1 && end_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_LOW_PASS;
    } else if (max_gain_index > NUM_SWEEP_STEPS * 0.9 && start_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_HIGH_PASS;
    } else if (start_gain < cutoff_gain && end_gain < cutoff_gain) {
        g_filter_type = FILTER_TYPE_BAND_PASS;
    } else if (start_gain > cutoff_gain && end_gain > cutoff_gain) {
        g_filter_type = FILTER_TYPE_BAND_STOP;
    } else {
        g_filter_type = FILTER_TYPE_UNKNOWN;
    }

    printf("Filter analysis complete. Type: %d\r\n", g_filter_type);
}

/* ======= 显示结果 ======= */
void Mode3_Display_Results(void)
{
    lcd_clear(WHITE);
    Mode3_LCD_ShowString_Simplified(10, 10, "Analysis Complete!", BLACK);

    char* type_str = "Type: Unknown";
    switch (g_filter_type) {
        case FILTER_TYPE_LOW_PASS:  type_str = "Type: Low-Pass";  break;
        case FILTER_TYPE_HIGH_PASS: type_str = "Type: High-Pass"; break;
        case FILTER_TYPE_BAND_PASS: type_str = "Type: Band-Pass"; break;
        case FILTER_TYPE_BAND_STOP: type_str = "Type: Band-Stop"; break;
        default: break;
    }
    Mode3_LCD_ShowString_Simplified(10, 30, type_str, BLACK);
    Mode3_LCD_ShowString_Simplified(10, 50, "Press key to return", BLACK);

    printf("Results displayed: %s\r\n", type_str);
}

/* ======= LCD显示简化函数 ======= */
void Mode3_LCD_ShowString_Simplified(uint16_t x, uint16_t y, const char *p, uint16_t color)
{
    // 使用16号字体，最大宽度240
    uint8_t font_size = 16;
    lcd_show_string(x, y, 240, font_size, font_size, (char*)p, color);
}
