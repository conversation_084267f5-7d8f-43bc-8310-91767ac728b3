#ifndef __MODE3_H
#define __MODE3_H

#include "stm32f4xx.h"
#include "stdio.h"
#include "lcd.h"
#include "AD9959.h"
#include "delay.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= 模式3配置参数 ======= */
#define SWEEP_START_FREQ_HZ         1000
#define SWEEP_END_FREQ_HZ           100000
#define SWEEP_STEP_FREQ_HZ          1000
#define NUM_SWEEP_STEPS             ((SWEEP_END_FREQ_HZ - SWEEP_START_FREQ_HZ) / SWEEP_STEP_FREQ_HZ + 1)
#define SETTLE_TIME_MS              100

/* ======= 应用状态枚举 ======= */
typedef enum {
    STATE_IDLE,
    STATE_SWEEPING,
    STATE_ANALYZING,
    STATE_DISPLAY_RESULTS,
    STATE_SIMULATING
} AppState;

/* ======= 滤波器类型枚举 ======= */
typedef enum {
    FILTER_TYPE_UNKNOWN,
    FILTER_TYPE_LOW_PASS,
    FILTER_TYPE_HIGH_PASS,
    FILTER_TYPE_BAND_PASS,
    FILTER_TYPE_BAND_STOP
} FilterType;

/* ======= 外部变量声明 ======= */
extern volatile AppState g_app_state;
extern FilterType g_filter_type;
extern uint32_t g_sweep_current_step;
extern uint32_t g_current_freq_sweep;
extern volatile uint8_t stop_flag;

/* ======= 函数声明 ======= */
void Mode3_Init(void);
void Mode3_Start_Learn(void);
void Mode3_Start_Simulation(void);
void Mode3_Stop_Simulation(void);
void Mode3_Process_State_Machine(void);
void Mode3_Display_Results(void);
void Mode3_LCD_ShowString_Simplified(uint16_t x, uint16_t y, const char *p, uint16_t color);

#ifdef __cplusplus
}
#endif

#endif /* __MODE3_H */
