/**
 ****************************************************************************************************
 * @file        lcd_ex.c
 * <AUTHOR>
 * @version     V1.2
 * @date        2024-12-14
 * @brief       lcd_ex.c��Ÿ���LCD����IC�ļĴ�����ʼ�����ִ���,�Լ�lcd.c,��.c�ļ�
 *              ��ֱ�Ӽ��뵽��������,ֻ��lcd.c���õ�,����ͨ��include����ʽ����.(��Ҫ��
 *              �����ļ��ٰ�����.c�ļ�!!����ᱨ��!) 
 * 
 * @license     Copyright (c) 2020-2032, �������������ӿƼ����޹�˾
 ****************************************************************************************************
 * @attention
 *
 * ʵ��ƽ̨:����ԭ�� STM32F407������
 * ������Ƶ:www.yuanzige.com
 * ������̳:www.openedv.com
 * ��˾��ַ:www.alientek.com
 * �����ַ:openedv.taobao.com
 *
 * �޸�˵��
 * V1.0 20200323
 * ��һ�η���
 * V1.1 20230529
 * 1��������ST7796��ILI9806 IC֧��
 * V1.2 20241214
 * 1����NT35510 IPS��Ļ����֧��
 ****************************************************************************************************
 */
 
#include "main.h"
#include "lcd.h"


/**
 * @brief       ST7789 �Ĵ�����ʼ������
 * @param       ��
 * @retval      ��
 */
void lcd_ex_st7789_reginit(void)
{
    lcd_wr_regno(0x11);

    HAL_Delay(120); 

    lcd_wr_regno(0x36);
    lcd_wr_data(0x00);

    lcd_wr_regno(0x3A);
    lcd_wr_data(0X05);

    lcd_wr_regno(0xB2);
    lcd_wr_data(0x0C);
    lcd_wr_data(0x0C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x33);
    lcd_wr_data(0x33);

    lcd_wr_regno(0xB7);
    lcd_wr_data(0x35);

    lcd_wr_regno(0xBB); /* vcom */
    lcd_wr_data(0x32);  /* 30 */

    lcd_wr_regno(0xC0);
    lcd_wr_data(0x0C);

    lcd_wr_regno(0xC2);
    lcd_wr_data(0x01);

    lcd_wr_regno(0xC3); /* vrh */
    lcd_wr_data(0x10);  /* 17 0D */

    lcd_wr_regno(0xC4); /* vdv */
    lcd_wr_data(0x20);  /* 20 */

    lcd_wr_regno(0xC6);
    lcd_wr_data(0x0f);

    lcd_wr_regno(0xD0);
    lcd_wr_data(0xA4); 
    lcd_wr_data(0xA1); 

    lcd_wr_regno(0xE0); /* Set Gamma  */
    lcd_wr_data(0xd0);
    lcd_wr_data(0x00);
    lcd_wr_data(0x02);
    lcd_wr_data(0x07);
    lcd_wr_data(0x0a);
    lcd_wr_data(0x28);
    lcd_wr_data(0x32);
    lcd_wr_data(0X44);
    lcd_wr_data(0x42);
    lcd_wr_data(0x06);
    lcd_wr_data(0x0e);
    lcd_wr_data(0x12);
    lcd_wr_data(0x14);
    lcd_wr_data(0x17);

    lcd_wr_regno(0XE1); /* Set Gamma */
    lcd_wr_data(0xd0);
    lcd_wr_data(0x00);
    lcd_wr_data(0x02);
    lcd_wr_data(0x07);
    lcd_wr_data(0x0a);
    lcd_wr_data(0x28);
    lcd_wr_data(0x31);
    lcd_wr_data(0x54);
    lcd_wr_data(0x47);
    lcd_wr_data(0x0e);
    lcd_wr_data(0x1c);
    lcd_wr_data(0x17);
    lcd_wr_data(0x1b); 
    lcd_wr_data(0x1e);

    lcd_wr_regno(0x2A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0xef);

    lcd_wr_regno(0x2B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0x3f);

    lcd_wr_regno(0x29); /* display on */
}

/**
 * @brief       ILI9341�Ĵ�����ʼ������
 * @param       ��
 * @retval      ��
 */
void lcd_ex_ili9341_reginit(void)
{
    lcd_wr_regno(0xCF);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC1);
    lcd_wr_data(0X30);
    lcd_wr_regno(0xED);
    lcd_wr_data(0x64);
    lcd_wr_data(0x03);
    lcd_wr_data(0X12);
    lcd_wr_data(0X81);
    lcd_wr_regno(0xE8);
    lcd_wr_data(0x85);
    lcd_wr_data(0x10);
    lcd_wr_data(0x7A);
    lcd_wr_regno(0xCB);
    lcd_wr_data(0x39);
    lcd_wr_data(0x2C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x34);
    lcd_wr_data(0x02);
    lcd_wr_regno(0xF7);
    lcd_wr_data(0x20);
    lcd_wr_regno(0xEA);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_regno(0xC0); /* Power control */
    lcd_wr_data(0x1B);  /* VRH[5:0] */
    lcd_wr_regno(0xC1); /* Power control */
    lcd_wr_data(0x01);  /* SAP[2:0];BT[3:0] */
    lcd_wr_regno(0xC5); /* VCM control */
    lcd_wr_data(0x30);  /* 3F */
    lcd_wr_data(0x30);  /* 3C */
    lcd_wr_regno(0xC7); /* VCM control2 */
    lcd_wr_data(0XB7);
    lcd_wr_regno(0x36); /* Memory Access Control */
    lcd_wr_data(0x48);
    lcd_wr_regno(0x3A);
    lcd_wr_data(0x55);
    lcd_wr_regno(0xB1);
    lcd_wr_data(0x00);
    lcd_wr_data(0x1A);
    lcd_wr_regno(0xB6); /* Display Function Control */
    lcd_wr_data(0x0A);
    lcd_wr_data(0xA2);
    lcd_wr_regno(0xF2); /* 3Gamma Function Disable */
    lcd_wr_data(0x00);
    lcd_wr_regno(0x26); /* Gamma curve selected */
    lcd_wr_data(0x01);
    lcd_wr_regno(0xE0); /* Set Gamma */
    lcd_wr_data(0x0F);
    lcd_wr_data(0x2A);
    lcd_wr_data(0x28);
    lcd_wr_data(0x08);
    lcd_wr_data(0x0E);
    lcd_wr_data(0x08);
    lcd_wr_data(0x54);
    lcd_wr_data(0XA9);
    lcd_wr_data(0x43);
    lcd_wr_data(0x0A);
    lcd_wr_data(0x0F);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_regno(0XE1); /* Set Gamma */
    lcd_wr_data(0x00);
    lcd_wr_data(0x15);
    lcd_wr_data(0x17);
    lcd_wr_data(0x07);
    lcd_wr_data(0x11);
    lcd_wr_data(0x06);
    lcd_wr_data(0x2B);
    lcd_wr_data(0x56);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x05);
    lcd_wr_data(0x10);
    lcd_wr_data(0x0F);
    lcd_wr_data(0x3F);
    lcd_wr_data(0x3F);
    lcd_wr_data(0x0F);
    lcd_wr_regno(0x2B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0x3f);
    lcd_wr_regno(0x2A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0xef);
    lcd_wr_regno(0x11); /* Exit Sleep */
    HAL_Delay(120);
    lcd_wr_regno(0x29); /* display on */
 }
 

/**
 * @brief       NT35310�Ĵ�����ʼ������ 
 * @param       ��
 * @retval      ��
 */
void lcd_ex_nt35310_reginit(void)
{
    lcd_wr_regno(0xED);
    lcd_wr_data(0x01);
    lcd_wr_data(0xFE);

    lcd_wr_regno(0xEE);
    lcd_wr_data(0xDE);
    lcd_wr_data(0x21);

    lcd_wr_regno(0xF1);
    lcd_wr_data(0x01);
    lcd_wr_regno(0xDF);
    lcd_wr_data(0x10);

    /* VCOMvoltage */
    lcd_wr_regno(0xC4);
    lcd_wr_data(0x8F);  /* 5f */

    lcd_wr_regno(0xC6);
    lcd_wr_data(0x00);
    lcd_wr_data(0xE2);
    lcd_wr_data(0xE2);
    lcd_wr_data(0xE2);
    lcd_wr_regno(0xBF);
    lcd_wr_data(0xAA);

    lcd_wr_regno(0xB0);
    lcd_wr_data(0x0D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x0D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x11);
    lcd_wr_data(0x00);
    lcd_wr_data(0x19);
    lcd_wr_data(0x00);
    lcd_wr_data(0x21);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x5D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x5D);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB1);
    lcd_wr_data(0x80);
    lcd_wr_data(0x00);
    lcd_wr_data(0x8B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x96);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB2);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x02);
    lcd_wr_data(0x00);
    lcd_wr_data(0x03);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB3);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB4);
    lcd_wr_data(0x8B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x96);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA1);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB5);
    lcd_wr_data(0x02);
    lcd_wr_data(0x00);
    lcd_wr_data(0x03);
    lcd_wr_data(0x00);
    lcd_wr_data(0x04);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB6);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB7);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3F);
    lcd_wr_data(0x00);
    lcd_wr_data(0x5E);
    lcd_wr_data(0x00);
    lcd_wr_data(0x64);
    lcd_wr_data(0x00);
    lcd_wr_data(0x8C);
    lcd_wr_data(0x00);
    lcd_wr_data(0xAC);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDC);
    lcd_wr_data(0x00);
    lcd_wr_data(0x70);
    lcd_wr_data(0x00);
    lcd_wr_data(0x90);
    lcd_wr_data(0x00);
    lcd_wr_data(0xEB);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDC);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xB8);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xBA);
    lcd_wr_data(0x24);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC1);
    lcd_wr_data(0x20);
    lcd_wr_data(0x00);
    lcd_wr_data(0x54);
    lcd_wr_data(0x00);
    lcd_wr_data(0xFF);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC2);
    lcd_wr_data(0x0A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x04);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC3);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x39);
    lcd_wr_data(0x00);
    lcd_wr_data(0x37);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x36);
    lcd_wr_data(0x00);
    lcd_wr_data(0x32);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2F);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x29);
    lcd_wr_data(0x00);
    lcd_wr_data(0x26);
    lcd_wr_data(0x00);
    lcd_wr_data(0x24);
    lcd_wr_data(0x00);
    lcd_wr_data(0x24);
    lcd_wr_data(0x00);
    lcd_wr_data(0x23);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x36);
    lcd_wr_data(0x00);
    lcd_wr_data(0x32);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2F);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x29);
    lcd_wr_data(0x00);
    lcd_wr_data(0x26);
    lcd_wr_data(0x00);
    lcd_wr_data(0x24);
    lcd_wr_data(0x00);
    lcd_wr_data(0x24);
    lcd_wr_data(0x00);
    lcd_wr_data(0x23);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC4);
    lcd_wr_data(0x62);
    lcd_wr_data(0x00);
    lcd_wr_data(0x05);
    lcd_wr_data(0x00);
    lcd_wr_data(0x84);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF0);
    lcd_wr_data(0x00);
    lcd_wr_data(0x18);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA4);
    lcd_wr_data(0x00);
    lcd_wr_data(0x18);
    lcd_wr_data(0x00);
    lcd_wr_data(0x50);
    lcd_wr_data(0x00);
    lcd_wr_data(0x0C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x17);
    lcd_wr_data(0x00);
    lcd_wr_data(0x95);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);
    lcd_wr_data(0xE6);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC5);
    lcd_wr_data(0x32);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x65);
    lcd_wr_data(0x00);
    lcd_wr_data(0x76);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC6);
    lcd_wr_data(0x20);
    lcd_wr_data(0x00);
    lcd_wr_data(0x17);
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC7);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC8);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xC9);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE0);
    lcd_wr_data(0x16);
    lcd_wr_data(0x00);
    lcd_wr_data(0x1C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x21);
    lcd_wr_data(0x00);
    lcd_wr_data(0x36);
    lcd_wr_data(0x00);
    lcd_wr_data(0x46);
    lcd_wr_data(0x00);
    lcd_wr_data(0x52);
    lcd_wr_data(0x00);
    lcd_wr_data(0x64);
    lcd_wr_data(0x00);
    lcd_wr_data(0x7A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x8B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA8);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB9);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC4);
    lcd_wr_data(0x00);
    lcd_wr_data(0xCA);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD9);
    lcd_wr_data(0x00);
    lcd_wr_data(0xE0);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE1);
    lcd_wr_data(0x16);
    lcd_wr_data(0x00);
    lcd_wr_data(0x1C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x22);
    lcd_wr_data(0x00);
    lcd_wr_data(0x36);
    lcd_wr_data(0x00);
    lcd_wr_data(0x45);
    lcd_wr_data(0x00);
    lcd_wr_data(0x52);
    lcd_wr_data(0x00);
    lcd_wr_data(0x64);
    lcd_wr_data(0x00);
    lcd_wr_data(0x7A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x8B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA8);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB9);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC4);
    lcd_wr_data(0x00);
    lcd_wr_data(0xCA);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD8);
    lcd_wr_data(0x00);
    lcd_wr_data(0xE0);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE2);
    lcd_wr_data(0x05);
    lcd_wr_data(0x00);
    lcd_wr_data(0x0B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x1B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x34);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x4F);
    lcd_wr_data(0x00);
    lcd_wr_data(0x61);
    lcd_wr_data(0x00);
    lcd_wr_data(0x79);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);
    lcd_wr_data(0x97);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA6);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB7);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC7);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD1);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD6);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDD);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);
    lcd_wr_regno(0xE3);
    lcd_wr_data(0x05);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA);
    lcd_wr_data(0x00);
    lcd_wr_data(0x1C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x33);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x50);
    lcd_wr_data(0x00);
    lcd_wr_data(0x62);
    lcd_wr_data(0x00);
    lcd_wr_data(0x78);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);
    lcd_wr_data(0x97);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA6);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB7);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC7);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD1);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD5);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDD);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE4);
    lcd_wr_data(0x01);
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0x00);
    lcd_wr_data(0x02);
    lcd_wr_data(0x00);
    lcd_wr_data(0x2A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x4B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x5D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x74);
    lcd_wr_data(0x00);
    lcd_wr_data(0x84);
    lcd_wr_data(0x00);
    lcd_wr_data(0x93);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB3);
    lcd_wr_data(0x00);
    lcd_wr_data(0xBE);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC4);
    lcd_wr_data(0x00);
    lcd_wr_data(0xCD);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD3);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDD);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);
    lcd_wr_regno(0xE5);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x02);
    lcd_wr_data(0x00);
    lcd_wr_data(0x29);
    lcd_wr_data(0x00);
    lcd_wr_data(0x3C);
    lcd_wr_data(0x00);
    lcd_wr_data(0x4B);
    lcd_wr_data(0x00);
    lcd_wr_data(0x5D);
    lcd_wr_data(0x00);
    lcd_wr_data(0x74);
    lcd_wr_data(0x00);
    lcd_wr_data(0x84);
    lcd_wr_data(0x00);
    lcd_wr_data(0x93);
    lcd_wr_data(0x00);
    lcd_wr_data(0xA2);
    lcd_wr_data(0x00);
    lcd_wr_data(0xB3);
    lcd_wr_data(0x00);
    lcd_wr_data(0xBE);
    lcd_wr_data(0x00);
    lcd_wr_data(0xC4);
    lcd_wr_data(0x00);
    lcd_wr_data(0xCD);
    lcd_wr_data(0x00);
    lcd_wr_data(0xD3);
    lcd_wr_data(0x00);
    lcd_wr_data(0xDC);
    lcd_wr_data(0x00);
    lcd_wr_data(0xF3);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE6);
    lcd_wr_data(0x11);
    lcd_wr_data(0x00);
    lcd_wr_data(0x34);
    lcd_wr_data(0x00);
    lcd_wr_data(0x56);
    lcd_wr_data(0x00);
    lcd_wr_data(0x76);
    lcd_wr_data(0x00);
    lcd_wr_data(0x77);
    lcd_wr_data(0x00);
    lcd_wr_data(0x66);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0xBB);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0x66);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);
    lcd_wr_data(0x45);
    lcd_wr_data(0x00);
    lcd_wr_data(0x43);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE7);
    lcd_wr_data(0x32);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);
    lcd_wr_data(0x76);
    lcd_wr_data(0x00);
    lcd_wr_data(0x66);
    lcd_wr_data(0x00);
    lcd_wr_data(0x67);
    lcd_wr_data(0x00);
    lcd_wr_data(0x67);
    lcd_wr_data(0x00);
    lcd_wr_data(0x87);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0xBB);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0x77);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x56);
    lcd_wr_data(0x00);
    lcd_wr_data(0x23);
    lcd_wr_data(0x00);
    lcd_wr_data(0x33);
    lcd_wr_data(0x00);
    lcd_wr_data(0x45);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE8);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0x87);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);
    lcd_wr_data(0x77);
    lcd_wr_data(0x00);
    lcd_wr_data(0x66);
    lcd_wr_data(0x00);
    lcd_wr_data(0x88);
    lcd_wr_data(0x00);
    lcd_wr_data(0xAA);
    lcd_wr_data(0x00);
    lcd_wr_data(0xBB);
    lcd_wr_data(0x00);
    lcd_wr_data(0x99);
    lcd_wr_data(0x00);
    lcd_wr_data(0x66);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x44);
    lcd_wr_data(0x00);
    lcd_wr_data(0x55);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xE9);
    lcd_wr_data(0xAA);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0x00);
    lcd_wr_data(0xAA);

    lcd_wr_regno(0xCF);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xF0);
    lcd_wr_data(0x00);
    lcd_wr_data(0x50);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xF3);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xF9);
    lcd_wr_data(0x06);
    lcd_wr_data(0x10);
    lcd_wr_data(0x29);
    lcd_wr_data(0x00);

    lcd_wr_regno(0x3A);
    lcd_wr_data(0x55);  /* 66 */

    lcd_wr_regno(0x11);
    HAL_Delay(100);
    lcd_wr_regno(0x29);
    lcd_wr_regno(0x35);
    lcd_wr_data(0x00);

    lcd_wr_regno(0x51);
    lcd_wr_data(0xFF);
    lcd_wr_regno(0x53);
    lcd_wr_data(0x2C);
    lcd_wr_regno(0x55);
    lcd_wr_data(0x82);
    lcd_wr_regno(0x2c);
}

/**
 * @brief       ST7796�Ĵ�����ʼ������ 
 * @param       ��
 * @retval      ��
 */
void lcd_ex_st7796_reginit(void)
{
    lcd_wr_regno(0x11);

    HAL_Delay(120); 

    lcd_wr_regno(0x36); /* Memory Data Access Control MY,MX~~ */
    lcd_wr_data(0x48);
    
    lcd_wr_regno(0x3A);
    lcd_wr_data(0x55);
    
    lcd_wr_regno(0xF0);
    lcd_wr_data(0xC3);
    
    lcd_wr_regno(0xF0);
    lcd_wr_data(0x96);

    lcd_wr_regno(0xB4);
    lcd_wr_data(0x01);
    
    lcd_wr_regno(0xB6); /* Display Function Control */
    lcd_wr_data(0x0A);
    lcd_wr_data(0xA2);

    lcd_wr_regno(0xB7);
    lcd_wr_data(0xC6);

    lcd_wr_regno(0xB9);
    lcd_wr_data(0x02);
    lcd_wr_data(0xE0);

    lcd_wr_regno(0xC0);
    lcd_wr_data(0x80);
    lcd_wr_data(0x16);

    lcd_wr_regno(0xC1);
    lcd_wr_data(0x19);

    lcd_wr_regno(0xC2);
    lcd_wr_data(0xA7);

    lcd_wr_regno(0xC5);
    lcd_wr_data(0x16);   

    lcd_wr_regno(0xE8);
    lcd_wr_data(0x40);
    lcd_wr_data(0x8A);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x29);
    lcd_wr_data(0x19);
    lcd_wr_data(0xA5);
    lcd_wr_data(0x33);

    lcd_wr_regno(0xE0);
    lcd_wr_data(0xF0);
    lcd_wr_data(0x07);
    lcd_wr_data(0x0D);
    lcd_wr_data(0x04);
    lcd_wr_data(0x05);
    lcd_wr_data(0x14);
    lcd_wr_data(0x36);
    lcd_wr_data(0x54);
    lcd_wr_data(0x4C);
    lcd_wr_data(0x38);
    lcd_wr_data(0x13);
    lcd_wr_data(0x14);
    lcd_wr_data(0x2E);
    lcd_wr_data(0x34);

    lcd_wr_regno(0xE1);
    lcd_wr_data(0xF0);
    lcd_wr_data(0x10);
    lcd_wr_data(0x14);
    lcd_wr_data(0x0E);
    lcd_wr_data(0x0C);
    lcd_wr_data(0x08);
    lcd_wr_data(0x35);
    lcd_wr_data(0x44);
    lcd_wr_data(0x4C);
    lcd_wr_data(0x26);
    lcd_wr_data(0x10);
    lcd_wr_data(0x12);
    lcd_wr_data(0x2C);
    lcd_wr_data(0x32);

    lcd_wr_regno(0xF0);
    lcd_wr_data(0x3C);

    lcd_wr_regno(0xF0);
    lcd_wr_data(0x69);

    HAL_Delay(120);

    lcd_wr_regno(0x21);
    lcd_wr_regno(0x29);
}

/**
 * @brief       NT35510�Ĵ�����ʼ������ 
 * @param       ��
 * @retval      ��
 */
void lcd_ex_nt35510_reginit(void)
{
    lcd_write_reg(0xF000, 0x55);
    lcd_write_reg(0xF001, 0xAA);
    lcd_write_reg(0xF002, 0x52);
    lcd_write_reg(0xF003, 0x08);
    lcd_write_reg(0xF004, 0x01);
    /* AVDD ratio */
    lcd_write_reg(0xB600, 0x34); /* 34 */
    lcd_write_reg(0xB601, 0x34); /* 34 */
    lcd_write_reg(0xB602, 0x34); /* 34 */
    /* AVDD 5.2V */
    lcd_write_reg(0xB000, 0x0D); /* 0C,��С��AVDD��ѹ���� */
    lcd_write_reg(0xB001, 0x0D); /* 0C */
    lcd_write_reg(0xB002, 0x0D); /* 0C */
     
    /* AVEE ratio */
    lcd_write_reg(0xB700, 0x24); /* 24 */
    lcd_write_reg(0xB701, 0x24); /* 24 */
    lcd_write_reg(0xB702, 0x24); /* 24 */
    /* AVEE -5.2V */
    lcd_write_reg(0xB100, 0x0D); /* 0C��С��AVEE��ѹ���� */
    lcd_write_reg(0xB101, 0x0D); /* 0C */
    lcd_write_reg(0xB102, 0x0D); /* 0C */

    /* VCL */
    lcd_write_reg(0xB800, 0x24);
    lcd_write_reg(0xB801, 0x24);
    lcd_write_reg(0xB802, 0x24);

    lcd_write_reg(0xB200, 0x00);

    /***************VGH ratio***************/
    /* VGH ratio */
    lcd_write_reg(0xB900, 0x24); /* 14 */
    lcd_write_reg(0xB901, 0x24); /* 14 */
    lcd_write_reg(0xB902, 0x24); /* 14 */
    /* VGH 10.2V */
    lcd_write_reg(0xB300, 0x05); /* 08 */
    lcd_write_reg(0xB301, 0x05); /* 08 */
    lcd_write_reg(0xB302, 0x05); /* 08 */

    /***************VGLX ratio***************/
    /* VGLX ratio */
    lcd_write_reg(0xBA00, 0x34);
    lcd_write_reg(0xBA01, 0x34);
    lcd_write_reg(0xBA02, 0x34);
    /* VGL_REG -12.6V */
    lcd_write_reg(0xB500, 0x0B);
    lcd_write_reg(0xB501, 0x0B);
    lcd_write_reg(0xB502, 0x0B);

    /***************VGMP/VGSP 4.5V***********/
    lcd_write_reg(0xBC00, 0x00); /* 00 */
    lcd_write_reg(0xBC01, 0xA3); /* 80 */
    lcd_write_reg(0xBC02, 0x00); /* 00 */

    /* GMN/VGSN -4.5V */
    lcd_write_reg(0xBD00, 0x00); /* 00 */
    lcd_write_reg(0xBD01, 0xA3); /* 80 */
    lcd_write_reg(0xBD02, 0x00); /* 00 */

    /* VCOM  -1.25V */
    lcd_write_reg(0xBE00, 0x00);
    lcd_write_reg(0xBE01, 0x37); /* 64~2F */


    /***************Gamma Setting***************/
    lcd_write_reg(0xD100, 0x00);
    lcd_write_reg(0xD101, 0x37);
    lcd_write_reg(0xD102, 0x00);
    lcd_write_reg(0xD103, 0x53);
    lcd_write_reg(0xD104, 0x00);
    lcd_write_reg(0xD105, 0x79);
    lcd_write_reg(0xD106, 0x00);
    lcd_write_reg(0xD107, 0x97);
    lcd_write_reg(0xD108, 0x00);
    lcd_write_reg(0xD109, 0xB1);
    lcd_write_reg(0xD10A, 0x00);
    lcd_write_reg(0xD10B, 0xD5);
    lcd_write_reg(0xD10C, 0x00);
    lcd_write_reg(0xD10D, 0xF4);
    lcd_write_reg(0xD10E, 0x01);
    lcd_write_reg(0xD10F, 0x23);
    lcd_write_reg(0xD110, 0x01);
    lcd_write_reg(0xD111, 0x49);
    lcd_write_reg(0xD112, 0x01);
    lcd_write_reg(0xD113, 0x87);
    lcd_write_reg(0xD114, 0x01);
    lcd_write_reg(0xD115, 0xB6);
    lcd_write_reg(0xD116, 0x02);
    lcd_write_reg(0xD117, 0x00);
    lcd_write_reg(0xD118, 0x02);
    lcd_write_reg(0xD119, 0x3B);
    lcd_write_reg(0xD11A, 0x02);
    lcd_write_reg(0xD11B, 0x3D);
    lcd_write_reg(0xD11C, 0x02);
    lcd_write_reg(0xD11D, 0x75);
    lcd_write_reg(0xD11E, 0x02);
    lcd_write_reg(0xD11F, 0xB1);
    lcd_write_reg(0xD120, 0x02);
    lcd_write_reg(0xD121, 0xD5);
    lcd_write_reg(0xD122, 0x03);
    lcd_write_reg(0xD123, 0x09);
    lcd_write_reg(0xD124, 0x03);
    lcd_write_reg(0xD125, 0x28);
    lcd_write_reg(0xD126, 0x03);
    lcd_write_reg(0xD127, 0x52);
    lcd_write_reg(0xD128, 0x03);
    lcd_write_reg(0xD129, 0x6B);
    lcd_write_reg(0xD12A, 0x03);
    lcd_write_reg(0xD12B, 0x8D);
    lcd_write_reg(0xD12C, 0x03);
    lcd_write_reg(0xD12D, 0xA2);
    lcd_write_reg(0xD12E, 0x03);
    lcd_write_reg(0xD12F, 0xBB);
    lcd_write_reg(0xD130, 0x03);
    lcd_write_reg(0xD131, 0xC1);
    lcd_write_reg(0xD132, 0x03);
    lcd_write_reg(0xD133, 0xC1);
                     
    lcd_write_reg(0xD200, 0x00);
    lcd_write_reg(0xD201, 0x37);
    lcd_write_reg(0xD202, 0x00);
    lcd_write_reg(0xD203, 0x53);
    lcd_write_reg(0xD204, 0x00);
    lcd_write_reg(0xD205, 0x79);
    lcd_write_reg(0xD206, 0x00);
    lcd_write_reg(0xD207, 0x97);
    lcd_write_reg(0xD208, 0x00);
    lcd_write_reg(0xD209, 0xB1);
    lcd_write_reg(0xD20A, 0x00);
    lcd_write_reg(0xD20B, 0xD5);
    lcd_write_reg(0xD20C, 0x00);
    lcd_write_reg(0xD20D, 0xF4);
    lcd_write_reg(0xD20E, 0x01);
    lcd_write_reg(0xD20F, 0x23);
    lcd_write_reg(0xD210, 0x01);
    lcd_write_reg(0xD211, 0x49);
    lcd_write_reg(0xD212, 0x01);
    lcd_write_reg(0xD213, 0x87);
    lcd_write_reg(0xD214, 0x01);
    lcd_write_reg(0xD215, 0xB6);
    lcd_write_reg(0xD216, 0x02);
    lcd_write_reg(0xD217, 0x00);
    lcd_write_reg(0xD218, 0x02);
    lcd_write_reg(0xD219, 0x3B);
    lcd_write_reg(0xD21A, 0x02);
    lcd_write_reg(0xD21B, 0x3D);
    lcd_write_reg(0xD21C, 0x02);
    lcd_write_reg(0xD21D, 0x75);
    lcd_write_reg(0xD21E, 0x02);
    lcd_write_reg(0xD21F, 0xB1);
    lcd_write_reg(0xD220, 0x02);
    lcd_write_reg(0xD221, 0xD5);
    lcd_write_reg(0xD222, 0x03);
    lcd_write_reg(0xD223, 0x09);
    lcd_write_reg(0xD224, 0x03);
    lcd_write_reg(0xD225, 0x28);
    lcd_write_reg(0xD226, 0x03);
    lcd_write_reg(0xD227, 0x52);
    lcd_write_reg(0xD228, 0x03);
    lcd_write_reg(0xD229, 0x6B);
    lcd_write_reg(0xD22A, 0x03);
    lcd_write_reg(0xD22B, 0x8D);
    lcd_write_reg(0xD22C, 0x03);
    lcd_write_reg(0xD22D, 0xA2);
    lcd_write_reg(0xD22E, 0x03);
    lcd_write_reg(0xD22F, 0xBB);
    lcd_write_reg(0xD230, 0x03);
    lcd_write_reg(0xD231, 0xC1);
    lcd_write_reg(0xD232, 0x03);
    lcd_write_reg(0xD233, 0xC1);

    lcd_write_reg(0xD300, 0x00);
    lcd_write_reg(0xD301, 0x37);
    lcd_write_reg(0xD302, 0x00);
    lcd_write_reg(0xD303, 0x53);
    lcd_write_reg(0xD304, 0x00);
    lcd_write_reg(0xD305, 0x79);
    lcd_write_reg(0xD306, 0x00);
    lcd_write_reg(0xD307, 0x97);
    lcd_write_reg(0xD308, 0x00);
    lcd_write_reg(0xD309, 0xB1);
    lcd_write_reg(0xD30A, 0x00);
    lcd_write_reg(0xD30B, 0xD5);
    lcd_write_reg(0xD30C, 0x00);
    lcd_write_reg(0xD30D, 0xF4);
    lcd_write_reg(0xD30E, 0x01);
    lcd_write_reg(0xD30F, 0x23);
    lcd_write_reg(0xD310, 0x01);
    lcd_write_reg(0xD311, 0x49);
    lcd_write_reg(0xD312, 0x01);
    lcd_write_reg(0xD313, 0x87);
    lcd_write_reg(0xD314, 0x01); 
    lcd_write_reg(0xD315, 0xB6);
    lcd_write_reg(0xD316, 0x02);
    lcd_write_reg(0xD317, 0x00);
    lcd_write_reg(0xD318, 0x02);
    lcd_write_reg(0xD319, 0x3b);
    lcd_write_reg(0xD31A, 0x02);
    lcd_write_reg(0xD31B, 0x3d);
    lcd_write_reg(0xD31C, 0x02);
    lcd_write_reg(0xD31D, 0x75);
    lcd_write_reg(0xD31E, 0x02);
    lcd_write_reg(0xD31F, 0xB1);
    lcd_write_reg(0xD320, 0x02);
    lcd_write_reg(0xD321, 0xD5);
    lcd_write_reg(0xD322, 0x03);
    lcd_write_reg(0xD323, 0x09);
    lcd_write_reg(0xD324, 0x03);
    lcd_write_reg(0xD325, 0x28);
    lcd_write_reg(0xD326, 0x03);
    lcd_write_reg(0xD327, 0x52);
    lcd_write_reg(0xD328, 0x03);
    lcd_write_reg(0xD329, 0x6B);
    lcd_write_reg(0xD32A, 0x03);
    lcd_write_reg(0xD32B, 0x8D);
    lcd_write_reg(0xD32C, 0x03);
    lcd_write_reg(0xD32D, 0xA2);
    lcd_write_reg(0xD32E, 0x03);
    lcd_write_reg(0xD32F, 0xBB);
    lcd_write_reg(0xD330, 0x03);
    lcd_write_reg(0xD331, 0xC1);
    lcd_write_reg(0xD332, 0x03);
    lcd_write_reg(0xD333, 0xC1);

    lcd_write_reg(0xD400, 0x00);
    lcd_write_reg(0xD401, 0x37);
    lcd_write_reg(0xD402, 0x00);
    lcd_write_reg(0xD403, 0x53);
    lcd_write_reg(0xD404, 0x00);
    lcd_write_reg(0xD405, 0x79);
    lcd_write_reg(0xD406, 0x00);
    lcd_write_reg(0xD407, 0x97);
    lcd_write_reg(0xD408, 0x00);
    lcd_write_reg(0xD409, 0xB1);
    lcd_write_reg(0xD40A, 0x00);
    lcd_write_reg(0xD40B, 0xD5);
    lcd_write_reg(0xD40C, 0x00);
    lcd_write_reg(0xD40D, 0xF4);
    lcd_write_reg(0xD40E, 0x01);
    lcd_write_reg(0xD40F, 0x23);
    lcd_write_reg(0xD410, 0x01);
    lcd_write_reg(0xD411, 0x49);
    lcd_write_reg(0xD412, 0x01);
    lcd_write_reg(0xD413, 0x87);
    lcd_write_reg(0xD414, 0x01);
    lcd_write_reg(0xD415, 0xB6);
    lcd_write_reg(0xD416, 0x02);
    lcd_write_reg(0xD417, 0x00);
    lcd_write_reg(0xD418, 0x02);
    lcd_write_reg(0xD419, 0x3B);
    lcd_write_reg(0xD41A, 0x02);
    lcd_write_reg(0xD41B, 0x3D);
    lcd_write_reg(0xD41C, 0x02);
    lcd_write_reg(0xD41D, 0x75);
    lcd_write_reg(0xD41E, 0x02);
    lcd_write_reg(0xD41F, 0xB1);
    lcd_write_reg(0xD420, 0x02);
    lcd_write_reg(0xD421, 0xD5);
    lcd_write_reg(0xD422, 0x03);
    lcd_write_reg(0xD423, 0x09);
    lcd_write_reg(0xD424, 0x03);
    lcd_write_reg(0xD425, 0x28);
    lcd_write_reg(0xD426, 0x03);
    lcd_write_reg(0xD427, 0x52);
    lcd_write_reg(0xD428, 0x03);
    lcd_write_reg(0xD429, 0x6B);
    lcd_write_reg(0xD42A, 0x03);
    lcd_write_reg(0xD42B, 0x8D);
    lcd_write_reg(0xD42C, 0x03);
    lcd_write_reg(0xD42D, 0xA2);
    lcd_write_reg(0xD42E, 0x03);
    lcd_write_reg(0xD42F, 0xBB);
    lcd_write_reg(0xD430, 0x03);
    lcd_write_reg(0xD431, 0xC1);
    lcd_write_reg(0xD432, 0x03);
    lcd_write_reg(0xD433, 0xC1);

    lcd_write_reg(0xD500, 0x00);
    lcd_write_reg(0xD501, 0x37);
    lcd_write_reg(0xD502, 0x00);
    lcd_write_reg(0xD503, 0x53);
    lcd_write_reg(0xD504, 0x00);
    lcd_write_reg(0xD505, 0x79);
    lcd_write_reg(0xD506, 0x00);
    lcd_write_reg(0xD507, 0x97);
    lcd_write_reg(0xD508, 0x00);
    lcd_write_reg(0xD509, 0xB1);
    lcd_write_reg(0xD50A, 0x00);
    lcd_write_reg(0xD50B, 0xD5);
    lcd_write_reg(0xD50C, 0x00);
    lcd_write_reg(0xD50D, 0xF4);
    lcd_write_reg(0xD50E, 0x01);
    lcd_write_reg(0xD50F, 0x23);
    lcd_write_reg(0xD510, 0x01);
    lcd_write_reg(0xD511, 0x49);
    lcd_write_reg(0xD512, 0x01);
    lcd_write_reg(0xD513, 0x87);
    lcd_write_reg(0xD514, 0x01);
    lcd_write_reg(0xD515, 0xB6);
    lcd_write_reg(0xD516, 0x02);
    lcd_write_reg(0xD517, 0x00);
    lcd_write_reg(0xD518, 0x02);
    lcd_write_reg(0xD519, 0x3B);
    lcd_write_reg(0xD51A, 0x02);
    lcd_write_reg(0xD51B, 0x3D);
    lcd_write_reg(0xD51C, 0x02);
    lcd_write_reg(0xD51D, 0x75);
    lcd_write_reg(0xD51E, 0x02);
    lcd_write_reg(0xD51F, 0xB1);
    lcd_write_reg(0xD520, 0x02);
    lcd_write_reg(0xD521, 0xD5);
    lcd_write_reg(0xD522, 0x03);
    lcd_write_reg(0xD523, 0x09);
    lcd_write_reg(0xD524, 0x03);
    lcd_write_reg(0xD525, 0x28);
    lcd_write_reg(0xD526, 0x03);
    lcd_write_reg(0xD527, 0x52);
    lcd_write_reg(0xD528, 0x03);
    lcd_write_reg(0xD529, 0x6B);
    lcd_write_reg(0xD52A, 0x03);
    lcd_write_reg(0xD52B, 0x8D);
    lcd_write_reg(0xD52C, 0x03);
    lcd_write_reg(0xD52D, 0xA2);
    lcd_write_reg(0xD52E, 0x03);
    lcd_write_reg(0xD52F, 0xBB);
    lcd_write_reg(0xD530, 0x03);
    lcd_write_reg(0xD531, 0xC1);
    lcd_write_reg(0xD532, 0x03);
    lcd_write_reg(0xD533, 0xC1);

    lcd_write_reg(0xD600, 0x00);
    lcd_write_reg(0xD601, 0x37);
    lcd_write_reg(0xD602, 0x00);
    lcd_write_reg(0xD603, 0x53);
    lcd_write_reg(0xD604, 0x00);
    lcd_write_reg(0xD605, 0x79);
    lcd_write_reg(0xD606, 0x00);
    lcd_write_reg(0xD607, 0x97);
    lcd_write_reg(0xD608, 0x00);
    lcd_write_reg(0xD609, 0xB1);
    lcd_write_reg(0xD60A, 0x00);
    lcd_write_reg(0xD60B, 0xD5);
    lcd_write_reg(0xD60C, 0x00);
    lcd_write_reg(0xD60D, 0xF4);
    lcd_write_reg(0xD60E, 0x01);
    lcd_write_reg(0xD60F, 0x23);
    lcd_write_reg(0xD610, 0x01);
    lcd_write_reg(0xD611, 0x49);
    lcd_write_reg(0xD612, 0x01);
    lcd_write_reg(0xD613, 0x87);
    lcd_write_reg(0xD614, 0x01);
    lcd_write_reg(0xD615, 0xB6);
    lcd_write_reg(0xD616, 0x02);
    lcd_write_reg(0xD617, 0x00);
    lcd_write_reg(0xD618, 0x02);
    lcd_write_reg(0xD619, 0x3B);
    lcd_write_reg(0xD61A, 0x02);
    lcd_write_reg(0xD61B, 0x3D);
    lcd_write_reg(0xD61C, 0x02);
    lcd_write_reg(0xD61D, 0x75);
    lcd_write_reg(0xD61E, 0x02);
    lcd_write_reg(0xD61F, 0xB1);
    lcd_write_reg(0xD620, 0x02);
    lcd_write_reg(0xD621, 0xD5);
    lcd_write_reg(0xD622, 0x03);
    lcd_write_reg(0xD623, 0x09);
    lcd_write_reg(0xD624, 0x03);
    lcd_write_reg(0xD625, 0x28);
    lcd_write_reg(0xD626, 0x03);
    lcd_write_reg(0xD627, 0x52);
    lcd_write_reg(0xD628, 0x03);
    lcd_write_reg(0xD629, 0x6B);
    lcd_write_reg(0xD62A, 0x03);
    lcd_write_reg(0xD62B, 0x8D);
    lcd_write_reg(0xD62C, 0x03);
    lcd_write_reg(0xD62D, 0xA2);
    lcd_write_reg(0xD62E, 0x03);
    lcd_write_reg(0xD62F, 0xBB);
    lcd_write_reg(0xD630, 0x03);
    lcd_write_reg(0xD631, 0xC1);
    lcd_write_reg(0xD632, 0x03);
    lcd_write_reg(0xD633, 0xC1);

    /***************LV2 Page 0 enable***************/
    lcd_write_reg(0xF000, 0x55);
    lcd_write_reg(0xF001, 0xAA);
    lcd_write_reg(0xF002, 0x52);
    lcd_write_reg(0xF003, 0x08);
    lcd_write_reg(0xF004, 0x00);

    /***************480x800***************/
    //lcd_write_reg(0xB500, 0x50);
    //RGB Internal mode
    //REGW 0xB30001
    //RGB I/F Setting

    lcd_write_reg(0xB000, 0x00); 
    lcd_write_reg(0xB001, 0x05); 
    lcd_write_reg(0xB002, 0x02);
    lcd_write_reg(0xB003, 0x05); 
    lcd_write_reg(0xB004, 0x02);

    /*## SDT: */
    lcd_write_reg(0xB600, 0x08);
    lcd_write_reg(0xB500, 0x50); /* 0x6b 480x854       0x50 480x800 */

    /***************Display control***************/
    //lcd_write_reg(0xB100, 0xCC);
    //lcd_write_reg(0xB101, 0x00);

    /***************Source hold time***************/
    lcd_write_reg(0xB600, 0x05);

    /***************Gate EQ control***************/
    lcd_write_reg(0xB700, 0x00);
    lcd_write_reg(0xB701, 0x00);


    /***************Source EQ control (Mode 2)***************/
    lcd_write_reg(0xB800, 0x01);
    lcd_write_reg(0xB801, 0x05);
    lcd_write_reg(0xB802, 0x05);
    lcd_write_reg(0xB803, 0x05);
                    
    /***************Inversion mode  (2-dot)***************/
    lcd_write_reg(0xBC00, 0x02);//02,2-dot inversion
    lcd_write_reg(0xBC01, 0x00);
    lcd_write_reg(0xBC02, 0x00);
                     

    /*************** BOE SETTING(DEFAULT) ***************/
    lcd_write_reg(0xCC00, 0x03);
    lcd_write_reg(0xCC01, 0x00);
    lcd_write_reg(0xCC02, 0x00);

    /***************Frame rate***************/
    lcd_write_reg(0xBD00, 0x01);
    lcd_write_reg(0xBD01, 0x84);
    lcd_write_reg(0xBD02, 0x07);  /* 0X1C   0x07 */
    lcd_write_reg(0xBD03, 0x31);
    lcd_write_reg(0xBD04, 0x00);


    lcd_write_reg(0xBA00, 0x01);

    lcd_write_reg(0xFF00, 0xAA);
    lcd_write_reg(0xFF01, 0x55);
    lcd_write_reg(0xFF02, 0x25);
    lcd_write_reg(0xFF03, 0x01);

    /*************** Timing control 4H w/ 4-Delayms ***************/
    lcd_write_reg(0x3600, 0x40);
    lcd_write_reg(0x3500, 0x00);

    lcd_write_reg(0x3A00, 0x05);

    lcd_wr_regno(0x1100);
    HAL_Delay(120);
    lcd_wr_regno(0x2900);
    HAL_Delay(10);
    lcd_wr_regno(0x2C00);
}

/**
 * @brief       ILI9806�Ĵ�����ʼ������ 
 * @param       ��
 * @retval      ��
 */
void lcd_ex_ili9806_reginit(void)
{
    lcd_wr_regno(0xFF); /* EXTC Command Set enable register */
    lcd_wr_data(0xFF);
    lcd_wr_data(0x98);
    lcd_wr_data(0x06);

    lcd_wr_regno(0xBC); /* GIP 1 */
    lcd_wr_data(0x01);
    lcd_wr_data(0x0F);
    lcd_wr_data(0x61);
    lcd_wr_data(0xFF);
    lcd_wr_data(0x01);
    lcd_wr_data(0x01);
    lcd_wr_data(0x0B);
    lcd_wr_data(0x10);
    lcd_wr_data(0x37);
    lcd_wr_data(0x63);
    lcd_wr_data(0xFF);
    lcd_wr_data(0xFF);
    lcd_wr_data(0x01);
    lcd_wr_data(0x01);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0xFF);
    lcd_wr_data(0x52);
    lcd_wr_data(0x01);
    lcd_wr_data(0x00);
    lcd_wr_data(0x40);

    lcd_wr_regno(0xBD); /* GIP 2 */
    lcd_wr_data(0x01);
    lcd_wr_data(0x23);
    lcd_wr_data(0x45);
    lcd_wr_data(0x67);
    lcd_wr_data(0x01);
    lcd_wr_data(0x23);
    lcd_wr_data(0x45);
    lcd_wr_data(0x67);

    lcd_wr_regno(0xBE); /* GIP 3 */
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0xAB);
    lcd_wr_data(0x60);
    lcd_wr_data(0x22);
    lcd_wr_data(0x22);
    lcd_wr_data(0x22);
    lcd_wr_data(0x22);
    lcd_wr_data(0x22);

    lcd_wr_regno(0xC7); /* VCOM Control */
    lcd_wr_data(0x36);

    lcd_wr_regno(0xED); /* EN_volt_reg VGMP / VGMN /VGSP / VGSN voltage to output */
    lcd_wr_data(0x7F);
    lcd_wr_data(0x0F);

    lcd_wr_regno(0XC0); /* Power Control 1 Setting AVDD / AVEE / VGH / VGL */
    lcd_wr_data(0x0F);
    lcd_wr_data(0x0B);
    lcd_wr_data(0x0A);  /* VGH 15V,VGLO-10V */

    lcd_wr_regno(0XFC); /* AVDD / AVEE generated by internal pumping. */
    lcd_wr_data(0x08);

    lcd_wr_regno(0XDF); 
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x20);

    lcd_wr_regno(0XF3); /* DVDD Voltage Setting */
    lcd_wr_data(0x74);

    lcd_wr_regno(0xB4); /* Inversion Type */
    lcd_wr_data(0x00);  /* 02 */
    lcd_wr_data(0x00);  /* 02 */
    lcd_wr_data(0x00);  /* 02 */

    lcd_wr_regno(0xF7); /* Resolution Control */
    lcd_wr_data(0x82);  /* 480*800 */

    lcd_wr_regno(0xB1); /* FRAME RATE Setting */
    lcd_wr_data(0x00);
    lcd_wr_data(0x13);
    lcd_wr_data(0x13); 

    lcd_wr_regno(0XF2); /* CR_EQ_PC_SDT  #C0,06,40,28 */
    lcd_wr_data(0x80);
    lcd_wr_data(0x04);
    lcd_wr_data(0x40);
    lcd_wr_data(0x28);

    lcd_wr_regno(0XC1); /* Power Control 2  SD OP Bias_VRH1_VRH2_EXT_CPCK_SEL */
    lcd_wr_data(0x17);
    lcd_wr_data(0x88);  /* VGMP */
    lcd_wr_data(0x88);  /* VGMN */
    lcd_wr_data(0x20);

    lcd_wr_regno(0xE0); /* Positive Gamma Control */
    lcd_wr_data(0x00);  /* P1 */
    lcd_wr_data(0x0A);  /* P2 */
    lcd_wr_data(0x12);  /* P3 */
    lcd_wr_data(0x10);  /* P4 */
    lcd_wr_data(0x0E);  /* P5 */
    lcd_wr_data(0x20);  /* P6 */
    lcd_wr_data(0xCC);  /* P7 */
    lcd_wr_data(0x07);  /* P8 */
    lcd_wr_data(0x06);  /* P9 */
    lcd_wr_data(0x0B);  /* P10 */
    lcd_wr_data(0x0E);  /* P11 */
    lcd_wr_data(0x0F);  /* P12 */
    lcd_wr_data(0x0D);  /* P13 */
    lcd_wr_data(0x15);  /* P14 */
    lcd_wr_data(0x10);  /* P15 */
    lcd_wr_data(0x00);  /* P16 */

    lcd_wr_regno(0xE1); /* Negative Gamma Correction */
    lcd_wr_data(0x00);  /* P1 */
    lcd_wr_data(0x0B);  /* P2 */
    lcd_wr_data(0x13);  /* P3 */
    lcd_wr_data(0x0D);  /* P4 */
    lcd_wr_data(0x0E);  /* P5 */
    lcd_wr_data(0x1B);  /* P6 */
    lcd_wr_data(0x71);  /* P7 */
    lcd_wr_data(0x06);  /* P8 */
    lcd_wr_data(0x06);  /* P9 */
    lcd_wr_data(0x0A);  /* P10 */
    lcd_wr_data(0x0F);  /* P11 */
    lcd_wr_data(0x0E);  /* P12 */
    lcd_wr_data(0x0F);  /* P13 */
    lcd_wr_data(0x15);  /* P14 */
    lcd_wr_data(0x0C);  /* P15 */
    lcd_wr_data(0x00);  /* P16 */

    lcd_wr_regno(0x2a);   
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x01);
    lcd_wr_data(0xdf);

    lcd_wr_regno(0x2b);   
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x03);
    lcd_wr_data(0x1f);

    lcd_wr_regno(0x3A); /* Pixel Format */
    lcd_wr_data(0x55);

    lcd_wr_regno(0x36); /* Memory Access Control */
    lcd_wr_data(0x00);  /* 02-180 */

    lcd_wr_regno(0x11);
    HAL_Delay(120);   
    lcd_wr_regno(0x29);  
    HAL_Delay(20);  
    lcd_wr_regno(0x2C);
}

/**
 * @brief       SSD1963�Ĵ�����ʼ������ 
 * @param       ��
 * @retval      ��
 */
void lcd_ex_ssd1963_reginit(void)
{
    lcd_wr_regno(0xE2); /* Set PLL with OSC = 10MHz (hardware),	Multiplier N = 35, 250MHz < VCO < 800MHz = OSC*(N+1), VCO = 300MHz */
    lcd_wr_data(0x1D);  /* ����1 */
    lcd_wr_data(0x02);  /* ����2 Divider M = 2, PLL = 300/(M+1) = 100MHz */
    lcd_wr_data(0x04);  /* ����3 Validate M and N values */
    HAL_Delay(1); 
    lcd_wr_regno(0xE0); /*  Start PLL command */
    lcd_wr_data(0x01);  /*  enable PLL */
    HAL_Delay(10);
    lcd_wr_regno(0xE0); /*  Start PLL command again */
    lcd_wr_data(0x03);  /*  now, use PLL output as system clock */
    HAL_Delay(12);
    lcd_wr_regno(0x01); /* ����λ */
    HAL_Delay(10);

    lcd_wr_regno(0xE6); /* ��������Ƶ��,33Mhz */
    lcd_wr_data(0x2F);
    lcd_wr_data(0xFF);
    lcd_wr_data(0xFF);

    lcd_wr_regno(0xB0); /* ����LCDģʽ */
    lcd_wr_data(0x20);  /* 24λģʽ */
    lcd_wr_data(0x00);  /* TFT ģʽ */

    lcd_wr_data((SSD_HOR_RESOLUTION - 1) >> 8); /* ����LCDˮƽ���� */
    lcd_wr_data(SSD_HOR_RESOLUTION - 1);
    lcd_wr_data((SSD_VER_RESOLUTION - 1) >> 8); /* ����LCD��ֱ���� */
    lcd_wr_data(SSD_VER_RESOLUTION - 1);
    lcd_wr_data(0x00);  /* RGB���� */

    lcd_wr_regno(0xB4); /* Set horizontal period */
    lcd_wr_data((SSD_HT - 1) >> 8);
    lcd_wr_data(SSD_HT - 1);
    lcd_wr_data(SSD_HPS >> 8);
    lcd_wr_data(SSD_HPS);
    lcd_wr_data(SSD_HOR_PULSE_WIDTH - 1);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);
    lcd_wr_regno(0xB6); /* Set vertical perio */
    lcd_wr_data((SSD_VT - 1) >> 8);
    lcd_wr_data(SSD_VT - 1);
    lcd_wr_data(SSD_VPS >> 8);
    lcd_wr_data(SSD_VPS);
    lcd_wr_data(SSD_VER_FRONT_PORCH - 1);
    lcd_wr_data(0x00);
    lcd_wr_data(0x00);

    lcd_wr_regno(0xF0); /* ����SSD1963��CPU�ӿ�Ϊ16bit */
    lcd_wr_data(0x03);  /* 16-bit(565 format) data for 16bpp */

    lcd_wr_regno(0x29); /* ������ʾ */
    /* ����PWM���  ����ͨ��ռ�ձȿɵ� */
    lcd_wr_regno(0xD0); /* �����Զ���ƽ��DBC */
    lcd_wr_data(0x00);  /* disable */

    lcd_wr_regno(0xBE); /* ����PWM��� */
    lcd_wr_data(0x05);  /* 1����PWMƵ�� */
    lcd_wr_data(0xFE);  /* 2����PWMռ�ձ� */
    lcd_wr_data(0x01);  /* 3����C */
    lcd_wr_data(0x00);  /* 4����D */
    lcd_wr_data(0x00);  /* 5����E */
    lcd_wr_data(0x00);  /* 6����F */

    lcd_wr_regno(0xB8); /* ����GPIO���� */
    lcd_wr_data(0x03);  /* 2��IO�����ó���� */
    lcd_wr_data(0x01);  /* GPIOʹ��������IO���� */
    lcd_wr_regno(0xBA);
    lcd_wr_data(0X01);  /* GPIO[1:0]=01,����LCD���� */
}


















