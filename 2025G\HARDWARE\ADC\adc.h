#ifndef __ADC_H
#define __ADC_H

#include "stm32f4xx.h"
#include "stm32f4xx_adc.h"
#include "stm32f4xx_dma.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_rcc.h"
#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======= ADC配置参数 ======= */
#define ADC_SAMPLES_PER_HALF_BUFFER 3000
#define ADC_TOTAL_BUFFER_SIZE       (ADC_SAMPLES_PER_HALF_BUFFER * 2)
#define ADC_VREF_V                  3.3f
#define ADC_RESOLUTION              4096

/* ======= 外部变量声明 ======= */
extern volatile uint16_t g_adc1_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
extern volatile uint16_t g_adc2_dma_buffer[ADC_TOTAL_BUFFER_SIZE];
extern uint16_t g_adc1_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
extern uint16_t g_adc2_processing_buffer[ADC_SAMPLES_PER_HALF_BUFFER];
extern volatile uint8_t g_adc1_data_ready;
extern volatile uint8_t g_adc2_data_ready;

/* ======= 函数声明 ======= */
void ADC_Mode3_Init(void);
void ADC1_Mode3_Init(void);
void ADC2_Mode3_Init(void);
void ADC_Start_Sampling(void);
void ADC_Stop_Sampling(void);

#ifdef __cplusplus
}
#endif

#endif /* __ADC_H */
