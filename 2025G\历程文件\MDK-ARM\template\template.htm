<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [template\template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image template\template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6190004: Last Updated: Thu Jul 31 20:11:29 2025
<BR><P>
<H3>Maximum Stack Usage =        488 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; lcd_init &rArr; lcd_ex_nt35310_reginit &rArr; HAL_Delay
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[5e]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[60]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAError) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[5f]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1d]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[61]">DAC_DMAConvCpltCh1</a> from stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[64]">DAC_DMAConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[63]">DAC_DMAErrorCh1</a> from stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[66]">DAC_DMAErrorCh2</a> from stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[62]">DAC_DMAHalfConvCpltCh1</a> from stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[65]">DAC_DMAHalfConvCpltCh2</a> from stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) referenced 2 times from stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
 <LI><a href="#[58]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[15]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream7_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f4xx_it.o(.text.NMI_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f4xx_it.o(.text.SVC_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">SystemInit</a> from system_stm32f4xx.o(.text.SystemInit) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[c]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[67]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from stm32f4xx_it.o(.text.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">USART6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[69]">arm_rfft_1024_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6e]">arm_rfft_128_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6b]">arm_rfft_2048_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6a]">arm_rfft_256_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6c]">arm_rfft_32_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6f]">arm_rfft_4096_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[6d]">arm_rfft_512_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[68]">arm_rfft_64_fast_init_f32</a> from arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32) referenced 2 times from arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
 <LI><a href="#[5b]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5d]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[118]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[70]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[81]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[119]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[11a]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[11b]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[11c]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[11d]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[9a]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>

<P><STRONG><a name="[11e]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[11f]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[76]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[120]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[121]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[75]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[f7]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[122]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[77]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_dcmple</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmple.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Filter_Response
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_dcmpge</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmpge.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Filter_Response
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Filter_Response
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
</UL>

<P><STRONG><a name="[7d]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Filter_Response
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[123]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[124]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[125]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[7e]"></a>_frnd</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, frnd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _frnd
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_roundf
</UL>

<P><STRONG><a name="[80]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[79]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[71]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[126]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
</UL>

<P><STRONG><a name="[127]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[85]"></a>Analyze_Filter_Response</STRONG> (Thumb, 460 bytes, Stack size 32 bytes, main.o(.text.Analyze_Filter_Response))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Analyze_Filter_Response &rArr; __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmple
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DAC_DMAConvCpltCh1</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAConvCpltCh1 &rArr; HAL_DAC_ConvCpltCallbackCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[64]"></a>DAC_DMAConvCpltCh2</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAConvCpltCh2 &rArr; HAL_DACEx_ConvCpltCallbackCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[63]"></a>DAC_DMAErrorCh1</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAErrorCh1 &rArr; HAL_DAC_ErrorCallbackCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ErrorCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[66]"></a>DAC_DMAErrorCh2</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAErrorCh2 &rArr; HAL_DACEx_ErrorCallbackCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ErrorCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[62]"></a>DAC_DMAHalfConvCpltCh1</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAHalfConvCpltCh1 &rArr; HAL_DAC_ConvHalfCpltCallbackCh1
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConvHalfCpltCallbackCh1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[65]"></a>DAC_DMAHalfConvCpltCh2</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DAC_DMAHalfConvCpltCh2 &rArr; HAL_DACEx_ConvHalfCpltCallbackCh2
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DACEx_ConvHalfCpltCallbackCh2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.DMA2_Stream7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream7_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>Display_Results</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, main.o(.text.Display_Results))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = Display_Results &rArr; LCD_ShowString_Simplified &rArr; lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a1]"></a>Error_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(.text.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FSMC_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>FSMC_NORSRAM_Extended_Timing_Init</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Extended_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FSMC_NORSRAM_Extended_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[c4]"></a>FSMC_NORSRAM_Init</STRONG> (Thumb, 186 bytes, Stack size 28 bytes, stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = FSMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[c5]"></a>FSMC_NORSRAM_Timing_Init</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, stm32f4xx_ll_fsmc.o(.text.FSMC_NORSRAM_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FSMC_NORSRAM_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[93]"></a>Generate_Flat_Top_Window</STRONG> (Thumb, 336 bytes, Stack size 48 bytes, main.o(.text.Generate_Flat_Top_Window))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Generate_Flat_Top_Window &rArr; __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[95]"></a>Generate_Hanning_Window</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, main.o(.text.Generate_Hanning_Window))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Generate_Hanning_Window &rArr; __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>Get_Signal_Accurate_Amplitude_And_Phase</STRONG> (Thumb, 404 bytes, Stack size 88 bytes, main.o(.text.Get_Signal_Accurate_Amplitude_And_Phase))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = Get_Signal_Accurate_Amplitude_And_Phase &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e6]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 536 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[82]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, main.o(.text.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 372<LI>Call Chain = HAL_ADC_ConvCpltCallback &rArr; Process_Buffer_DFT_SIM &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[84]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, main.o(.text.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 372<LI>Call Chain = HAL_ADC_ConvHalfCpltCallback &rArr; Process_Buffer_DFT_SIM &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[83]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_ADC_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[9c]"></a>HAL_ADC_Init</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[9d]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 444 bytes, Stack size 64 bytes, adc.o(.text.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 590 bytes, Stack size 40 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>HAL_ADC_Stop_DMA</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Simulation_Mode
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>HAL_DACEx_ConvCpltCallbackCh2</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DACEx_ConvCpltCallbackCh2
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh2
</UL>

<P><STRONG><a name="[8e]"></a>HAL_DACEx_ConvHalfCpltCallbackCh2</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DACEx_ConvHalfCpltCallbackCh2
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh2
</UL>

<P><STRONG><a name="[8c]"></a>HAL_DACEx_ErrorCallbackCh2</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DACEx_ErrorCallbackCh2
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh2
</UL>

<P><STRONG><a name="[e9]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 190 bytes, Stack size 28 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[89]"></a>HAL_DAC_ConvCpltCallbackCh1</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DAC_ConvCpltCallbackCh1
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAConvCpltCh1
</UL>

<P><STRONG><a name="[8d]"></a>HAL_DAC_ConvHalfCpltCallbackCh1</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DAC_ConvHalfCpltCallbackCh1
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAHalfConvCpltCh1
</UL>

<P><STRONG><a name="[8b]"></a>HAL_DAC_ErrorCallbackCh1</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DAC_ErrorCallbackCh1
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMAErrorCh1
</UL>

<P><STRONG><a name="[a6]"></a>HAL_DAC_Init</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[a7]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 232 bytes, Stack size 48 bytes, dac.o(.text.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[a8]"></a>HAL_DAC_Start_DMA</STRONG> (Thumb, 418 bytes, Stack size 48 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_DAC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
</UL>

<P><STRONG><a name="[a9]"></a>HAL_DAC_Stop_DMA</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_DAC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Simulation_Mode
</UL>

<P><STRONG><a name="[a5]"></a>HAL_DMA_Abort</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
</UL>

<P><STRONG><a name="[d5]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 798 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream7_IRQHandler
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>HAL_DMA_Init</STRONG> (Thumb, 366 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[a3]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 194 bytes, Stack size 40 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[ae]"></a>HAL_Delay</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, stm32f4xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntReset
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ssd1963_reginit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9806_reginit
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35510_reginit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7796_reginit
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35310_reginit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9341_reginit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7789_reginit
</UL>

<P><STRONG><a name="[9f]"></a>HAL_GPIO_Init</STRONG> (Thumb, 950 bytes, Stack size 56 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FSMC_MspInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[110]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[de]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteData_AD9959
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Intserve
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntReset
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[aa]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>

<P><STRONG><a name="[f5]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[b0]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>HAL_InitTick</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[b3]"></a>HAL_MspInit</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[b6]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[b5]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EncodePriority
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[b1]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_NVIC_SetPriorityGrouping &rArr; __NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 610 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[bf]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[be]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[c0]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[bd]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[c1]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1726 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[c2]"></a>HAL_SRAM_Init</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_FSMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Extended_Timing_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Timing_Init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FSMC_Init
</UL>

<P><STRONG><a name="[c3]"></a>HAL_SRAM_MspInit</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, fsmc.o(.text.HAL_SRAM_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_SRAM_MspInit &rArr; HAL_FSMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FSMC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
</UL>

<P><STRONG><a name="[b4]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_SYSTICK_Config &rArr; SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[ee]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 290 bytes, Stack size 20 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
</UL>

<P><STRONG><a name="[c8]"></a>HAL_TIM_IC_ConfigChannel</STRONG> (Thumb, 304 bytes, Stack size 32 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_TIM_IC_ConfigChannel &rArr; TIM_TI1_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_SetConfig
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI4_SetConfig
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI3_SetConfig
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
</UL>

<P><STRONG><a name="[cd]"></a>HAL_TIM_IC_Init</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_TIM_IC_Init &rArr; HAL_TIM_IC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
</UL>

<P><STRONG><a name="[ce]"></a>HAL_TIM_IC_MspInit</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, tim.o(.text.HAL_TIM_IC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_TIM_IC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_TIM_IC_Start_IT</STRONG> (Thumb, 678 bytes, Stack size 80 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_TIM_IC_Start_IT &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
</UL>

<P><STRONG><a name="[d7]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[d6]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[d2]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 1116 bytes, Stack size 80 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>HAL_UART_Init</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[db]"></a>HAL_UART_MspInit</STRONG> (Thumb, 248 bytes, Stack size 56 bytes, usart.o(.text.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[f9]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[f8]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[dd]"></a>IO_Update</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, ad9959.o(.text.IO_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IO_Update &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Amplitude
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
</UL>

<P><STRONG><a name="[df]"></a>Init_AD9959</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, ad9959.o(.text.Init_AD9959))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Init_AD9959 &rArr; Write_frequence &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteData_AD9959
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Intserve
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntReset
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e1]"></a>IntReset</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, ad9959.o(.text.IntReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IntReset &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
</UL>

<P><STRONG><a name="[e0]"></a>Intserve</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, ad9959.o(.text.Intserve))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Intserve &rArr; HAL_GPIO_WritePin
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
</UL>

<P><STRONG><a name="[92]"></a>LCD_ShowString_Simplified</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, main.o(.text.LCD_ShowString_Simplified))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = LCD_ShowString_Simplified &rArr; lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Simulation_Mode
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Results
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e5]"></a>MX_ADC1_Init</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, adc.o(.text.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e7]"></a>MX_ADC2_Init</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, adc.o(.text.MX_ADC2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = MX_ADC2_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e8]"></a>MX_DAC_Init</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, dac.o(.text.MX_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_DAC_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ea]"></a>MX_DMA_Init</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, dma.o(.text.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[eb]"></a>MX_FSMC_Init</STRONG> (Thumb, 174 bytes, Stack size 72 bytes, fsmc.o(.text.MX_FSMC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = MX_FSMC_Init &rArr; HAL_SRAM_Init &rArr; HAL_SRAM_MspInit &rArr; HAL_FSMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ec]"></a>MX_GPIO_Init</STRONG> (Thumb, 676 bytes, Stack size 120 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ed]"></a>MX_TIM5_Init</STRONG> (Thumb, 138 bytes, Stack size 40 bytes, tim.o(.text.MX_TIM5_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM5_Init &rArr; HAL_TIM_IC_Init &rArr; HAL_TIM_IC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ef]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usart.o(.text.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[9b]"></a>Process_Buffer_DFT_SIM</STRONG> (Thumb, 964 bytes, Stack size 320 bytes, main.o(.text.Process_Buffer_DFT_SIM))
<BR><BR>[Stack]<UL><LI>Max Depth = 356<LI>Call Chain = Process_Buffer_DFT_SIM &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_roundf
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>

<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f1]"></a>Start_Simulation_Mode</STRONG> (Thumb, 338 bytes, Stack size 40 bytes, main.o(.text.Start_Simulation_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = Start_Simulation_Mode &rArr; LCD_ShowString_Simplified &rArr; lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Start_DMA
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Start_IT
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f2]"></a>Start_Sweep_And_Analysis</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, main.o(.text.Start_Sweep_And_Analysis))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = Start_Sweep_And_Analysis &rArr; LCD_ShowString_Simplified &rArr; lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Amplitude
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f4]"></a>Stop_Simulation_Mode</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, main.o(.text.Stop_Simulation_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = Stop_Simulation_Mode &rArr; LCD_ShowString_Simplified &rArr; lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Stop_DMA
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f6]"></a>SystemClock_Config</STRONG> (Thumb, 190 bytes, Stack size 88 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5c]"></a>SystemInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, system_stm32f4xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[cf]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 420 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
</UL>

<P><STRONG><a name="[d1]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Start_IT
</UL>

<P><STRONG><a name="[c9]"></a>TIM_TI1_SetConfig</STRONG> (Thumb, 278 bytes, Stack size 28 bytes, stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = TIM_TI1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e2]"></a>WriteData_AD9959</STRONG> (Thumb, 398 bytes, Stack size 32 bytes, ad9959.o(.text.WriteData_AD9959))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WriteData_AD9959 &rArr; HAL_GPIO_WritePin
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Amplitude
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
</UL>

<P><STRONG><a name="[f3]"></a>Write_Amplitude</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, ad9959.o(.text.Write_Amplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Write_Amplitude &rArr; IO_Update &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteData_AD9959
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e3]"></a>Write_frequence</STRONG> (Thumb, 224 bytes, Stack size 24 bytes, ad9959.o(.text.Write_frequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Write_frequence &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteData_AD9959
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
</UL>

<P><STRONG><a name="[fa]"></a>arm_cfft_init_f32</STRONG> (Thumb, 180 bytes, Stack size 0 bytes, arm_cfft_init_f32.o(.text.arm_cfft_init_f32))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_64_fast_init_f32
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_512_fast_init_f32
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_4096_fast_init_f32
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_32_fast_init_f32
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_256_fast_init_f32
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_2048_fast_init_f32
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_128_fast_init_f32
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_1024_fast_init_f32
</UL>

<P><STRONG><a name="[10f]"></a>arm_rfft_fast_init_f32</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>lcd_clear</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, lcd.o(.text.lcd_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = lcd_clear &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_set_cursor
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_ram_prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Simulation_Mode
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Results
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[fd]"></a>lcd_display_dir</STRONG> (Thumb, 530 bytes, Stack size 16 bytes, lcd.o(.text.lcd_display_dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = lcd_display_dir &rArr; lcd_scan_dir &rArr; lcd_write_reg
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_scan_dir
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[ff]"></a>lcd_draw_point</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, lcd.o(.text.lcd_draw_point))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_set_cursor
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_ram_prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_char
</UL>

<P><STRONG><a name="[100]"></a>lcd_ex_ili9341_reginit</STRONG> (Thumb, 584 bytes, Stack size 56 bytes, lcd.o(.text.lcd_ex_ili9341_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = lcd_ex_ili9341_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[103]"></a>lcd_ex_ili9806_reginit</STRONG> (Thumb, 878 bytes, Stack size 96 bytes, lcd.o(.text.lcd_ex_ili9806_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = lcd_ex_ili9806_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[104]"></a>lcd_ex_nt35310_reginit</STRONG> (Thumb, 3986 bytes, Stack size 320 bytes, lcd.o(.text.lcd_ex_nt35310_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = lcd_ex_nt35310_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[105]"></a>lcd_ex_nt35510_reginit</STRONG> (Thumb, 4022 bytes, Stack size 240 bytes, lcd.o(.text.lcd_ex_nt35510_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = lcd_ex_nt35510_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_reg
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[107]"></a>lcd_ex_ssd1963_reginit</STRONG> (Thumb, 386 bytes, Stack size 40 bytes, lcd.o(.text.lcd_ex_ssd1963_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = lcd_ex_ssd1963_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[108]"></a>lcd_ex_st7789_reginit</STRONG> (Thumb, 452 bytes, Stack size 56 bytes, lcd.o(.text.lcd_ex_st7789_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = lcd_ex_st7789_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[109]"></a>lcd_ex_st7796_reginit</STRONG> (Thumb, 480 bytes, Stack size 56 bytes, lcd.o(.text.lcd_ex_st7796_reginit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = lcd_ex_st7796_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[10a]"></a>lcd_init</STRONG> (Thumb, 1084 bytes, Stack size 64 bytes, lcd.o(.text.lcd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = lcd_init &rArr; lcd_ex_nt35310_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAM_Extended_Timing_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_display_dir
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ssd1963_reginit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9806_reginit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_reg
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35510_reginit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7796_reginit
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35310_reginit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9341_reginit
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7789_reginit
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_rd_data
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>lcd_scan_dir</STRONG> (Thumb, 860 bytes, Stack size 40 bytes, lcd.o(.text.lcd_scan_dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = lcd_scan_dir &rArr; lcd_write_reg
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_reg
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_display_dir
</UL>

<P><STRONG><a name="[fb]"></a>lcd_set_cursor</STRONG> (Thumb, 392 bytes, Stack size 32 bytes, lcd.o(.text.lcd_set_cursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>

<P><STRONG><a name="[10e]"></a>lcd_show_char</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, lcd.o(.text.lcd_show_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
</UL>

<P><STRONG><a name="[e4]"></a>lcd_show_string</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, lcd.o(.text.lcd_show_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = lcd_show_string &rArr; lcd_show_char &rArr; lcd_draw_point &rArr; lcd_set_cursor &rArr; lcd_wr_data
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_char
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
</UL>

<P><STRONG><a name="[10c]"></a>lcd_ssd_backlight_set</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, lcd.o(.text.lcd_ssd_backlight_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lcd_ssd_backlight_set &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_data
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[102]"></a>lcd_wr_data</STRONG> (Thumb, 30 bytes, Stack size 4 bytes, lcd.o(.text.lcd_wr_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lcd_wr_data
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_scan_dir
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_set_cursor
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ssd1963_reginit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9806_reginit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7796_reginit
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35310_reginit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9341_reginit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7789_reginit
</UL>

<P><STRONG><a name="[101]"></a>lcd_wr_regno</STRONG> (Thumb, 30 bytes, Stack size 4 bytes, lcd.o(.text.lcd_wr_regno))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lcd_wr_regno
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ssd_backlight_set
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_scan_dir
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_set_cursor
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ssd1963_reginit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9806_reginit
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35510_reginit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7796_reginit
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35310_reginit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_ili9341_reginit
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_st7789_reginit
</UL>

<P><STRONG><a name="[fc]"></a>lcd_write_ram_prepare</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lcd.o(.text.lcd_write_ram_prepare))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>

<P><STRONG><a name="[106]"></a>lcd_write_reg</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, lcd.o(.text.lcd_write_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lcd_write_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_scan_dir
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_ex_nt35510_reginit
</UL>

<P><STRONG><a name="[5b]"></a>main</STRONG> (Thumb, 1420 bytes, Stack size 80 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = main &rArr; lcd_init &rArr; lcd_ex_nt35310_reginit &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Simulation_Mode
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Results
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Analyze_Filter_Response
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Signal_Accurate_Amplitude_And_Phase
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Simulation_Mode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sweep_And_Analysis
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Hanning_Window
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Flat_Top_Window
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_ShowString_Simplified
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_init_f32
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC2_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FSMC_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Amplitude
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_frequence
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_AD9959
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[111]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[99]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Signal_Accurate_Amplitude_And_Phase
</UL>

<P><STRONG><a name="[94]"></a>__hardfp_cosf</STRONG> (Thumb, 280 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Signal_Accurate_Amplitude_And_Phase
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Hanning_Window
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Flat_Top_Window
</UL>

<P><STRONG><a name="[f0]"></a>__hardfp_roundf</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, roundf.o(i.__hardfp_roundf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __hardfp_roundf &rArr; _frnd
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frnd
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
</UL>

<P><STRONG><a name="[97]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Signal_Accurate_Amplitude_And_Phase
</UL>

<P><STRONG><a name="[98]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Buffer_DFT_SIM
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Signal_Accurate_Amplitude_And_Phase
</UL>

<P><STRONG><a name="[117]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[114]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[116]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[112]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[115]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[128]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[129]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[12a]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[113]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[10b]"></a>lcd_rd_data</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, lcd.o(.text.lcd_rd_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lcd_rd_data &rArr; lcd_opt_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_opt_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[10d]"></a>lcd_opt_delay</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lcd.o(.text.lcd_opt_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lcd_opt_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_rd_data
</UL>

<P><STRONG><a name="[af]"></a>HAL_FSMC_MspInit</STRONG> (Thumb, 258 bytes, Stack size 56 bytes, fsmc.o(.text.HAL_FSMC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_FSMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SRAM_MspInit
</UL>

<P><STRONG><a name="[9e]"></a>ADC_Init</STRONG> (Thumb, 394 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(.text.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[5e]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = ADC_DMAConvCplt &rArr; HAL_ADC_ConvCpltCallback &rArr; Process_Buffer_DFT_SIM &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[5f]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 388<LI>Call Chain = ADC_DMAHalfConvCplt &rArr; HAL_ADC_ConvHalfCpltCallback &rArr; Process_Buffer_DFT_SIM &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[60]"></a>ADC_DMAError</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ADC_DMAError &rArr; HAL_ADC_ErrorCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[ab]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 240 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[ac]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[ad]"></a>DMA_SetConfig</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[bb]"></a>__NVIC_SetPriorityGrouping</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>

<P><STRONG><a name="[b8]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[b9]"></a>NVIC_EncodePriority</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, stm32f4xx_hal_cortex.o(.text.NVIC_EncodePriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = NVIC_EncodePriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[ba]"></a>__NVIC_SetPriority</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[b7]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, stm32f4xx_hal_cortex.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>

<P><STRONG><a name="[c7]"></a>SysTick_Config</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[ca]"></a>TIM_TI2_SetConfig</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(.text.TIM_TI2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_TI2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[cb]"></a>TIM_TI3_SetConfig</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(.text.TIM_TI3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_TI3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[cc]"></a>TIM_TI4_SetConfig</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(.text.TIM_TI4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_TI4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[dc]"></a>UART_SetConfig</STRONG> (Thumb, 314 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[d4]"></a>UART_EndRxTransfer</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text.UART_EndRxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[d3]"></a>UART_Receive_IT</STRONG> (Thumb, 360 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[67]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_DMAAbortOnError &rArr; HAL_UART_ErrorCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[d8]"></a>UART_Transmit_IT</STRONG> (Thumb, 148 bytes, Stack size 12 bytes, stm32f4xx_hal_uart.o(.text.UART_Transmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UART_Transmit_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[69]"></a>arm_rfft_1024_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_1024_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6e]"></a>arm_rfft_128_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_128_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6b]"></a>arm_rfft_2048_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_2048_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6a]"></a>arm_rfft_256_fast_init_f32</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_256_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6c]"></a>arm_rfft_32_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_32_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6f]"></a>arm_rfft_4096_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_4096_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[6d]"></a>arm_rfft_512_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_512_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL>
<P><STRONG><a name="[68]"></a>arm_rfft_64_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_rfft_64_fast_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
