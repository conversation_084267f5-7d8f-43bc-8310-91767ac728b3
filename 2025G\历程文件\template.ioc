#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_1
ADC1.DMAContinuousRequests=ENABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_Ext_IT11
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,master,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,DMAContinuousRequests,ExternalTrigConv
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
ADC2.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_2
ADC2.DMAContinuousRequests=ENABLE
ADC2.ExternalTrigConv=ADC_EXTERNALTRIGCONV_Ext_IT11
ADC2.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,ExternalTrigConv,DMAContinuousRequests
ADC2.NbrOfConversionFlag=1
ADC2.Rank-0\#ChannelRegularConversion=1
ADC2.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_EXT_IT9
DAC.IPParameters=DAC_Trigger
Dma.ADC1.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.2.Instance=DMA2_Stream0
Dma.ADC1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.2.MemInc=DMA_MINC_ENABLE
Dma.ADC1.2.Mode=DMA_CIRCULAR
Dma.ADC1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.2.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.2.Priority=DMA_PRIORITY_HIGH
Dma.ADC1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.ADC2.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC2.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC2.1.Instance=DMA2_Stream2
Dma.ADC2.1.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC2.1.MemInc=DMA_MINC_ENABLE
Dma.ADC2.1.Mode=DMA_CIRCULAR
Dma.ADC2.1.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC2.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC2.1.Priority=DMA_PRIORITY_HIGH
Dma.ADC2.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.DAC1.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.DAC1.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.DAC1.3.Instance=DMA1_Stream5
Dma.DAC1.3.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.DAC1.3.MemInc=DMA_MINC_ENABLE
Dma.DAC1.3.Mode=DMA_CIRCULAR
Dma.DAC1.3.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.DAC1.3.PeriphInc=DMA_PINC_DISABLE
Dma.DAC1.3.Priority=DMA_PRIORITY_LOW
Dma.DAC1.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART1_TX
Dma.Request1=ADC2
Dma.Request2=ADC1
Dma.Request3=DAC1
Dma.RequestsNb=4
Dma.USART1_TX.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.0.Instance=DMA2_Stream7
Dma.USART1_TX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.0.Mode=DMA_NORMAL
Dma.USART1_TX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FSMC.AddressSetupTime1=0x0F
FSMC.BusTurnAroundDuration1=0
FSMC.DataSetupTime1=60
FSMC.ExtendedAddressSetupTime1=9
FSMC.ExtendedBusTurnAroundDuration1=0
FSMC.ExtendedDataSetupTime1=9
FSMC.ExtendedMode1=FSMC_EXTENDED_MODE_ENABLE
FSMC.IPParameters=ExtendedMode1,AddressSetupTime1,DataSetupTime1,BusTurnAroundDuration1,ExtendedAddressSetupTime1,ExtendedDataSetupTime1,ExtendedBusTurnAroundDuration1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP2=DAC
Mcu.IP3=DMA
Mcu.IP4=FSMC
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SYS
Mcu.IP8=TIM5
Mcu.IP9=USART1
Mcu.IPNb=10
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE4
Mcu.Pin1=PF9
Mcu.Pin10=PC4
Mcu.Pin11=PF11
Mcu.Pin12=PF12
Mcu.Pin13=PE7
Mcu.Pin14=PE8
Mcu.Pin15=PE9
Mcu.Pin16=PE10
Mcu.Pin17=PE11
Mcu.Pin18=PE12
Mcu.Pin19=PE13
Mcu.Pin2=PH0-OSC_IN
Mcu.Pin20=PE14
Mcu.Pin21=PE15
Mcu.Pin22=PB15
Mcu.Pin23=PD8
Mcu.Pin24=PD9
Mcu.Pin25=PD10
Mcu.Pin26=PD12
Mcu.Pin27=PD14
Mcu.Pin28=PD15
Mcu.Pin29=PC8
Mcu.Pin3=PH1-OSC_OUT
Mcu.Pin30=PC9
Mcu.Pin31=PA9
Mcu.Pin32=PA10
Mcu.Pin33=PA13
Mcu.Pin34=PA14
Mcu.Pin35=PD0
Mcu.Pin36=PD1
Mcu.Pin37=PD4
Mcu.Pin38=PD5
Mcu.Pin39=PG10
Mcu.Pin4=PA0-WKUP
Mcu.Pin40=PG11
Mcu.Pin41=PG12
Mcu.Pin42=PG13
Mcu.Pin43=PG14
Mcu.Pin44=PG15
Mcu.Pin45=PB3
Mcu.Pin46=PB4
Mcu.Pin47=PB5
Mcu.Pin48=PB6
Mcu.Pin49=PB7
Mcu.Pin5=PA1
Mcu.Pin50=VP_SYS_VS_Systick
Mcu.Pin51=VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.4.0_1.4.0
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PA4
Mcu.Pin9=PA7
Mcu.PinsNb=52
Mcu.ThirdParty0=STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32F407ZGTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:2\:0\:true\:false\:true\:false\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_PuPd,GPIO_Label
PA0-WKUP.GPIO_Label=SIM_KEY
PA0-WKUP.GPIO_PuPd=GPIO_PULLDOWN
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPIO_Input
PA1.Signal=ADCx_IN1
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Signal=ADCx_IN2
PA3.Signal=S_TIM5_CH4
PA4.Signal=COMP_DAC1_group
PA7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA7.GPIO_Label=RELAY
PA7.GPIO_PuPd=GPIO_PULLDOWN
PA7.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PA7.Locked=true
PA7.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=LCD_BL
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PB3.GPIO_Label=CS
PB3.GPIO_PuPd=GPIO_PULLUP
PB3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB3.Locked=true
PB3.PinState=GPIO_PIN_SET
PB3.Signal=GPIO_Output
PB4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB4.GPIO_Label=SCLK
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB5.GPIO_Label=SDIO0
PB5.GPIO_PuPd=GPIO_PULLUP
PB5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB6.GPIO_Label=UPDATE
PB6.GPIO_PuPd=GPIO_PULLUP
PB6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB6.Locked=true
PB6.Signal=GPIO_Output
PB7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB7.GPIO_Label=PS0
PB7.GPIO_PuPd=GPIO_PULLUP
PB7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB7.Locked=true
PB7.Signal=GPIO_Output
PC4.GPIOParameters=GPIO_PuPd,GPIO_Label
PC4.GPIO_Label=IDLE_KEY
PC4.GPIO_PuPd=GPIO_PULLDOWN
PC4.Locked=true
PC4.Signal=GPIO_Input
PC8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=PS1
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC8.Locked=true
PC8.Signal=GPIO_Output
PC9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=PS2
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC9.Locked=true
PC9.Signal=GPIO_Output
PD0.Signal=FSMC_D2_DA2
PD1.Signal=FSMC_D3_DA3
PD10.Signal=FSMC_D15_DA15
PD12.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PD12.GPIO_Label=SDIO3
PD12.GPIO_PuPd=GPIO_PULLUP
PD12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD12.Locked=true
PD12.PinState=GPIO_PIN_RESET
PD12.Signal=GPIO_Output
PD14.Signal=FSMC_D0_DA0
PD15.Signal=FSMC_D1_DA1
PD4.Signal=FSMC_NOE
PD5.Signal=FSMC_NWE
PD8.Signal=FSMC_D13_DA13
PD9.Signal=FSMC_D14_DA14
PE10.Signal=FSMC_D7_DA7
PE11.Signal=FSMC_D8_DA8
PE12.Signal=FSMC_D9_DA9
PE13.Signal=FSMC_D10_DA10
PE14.Signal=FSMC_D11_DA11
PE15.Signal=FSMC_D12_DA12
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label
PE4.GPIO_Label=LEARN_KEY
PE4.GPIO_PuPd=GPIO_PULLDOWN
PE4.Locked=true
PE4.Signal=GPIO_Input
PE7.Signal=FSMC_D4_DA4
PE8.Signal=FSMC_D5_DA5
PE9.Signal=FSMC_D6_DA6
PF11.Signal=GPXTI11
PF12.Signal=FSMC_A6
PF9.Signal=GPXTI9
PG10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG10.GPIO_Label=PWR1
PG10.GPIO_PuPd=GPIO_PULLUP
PG10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG10.Locked=true
PG10.Signal=GPIO_Output
PG11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG11.GPIO_Label=Reset
PG11.GPIO_PuPd=GPIO_PULLUP
PG11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG11.Locked=true
PG11.Signal=GPIO_Output
PG12.Mode=NorPsramChipSelect4_1
PG12.Signal=FSMC_NE4
PG13.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG13.GPIO_Label=SDIO2
PG13.GPIO_PuPd=GPIO_PULLUP
PG13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG13.Locked=true
PG13.Signal=GPIO_Output
PG14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG14.GPIO_Label=SDIO1
PG14.GPIO_PuPd=GPIO_PULLUP
PG14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG14.Locked=true
PG14.Signal=GPIO_Output
PG15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PG15.GPIO_Label=PS3
PG15.GPIO_PuPd=GPIO_PULLUP
PG15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG15.Locked=true
PG15.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=template.ioc
ProjectManager.ProjectName=template
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_FSMC_Init-FSMC-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_ADC2_Init-ADC2-false-HAL-true,8-MX_DAC_Init-DAC-false-HAL-true,9-MX_TIM4_Init-TIM4-false-HAL-true,10-MX_TIM5_Init-TIM5-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN1.0=ADC1_IN1,IN1
SH.ADCx_IN1.ConfNb=1
SH.ADCx_IN2.0=ADC2_IN2,IN2
SH.ADCx_IN2.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SH.FSMC_A6.0=FSMC_A6,A6_1
SH.FSMC_A6.ConfNb=1
SH.FSMC_D0_DA0.0=FSMC_D0,16b-d1
SH.FSMC_D0_DA0.ConfNb=1
SH.FSMC_D10_DA10.0=FSMC_D10,16b-d1
SH.FSMC_D10_DA10.ConfNb=1
SH.FSMC_D11_DA11.0=FSMC_D11,16b-d1
SH.FSMC_D11_DA11.ConfNb=1
SH.FSMC_D12_DA12.0=FSMC_D12,16b-d1
SH.FSMC_D12_DA12.ConfNb=1
SH.FSMC_D13_DA13.0=FSMC_D13,16b-d1
SH.FSMC_D13_DA13.ConfNb=1
SH.FSMC_D14_DA14.0=FSMC_D14,16b-d1
SH.FSMC_D14_DA14.ConfNb=1
SH.FSMC_D15_DA15.0=FSMC_D15,16b-d1
SH.FSMC_D15_DA15.ConfNb=1
SH.FSMC_D1_DA1.0=FSMC_D1,16b-d1
SH.FSMC_D1_DA1.ConfNb=1
SH.FSMC_D2_DA2.0=FSMC_D2,16b-d1
SH.FSMC_D2_DA2.ConfNb=1
SH.FSMC_D3_DA3.0=FSMC_D3,16b-d1
SH.FSMC_D3_DA3.ConfNb=1
SH.FSMC_D4_DA4.0=FSMC_D4,16b-d1
SH.FSMC_D4_DA4.ConfNb=1
SH.FSMC_D5_DA5.0=FSMC_D5,16b-d1
SH.FSMC_D5_DA5.ConfNb=1
SH.FSMC_D6_DA6.0=FSMC_D6,16b-d1
SH.FSMC_D6_DA6.ConfNb=1
SH.FSMC_D7_DA7.0=FSMC_D7,16b-d1
SH.FSMC_D7_DA7.ConfNb=1
SH.FSMC_D8_DA8.0=FSMC_D8,16b-d1
SH.FSMC_D8_DA8.ConfNb=1
SH.FSMC_D9_DA9.0=FSMC_D9,16b-d1
SH.FSMC_D9_DA9.ConfNb=1
SH.FSMC_NOE.0=FSMC_NOE,Lcd1
SH.FSMC_NOE.ConfNb=1
SH.FSMC_NWE.0=FSMC_NWE,Lcd1
SH.FSMC_NWE.ConfNb=1
SH.GPXTI11.0=ADC1_EXTI11,External-Trigger-for-Regular-conversion
SH.GPXTI11.1=ADC2_EXTI11,External-Trigger-for-Regular-conversion
SH.GPXTI11.ConfNb=2
SH.GPXTI9.0=DAC_EXTI9,External-Trigger
SH.GPXTI9.ConfNb=1
SH.S_TIM5_CH4.0=TIM5_CH4,Input_Capture4_from_TI4
SH.S_TIM5_CH4.ConfNb=1
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0.DSPOoLibraryJjLibrary_Checked=true
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0.IPParameters=LibraryCcDSPOoLibraryJjDSPOoLibrary
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0.LibraryCcDSPOoLibraryJjDSPOoLibrary=true
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0_SwParameter=LibraryCcDSPOoLibraryJjDSPOoLibrary\:true;
TIM5.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM5.IPParameters=Channel-Input_Capture4_from_TI4
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.4.0_1.4.0.Mode=DSPOoLibraryJjLibrary
VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.4.0_1.4.0.Signal=STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.4.0_1.4.0
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
